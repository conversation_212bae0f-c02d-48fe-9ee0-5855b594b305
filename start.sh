#!/bin/bash

# Nadjib WordPress Project Startup Script

echo "🚀 Starting Nadjib WordPress Project..."

# Create log directories if they don't exist
mkdir -p logs/nginx logs/php

# Set proper permissions for WordPress files
echo "📁 Setting file permissions..."
sudo chown -R $USER:$USER app/public
find app/public -type d -exec chmod 755 {} \;
find app/public -type f -exec chmod 644 {} \;

# Check if MariaDB is running
echo "🗄️  Checking MariaDB status..."
if ! systemctl is-active --quiet mariadb; then
    echo "Starting MariaDB..."
    sudo systemctl start mariadb
fi

# Create database if it doesn't exist
echo "📊 Setting up database..."
mysql -u root -e "CREATE DATABASE IF NOT EXISTS local;" 2>/dev/null || {
    echo "⚠️  Database setup failed. You may need to configure MariaDB first."
    echo "Run: sudo mysql_secure_installation"
}

# Update wp-config.php for local database
echo "⚙️  Updating WordPress configuration..."
sed -i "s/define( 'DB_HOST', 'mysql' );/define( 'DB_HOST', 'localhost' );/" app/public/wp-config.php

# Start PHP built-in server
echo "🌐 Starting PHP development server..."
cd app/public
php -S localhost:8080 -t . > ../../logs/php/php-server.log 2>&1 &
PHP_PID=$!
cd ../..

# Save PID for stopping later
echo $PHP_PID > .php-server.pid

# Wait for server to start
echo "⏳ Waiting for server to start..."
sleep 3

# Check if server is running
if kill -0 $PHP_PID 2>/dev/null; then
    echo ""
    echo "✅ Project started successfully!"
    echo ""
    echo "🌐 Access your WordPress site at: http://localhost:8080"
    echo "🗄️  Database: MariaDB on localhost:3306"
    echo ""
    echo "📊 Database credentials:"
    echo "   Host: localhost"
    echo "   Database: local"
    echo "   Username: root"
    echo "   Password: (empty - default MariaDB setup)"
    echo ""
    echo "🛠️  Useful commands:"
    echo "   ./stop.sh          - Stop all services"
    echo "   ./logs.sh          - View PHP server logs"
    echo "   ./restart.sh       - Restart services"
    echo ""
    echo "📝 Note: This is using PHP's built-in server for development."
    echo "   For production, use the Docker setup with: docker-compose up -d"
else
    echo "❌ Failed to start PHP server"
    exit 1
fi
