{"521223": {"title": "Get More Leads", "content": "Form abandonment is one of the biggest challenges you need to tackle to grow your business online.\r\n\r\nUsing the Form Abandonment Addon, you can save partial form submissions and follow up with those interested prospects with an abandonment email, even if they didn’t complete the entire form. This is a smart way to invite them back to complete the form so they don’t forget about you.", "button": "LEARN MORE", "url": "https://wpforms.com/addons/form-abandonment-addon/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521223", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521223}, "521225": {"title": "Give Your Forms a Fresh Look", "content": "By default, your forms display fields in one column. With multi-column layout, each field has a layout setting that lets you customize how your fields are displayed.\r\n\r\nFor instance, with the multiple choice field, you can select 1, 2, or 3 columns to display your choices inside of.", "button": "LEARN MORE", "url": " https://wpforms.com/docs/how-to-create-multi-column-form-layouts-in-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521225", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521225}, "521230": {"title": "Display a Message After Users Submit", "content": "With the conditional form confirmations feature you can do things like add a Thank You note after a customer makes a purchase or donates to your cause, then provide them with next steps.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-create-conditional-form-confirmations/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521230", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521230}, "520212": {"title": "Improve Your Form Completion Rates", "content": "<p>Conversational Forms by WPForms is the first true interactive form layout built exclusively for WordPress that is guaranteed to boost form completion and your overall form conversions.</p>\r\n\r\n<p>Instead of asking all twenty questions at once, Conversational Forms takes the <strong>one question at a time approach</strong> similar to a face-to-face conversation.</p>", "button": "LEARN MORE", "url": "https://wpforms.com/addons/conversational-forms-addon/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=520212", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 520212}, "521237": {"title": "Boost Form Completion Rates", "content": "Sometimes, your forms need to collect a lot of information.\r\n\r\nIn this case, use the easy drag and drop builder to break long forms into multi-page forms and quickly improve user experience.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-create-multi-page-forms-in-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521237", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521237}, "521239": {"title": "Customize Your Form Progress Bar", "content": "Change the look of your Multi-Page Forms by customizing the Progress Indicator, also known as Breadcrumbs.\r\n\r\nWPForms offers 3 different types of breadcrumb layouts for you to choose from for your forms. Plus, you can even change the color to keep the branding of your website consistent.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-create-multi-page-forms-in-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521239", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521239}, "521246": {"title": "Supercharge Your Forms", "content": "You can do some truly amazing things with the power of Zapier + WPForms!\r\n\r\nZapier is a connector service that helps you integrate your forms to 1,400+ apps so you can get more done, faster.", "button": "LEARN MORE", "url": "https://wpforms.com/addons/zapier-addon/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521246", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521246}, "521249": {"title": "Create Distraction-Free Landing Pages", "content": "One of the biggest reasons why people abandon your website without filling out your form is distraction.\r\n\r\nUse the Form Pages Addon to create completely customized and distraction-free landing pages to boost your conversions.", "button": "LEARN MORE", "url": "https://wpforms.com/form-pages-demo/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521249", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521249}, "521259": {"title": "Use Ready-To-Go Forms", "content": "With just one click, you can import a form demo template from nearly every niche and industry.\r\n\r\nTry using the Form Templates Pack Addon as a quick and simple hack to set up an already fully functioning and ready-to-go form today.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-install-and-use-the-form-templates-pack-addon/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521259", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521259}, "521261": {"title": "Make Your Forms Private", "content": "Want to increase the overall quality of your form submissions? You can set up a password using the Form Locker Addon to make sure only visitors with that password can submit to your form.\r\n\r\nYou may want to do this for a lot of reasons, including collecting reviews, success stories, or if you’re accepting guest posts on your blog.", "button": "LEARN MORE", "url": "https://wpforms.com/how-to-password-protect-wordpress-forms-step-by-step/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521261", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521261}, "521266": {"title": "Add Images to Your Fields", "content": "If you’d like to make your forms super visual, consider adding images to your forms. A great idea for order forms.\r\n\r\nWPForms makes it simple to add images to your Checkboxes, Multiple Choice, and items fields so your users can see the option or item you’ve listed. That way, they can make sure they are selecting exactly what they want.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-add-image-choices-to-fields/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521266", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521266}, "521269": {"title": "Find Where Your Form Users Come From", "content": "You can add a Smart Tag to a Hidden Field on your form to unlock hidden user data. Smart Tags can save you time by allowing you to include useful details in your forms.\r\n\r\nFor instance, by adding the {url_referer} Smart Tag, your form will automatically insert the address of the page which referred the user to the page containing the form.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-use-smart-tags-in-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521269#url", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521269}, "521272": {"title": "Close Forms Automatically", "content": "The Form Locker Addon has tons of helpful hacks inside of it, including the ability to close forms at a specific date and time.\r\n\r\nThis is a great tool for things like job applications or any other time-sensitive forms.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-install-and-use-the-form-locker-addon-in-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521272", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521272}, "521278": {"title": "Want GDPR Compliant Forms?", "content": "Want to make sure your forms are compliant with the European Union’s General Data Protection Regulation? We've got you covered.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-create-gdpr-compliant-forms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521278", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521278}, "521282": {"title": "Terms of Service or Privacy Policy Too Long?", "content": "You can make a scrollable Terms of Service easily! With rising privacy concerns, and the fact that privacy laws are getting more strict, we want to make it easy for you to comply and let your site visitors know their data is protected.\r\n\r\nWith a scrollable Terms of Service, you can make sure that adding your site’s privacy policy or Terms of Service doesn’t take away from the visual appeal of your form.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-add-a-terms-of-service-checkbox-to-a-form/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521282", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521282}, "521287": {"title": "Prevent Spam", "content": "We know spam is a giant issue for contact forms. So, you can use the custom CAPTCHA feature to protect your forms from spam. The Custom Capt<PERSON> lets you set your own questions and answers to show your visitors.\r\n\r\nOr, you can add random math questions to make sure a user is a real human. No one wants fake form submissions!", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-install-and-use-custom-captcha-addon-in-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521287", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521287}, "521289": {"title": "Gather More Reliable Data", "content": "Worried people are picking specific choices on your multiple choice fields  just because one is at the top? Maybe you’ve noticed a pattern where one answer is getting picked a lot.\r\n\r\nThis could happen because of something called Order Bias. So we made it easy to avoid any sort of answer bias by using randomization.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-randomize-checkbox-and-multiple-choice-options/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521289", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521289}, "521293": {"title": "Avoid Multiple Entries", "content": "With the Form Locker <PERSON>, you can limit one entry per person on your forms and avoid duplicate submissions.\r\n\r\nThis can be really useful for scholarship applications, giveaways, and more.", "button": "LEARN MORE", "url": "https://wpforms.com/how-to-limit-the-number-of-wordpress-form-entries/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521293", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521293}, "521295": {"title": "Re-Use Your Forms", "content": "Love the way one of your forms works, looks, and converts and want to re-use it on another website you own?\r\n\r\nWPForms lets you do this with the Form Export feature so you don't have to start from scratch to build a form you already know you love.\r\n\r\n&nbsp;", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-import-and-export-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521295#export-forms", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521295}, "521300": {"title": "Create Forms Fast", "content": "Want to add a bunch of choices to your Multiple Choice fields all at once? The Bulk Add option lets you quickly add a long list of items as options in your Multiple Choice fields.\r\n\r\nThis works great if you’ve got a long list of products or departments and you’re asking your visitor to select just one.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-bulk-add-choices-for-multiple-choice-checkbox-and-dropdown-fields/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521300", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521300}, "521310": {"title": "Need More Content for Your Blog?", "content": "Accepting guest post submissions is a great way to consistently get fresh content for your site.\r\n\r\nThe WPForms Post Submissions Addon makes it easy to collect user-submitted content in WordPress without users ever having to log in to your website’s admin area.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-install-and-use-the-post-submissions-addon-in-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521310", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521310}, "521313": {"title": "Allow WordPress Users to Register Themselves", "content": "If you want to allow user registration on your WordPress site, you can quickly do this by creating a user registration form in WordPress that allows users to fill in the necessary information themselves.\r\n\r\nThe User Registration Addon lets you set this up in just minutes.", "button": "LEARN MORE", "url": "https://wpforms.com/how-to-create-a-user-registration-form-in-wordpress/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521313", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521313}, "521316": {"title": "Get More Mobile Leads", "content": "All forms created with WPForms are completely responsive and mobile-friendly. But did you know that you can easily add a field for a signature with the WPForms Signature Addon?\r\n\r\nBest of all, the Signature Addon is mobile-friendly, meaning you’ll get more form completions, less form abandonment, and be able to your online business even faster.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-install-and-use-the-signature-addon-in-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521316", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521316}, "521324": {"title": "Get Paid in Multiple Ways", "content": "You can use both the Stripe Addon and the PayPal Standard Addon to collect payments. But did you know that you can set up your forms to let your website visitors pick from <strong>either</strong> payment, right on your form?\r\n\r\nWith Conditional Logic, you can give your visitors more ways to pay you so you can get more money from your site.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-allow-users-to-choose-a-payment-method-on-your-form/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521324", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521324}, "521328": {"title": "Boost Your Website Income", "content": "Setting up Recurring Payments with Conditional Logic + the Stripe Addon can help you earn even more.\r\n\r\nLet visitors choose between One Time or Recurring Payments.\r\n\r\nMeaning more money for you and your small business, time and again.", "button": "LEARN MORE", "url": "https://wpforms.com/how-to-accept-recurring-payments-on-your-wordpress-forms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521328", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521328}, "521330": {"title": "See Where Your Leads Are Coming From", "content": "You can activate Geolocation to learn more about your audience. Your user’s country, state, and city information will be stored along with their submission.\r\n\r\nThe more you know about your website visitors, the easier it is to turn more of them into customers with added insight into where they live.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-install-and-use-the-geolocation-addon-with-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521330", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521330}, "521332": {"title": "Create Connections with Your Audience", "content": "Do you see your logo at the top of this email?\r\n\r\nA simple but super smart way to make your services stand out is by using branded email notifications. You can add your logo or any image you choose to display on your email notifications.\r\n\r\nThis way, when a customer or anyone on your team gets an email notification from your forms, they’ll see the image you want them to see. You can help build trust and a relationship right inside your email notification.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-add-custom-header-image-in-your-wpforms-email-template/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521332", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521332}, "521335": {"title": "Get to Know Your Customers", "content": "Did you know you can use NPS Surveys to learn about your visitors? Net Promoter Score Surveys can give you valuable visual insights into what’s happening with your business based on customer perception of your brand.\r\n\r\nNPS Surveys are used by some of the biggest brands around. They help you to better gauge customer loyalty and make your business more successful.", "button": "LEARN MORE", "url": "https://wpforms.com/how-to-create-a-net-promoter-score-nps-survey-in-wordpress/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521335", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521335}, "521337": {"title": "Organize Events", "content": "Are you planning a party or event? Then you should definitely be using a WPForms RSVP form. It’s an easy way to grow your business online while also finding out who is attending your event and who isn't.\r\n\r\nAnd not to mention, making the RSVP process easy for your guests makes it easier for them to respond. You’ll get a higher response rate by sharing a link to confirm rather than asking for their replies via snail mail.", "button": "LEARN MORE", "url": "https://wpforms.com/how-to-create-an-rsvp-form-in-wordpress/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521337", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521337}, "521339": {"title": "Collect More Leads", "content": "With the Offline Forms Addon, you’ll never lose data or leads again just because someone lost their internet connection and gave up on sending you their information.  The Offline Forms Addon allows your users to save their entered data offline. Then, they can submit it when their internet connection is restored.\r\n\r\nThis could be super helpful for you to use yourself, too. For example, if you’re at a trade show collecting leads in person and the event has a spotty Wi-Fi connection.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-install-and-set-up-the-offline-forms-addon/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521339", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521339}, "521175": {"title": "Improve Your Form Questions", "content": "<span style=\"font-weight: 400;\">You can easily show or hide something to a website visitor on your forms once they select something that triggers this with Smart Conditional Logic. </span>\r\n\r\n<span style=\"font-weight: 400;\">It’s easy to customize forms to have fields automatically show or hide other fields depending on a user’s selection.</span>", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-use-conditional-logic-with-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521175", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521175}, "527323": {"title": "Create Forms That Don't Reload When Submitted", "content": "Keeping a page from refreshing after someone submits on a form makes for an excellent user experience and can even help you get more leads. Ajax-enabled forms are becoming a must-have for websites of all sizes.", "button": "LEARN MORE", "url": "https://wpforms.com/wordpress-ajax-contact-form/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=527323", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 527323}, "521212": {"title": "Send Form Notifications to the Right People", "content": "With Conditional Notifications, you can send out different emails to different people / departments depending on what a user selects on your form.\r\n\r\nFor example, if a user is submitting a complaint and selects that they had an issue with billing, you could have an email go out to a billing coordinator from your team.", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-create-conditional-form-notifications-in-wpforms/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521212", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521212}, "521214": {"title": "Redirect Users After They Submit", "content": "Did you know you can show visitors different web pages based on which form they submit on your website?\r\n\r\nYou can apply conditional rules to trigger different things to take place when someone submits to your form!", "button": "LEARN MORE", "url": "https://wpforms.com/docs/how-to-create-conditional-form-confirmations/?utm_source=WordPress&utm_medium=email&utm_campaign=Email+Summaries&utm_content=521214", "type": ["agency", "basic", "elite", "lite", "plus", "pro", "ultimate"], "id": 521214}}