.astra-sites-custom-cta-wrap {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 8px;
	background-color: transparent;
	background-image: linear-gradient( 342deg, #4a00e0 0%, #8e2de2 100% );
	transition: all 0.4s ease-in-out;
	text-align: center;
	margin-top: 0;
	clear: both;
	opacity: 0;
	visibility: hidden;
	margin-left: 160px;
	z-index: 10;
}
.astra-sites-custom-cta-wrap.show {
	opacity: 1;
	visibility: visible;
}

.astra-sites-cta-title {
	color: #ffffff;
	font-size: 17px;
	line-height: 1.4em;
	vertical-align: middle;
}

.astra-sites-cta-btn {
	display: inline-block;
}

.astra-sites-cta-btn a {
	font-size: 14px;
	font-weight: 600;
	color: #181723;
	background-color: #ffc300;
	border-radius: 4px;
	padding: 09px 30px;
	line-height: 1.5em;
	transition: none;
	display: inline-block;
	margin-left: 4rem;
	text-decoration: none;
}
.astra-sites-cta-btn a:hover {
	background-color: #ffb000;
}
.astra-previewing-single-pages .astra-sites-custom-cta-wrap {
	visibility: hidden;
}

@media screen and ( max-width: 768px ) {
	.astra-sites-custom-cta-wrap {
		margin-left: 0;
	}
	.astra-sites-cta-title {
		display: block;
		margin-bottom: 10px;
	}
	.astra-sites-cta-btn a {
		font-size: 14px;
		padding: 8px 28px;
		margin-left: unset;
	}
}
