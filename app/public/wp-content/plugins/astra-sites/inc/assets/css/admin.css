@font-face {
	font-family: "icomoon";
	src: url( "../fonts/astra-sites.eot?9i8jex" );
	src: url( "../fonts/astra-sites.eot?9i8jex#iefix" ) format( "embedded-opentype" ), url( "../fonts/astra-sites.ttf?9i8jex" ) format( "truetype" ), url( "../fonts/astra-sites.woff?9i8jex" ) format( "woff" ), url( "../fonts/astra-sites.svg?9i8jex#icomoon" ) format( "svg" );
	font-weight: normal;
	font-style: normal;
}

[class^="ast-icon-"],
[class*=" ast-icon-"] {
	/* use !important to prevent issues with browser extensions that change fonts */
	font-family: "icomoon" !important;
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.ast-icon-search:before {
	content: "\e900";
}
.ast-icon-heart:before {
	content: "\e901";
}
.ast-icon-refresh:before {
	content: "\e902";
}
.ast-icon-chevron-left:before {
	content: "\e904";
}
.ast-icon-file:before {
	content: "\e903";
}
.ast-icon-layers:before {
	content: "\e905";
}

.favorite-filters-wrap a {
	font-size: 1rem;
}

.wrap .status,
.wrap .site-type {
	position: absolute;
	z-index: 1;
	text-transform: uppercase;
	color: #fff;
	top: 15px;
	right: 16px;
	font-size: 10px;
	letter-spacing: 0.7px;
	font-weight: 500;
	padding: 0.2em 0.7em;
	border-radius: 4px;
}

.astra-sites-autocomplete-result {
	position: absolute;
	top: 46px !important;
	left: 0;
	right: 0;
	bottom: 0;
}

.astra-sites-autocomplete-result .ui-autocomplete {
	text-align: left;
	left: 0 !important;
	right: 0 !important;
	width: calc( 100% - 2px ) !important;
	z-index: 999999;
	top: 0 !important;
}

.ast-white-label-flag {
	opacity: 0;
	visibility: hidden;
}

.single-site-footer .dashicons-editor-help {
	cursor: pointer;
	display: inline-block;
}

.preview-page-from-search-result .astra-sites-import-plugins .dashicons,
.astra-sites-page-import-popup .astra-sites-import-plugins .dashicons {
	display: none;
}
.ast-sites-ps-msg {
	bottom: 10px;
	position: absolute;
	color: #777;
	margin-right: 1em;
}
#astra-sites__category-filter {
	display: inline-block;
}
#astra-sites__category-filter .astra-sites__category-filter-anchor {
	height: 44px;
	position: relative;
	cursor: pointer;
	display: flex;
	padding: 0 12px 0 12px;
	border: 0;
	width: 135px;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
	border-radius: 6px 0 0 6px;
}
#astra-sites__category-filter .astra-sites__category-filter-anchor:after {
	position: absolute;
	content: "";
	border-left: 2px solid #555;
	border-top: 2px solid #555;
	padding: 3px;
	right: 14px;
	top: 38%;
	-moz-transform: rotate( -135deg );
	-ms-transform: rotate( -135deg );
	-o-transform: rotate( -135deg );
	-webkit-transform: rotate( -135deg );
	transform: rotate( -135deg );
}
#astra-sites__category-filter .ast-sites__filter-wrap-checkbox.first-wrap {
	padding-top: 15px;
	margin-top: 15px;
	border-top: 1px solid #eee;
}
#astra-sites__category-filter ul.astra-sites__category-filter-items {
	display: none;
	margin: 0;
	position: absolute;
	width: 98px;
	top: 40px;
	box-sizing: content-box;
	border: 1px solid #e4e4e4;
	margin-top: 10px;
	min-width: 135px;
	background-color: #fff;
	border-radius: 6px;
	box-shadow: 0 3px 6px rgba( 0, 0, 0, 0.1 );
	padding: 15px;
	-webkit-transform-origin: 50% 0;
	transform-origin: 50% 0;
	-webkit-transition: all 0.2s ease;
	transition: all 0.2s ease;
}
#astra-sites__category-filter li.ast-sites__filter-wrap.category-active:before {
	content: "✓";
	color: #1e8cbe;
	position: absolute;
	left: -15px;
	font-size: 15px;
}

.astra-sites__category-filter-items label {
	vertical-align: top;
}
#astra-sites__category-filter ul.astra-sites__category-filter-items li {
	list-style: none;
	margin-bottom: 8px;
	margin-left: 14px;
	cursor: pointer;
	position: relative;
	font-weight: 500;
}
#astra-sites__category-filter ul.astra-sites__category-filter-items li.ast-sites__filter-wrap-checkbox {
	margin-left: 0;
}
#astra-sites .astra-theme:hover .inner {
	transform: translate( 0, -1px );
	box-shadow: 0 6px 14px 0 rgba( 0, 0, 0, 0.12 );
}
#astra-sites .astra-theme .inner {
	position: relative;
	border-radius: 2px;
	overflow: initial;
	border: 1px solid #ddd;
	transition: all 0.4s;
}
#single-pages .inner {
	border: 1px solid #e2e2e2;
	transition: all 250ms cubic-bezier( 0.02, 0.01, 0.47, 1 );
}
#single-pages .current_page .inner,
#single-pages .inner:hover {
	box-shadow: 0 3px 12px rgba( 0, 0, 0, 0.14 );
}
#astra-sites-admin.wrap .status,
#astra-sites-admin.wrap .site-type.premium {
	background: #0073aa;
	border: 2px solid #ffffff;
	top: 17px;
	right: 18px;
}
#astra-sites-admin .filter-links .current {
	border-bottom: none;
	color: #23282d;
}
#wp-filter-search-input {
	font-weight: 400;
	width: 100%;
	opacity: 1;
	height: 46px;
	background: #fff;
	line-height: 32px;
	border-radius: 0 6px 6px 0;
	font-size: 13px;
	padding: 5px 20px;
	border: 1px solid #dddddd;
	border-left: 1px solid #dddddd;
	box-shadow: none;
}

#astra-sites-filters .ui-widget.ui-widget-content {
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	border-bottom-left-radius: 6px;
	border-bottom-right-radius: 6px;
	border: 1px solid rgba( 32, 33, 36, 0.18 );
	border-top: none;
	overflow: hidden;
	box-shadow: 0 3px 4px 0 rgba( 32, 33, 36, 0.18 );
	background: transparent;
	padding-top: 10px;
	padding-bottom: 10px;
	background: #fff;
}

#astra-sites-filters .ui-autocomplete li,
#astra-sites-filters .ui-autocomplete li:focus,
#astra-sites-filters .ui-autocomplete li:hover {
	outline: none;
}
#astra-sites-filters .ui-autocomplete li {
	padding: 3px 20px;
	text-transform: capitalize;
}
#astra-sites-filters .ui-autocomplete li:hover {
	background-color: #eeeeee;
}
.appearance_page_starter-templates .wp-filter .search-form.searching #astra-sites__category-filter {
	border-bottom-left-radius: 0;
}
.searching #wp-filter-search-input {
	border-bottom-right-radius: 0;
}

#wp-filter-search-input.has-input + .search-icon {
	display: none;
}
.appearance_page_starter-templates .search-icon {
	position: absolute;
	top: 0;
	right: 0;
	color: #c3c3c3;
	padding: 15px 19px 12px 12px;
}
.appearance_page_starter-templates .search-icon:before {
	vertical-align: middle;
}
#astra-sites-admin .filter-links li > a {
	border-bottom: none;
	font-size: 12px;
	padding: 2px 0;
	text-transform: uppercase;
	letter-spacing: 0.3px;
	font-weight: 500;
}
#astra-sites-admin .filter-links li > a:focus {
	box-shadow: none;
}
#astra-sites-admin .theme-actions {
	box-shadow: none;
	left: 0;
	border-left: none;
	background: #fafafa;
	padding: 0;
	margin-right: 20%;
}
.searching .pages-count,
.searching .page-title {
	display: block;
}
.searching .no-pages .theme-name {
	min-height: 36px;
}
#astra-sites-admin .theme-browser .theme .theme-name {
	height: auto;
	width: 90%;
	align-self: center;
	box-shadow: none;
	background: none;
	padding: 14px 15px;
	font-size: 14px;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
#astra-sites-admin .theme-id-container {
	background: #fff;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	border-bottom-left-radius: 2px;
	border-bottom-right-radius: 2px;
	border-top: 1px solid #eeeeee;
}
#astra-sites-admin .sites-page-count {
	color: #888;
	font-size: 12px;
	display: block;
	margin-top: 5px;
}
#astra-sites-admin .theme-action-wrap {
	width: 100%;
	padding: 8px 15px;
}
.filters-wrap-page-categories {
	margin-right: 10px;
}
#astra-sites-admin .favorite-filters-wrap .filter-links a {
	border: none;
}
.favorite-filters-wrap .current .dashicons {
	color: #c34444;
}
.favorite-filters-wrap .dashicons {
	color: #71777c;
	font-size: 18px;
	line-height: 18px;
	width: 17px;
	height: 17px;
}
.theme-browser .theme:hover,
.theme-browser .theme:focus {
	cursor: initial;
}
.theme-browser .theme .inner:hover,
.theme-browser .theme .inner:focus {
	cursor: pointer;
}

.searching .theme-browser .theme .inner:hover .type {
	opacity: 1;
	visibility: visible;
}

.astra-sites-sync-library-button.updating-message {
	animation: rotation 2s infinite linear;
}

.astra-sites-sync-library,
.favorite-filters-title {
	width: 10%;
	padding: 15px 5px;
	align-self: center;
	justify-content: center;
	text-align: center;
	margin-left: 5px;
}
.astra-sites-sync-library a,
.favorite-filters-title a {
	color: #666;
	cursor: pointer;
}

.favorite-action-wrap {
	padding-right: 15px;
	padding-left: 10px;
}

.favorite-action-wrap i {
	color: #666666;
	height: 18px;
	width: 18px;
	font-size: 16px;
	line-height: 1.5;
	transition-property: border, background, color;
	transition-duration: 0.05s;
	transition-timing-function: ease-in-out;
}
.theme-id-container .ast-icon-star {
	padding: 0 15px 0 0;
}
.wrap .status {
	left: -0.5em;
}
.wrap .status.publish,
.wrap .site-type.free {
	display: none;
}

.astra-sites-activate-license .astra-sites-import-content,
.astra-sites-get-agency-bundle .astra-sites-import-content {
	height: auto;
}
.astra-sites-import-content {
	display: block;
	padding: 10px 35px 20px 35px;
	background: #f1f3f5;
	height: 200px;
	overflow: auto;
	position: relative;
}

.astra-sites-activate-license .astra-sites-import-content {
	padding: 20px 35px 30px 35px;
}

#astra-sites-skip-and-import-notice-update-available ul {
	list-style-type: disc;
	margin-left: 15px;
}

.astra-sites-import-content .install-theme-info {
	display: block;
	padding: 0;
}

.install-theme-info .site-type {
	display: none;
}

.theme {
	position: relative;
}
.wrap .astra-sites-preview .site-type.premium {
	display: block;
	display: none;
	position: relative;
	margin: 0.5em 0em 1em 0em;
	top: 0;
	left: 0;
	text-align: center;
}

.theme-details-read-more.open {
	margin: 0.5em 0 0 0;
}

.astra-sites-preview .theme-screenshot {
	width: 100%;
}

.install-theme-info .site-type.premium {
	display: none;
}

/**
 * Required Plugins
 */
.required-plugins.loading {
	text-align: center;
}
.required-plugins button {
	float: right;
}
.required-plugins .plugin-card {
	float: none;
	width: 100%;
	border: none;
	margin: 0 0 0.8em 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	transition: background ease 0.8s;
}
.required-plugins .plugin-card.plugin-card-update-failed {
	flex-wrap: wrap;
}
.required-plugins .spinner {
	float: none;
	margin: 0;
}

.expanded .wp-full-overlay-footer {
	height: 111px;
}

.wp-full-overlay-footer .view-site,
.wp-full-overlay-footer .go-pro,
.wp-full-overlay-footer .astra-demo-import {
	width: 100%;
	text-align: center;
}

.wp-core-ui .wp-full-overlay-footer .button.button-hero,
.wp-core-ui .wp-full-overlay-footer .button-group.button-hero .button {
	padding: 0 10px 1px;
}

.wp-full-overlay-footer .installing:before {
	vertical-align: text-bottom;
}

.astra-sites-advanced-options-wrap h4 {
	margin: 1em 0 0.5em 0;
	padding: 0.5em 0;
	transition: all ease 0.3s;
}

/**
 * Read more link
 */
.wp-core-ui .theme-details-read-more:focus,
.wp-core-ui .theme-details-read-more:hover {
	outline: none;
	box-shadow: none;
}
.wp-core-ui .theme-details-read-more {
	margin: 10px 0;
	display: none;
	text-decoration: none;
}

/**
 * Go pro.
 */
.wp-core-ui .go-pro.button[disabled] {
	background-color: #fcb92c !important;
	color: white !important;
	box-shadow: 1px 0 #eab23a !important;
	text-shadow: 1px 0 #6b4e13 !important;
	border-color: #e2a932 !important;
	cursor: pointer;
}
.wp-core-ui .view-site .dashicons,
.wp-core-ui .go-pro .dashicons {
	font-size: 1rem;
	vertical-align: middle;
}

/**
 * Errors
 */
.plugin-card-update-failed .notice {
	margin-top: 1.5em;
}

.no-themes {
	margin-top: 40px;
}

.no-themes p {
	font-size: 15px;
}

.no-themes .left-margin {
	margin-left: 30px;
}

/**
 *
 */
.astra-sites-preview .wp-full-overlay-sidebar-content {
	bottom: 100px;
}

.footer-import-button-wrap {
	padding: 10px 20px;
}

.footer-import-button-wrap .button {
	margin: 0;
}

.astra-sites-preview.expanded .wp-full-overlay-footer {
	left: initial;
}

/**
 * Menu Page
 */
.astra-sites-title {
	float: left;
	font-size: 20px;
	font-weight: 400;
	margin: 0;
	padding: 0;
	color: #444;
}

#astra-sites-admin #single-pages .site-single .theme-name {
	font-weight: 500;
	font-size: 13px;
	padding: 8px 14px;
	background: #fff;
}

.astra-sites-logo-wrap {
	width: 42px;
	height: 42px;
}

.astra-sites-logo-wrap a,
.astra-sites-logo-wrap img {
	width: inherit;
	height: inherit;
	display: inline-block;
}

/**
 * API Error
 */
.astra-api-error {
	margin: 0 0 0.5em 0;
}

/**
 * Grid
 */
.appearance_page_starter-templates .wp-filter .search-form {
	margin-left: 1em;
	position: relative;
	display: flex;
	border-bottom: 1px solid #ccc;
}
.appearance_page_starter-templates .wp-filter .search-form #astra-sites__category-filter {
	border: 1px solid #dddddd;
	border-right: 0;
	border-radius: 6px 0 0 6px;
}
.appearance_page_starter-templates .wp-filter .search-form input[type="search"] {
	width: 20px;
	font-size: 13px;
	padding: 5px 10px;
	opacity: 0;
	-webkit-transition: width ease 0.3s;
	-moz-transition: width ease 0.3s;
	transition: width ease 0.3s;
}
.filter-count {
	min-width: 3em;
	margin-right: 14px;
}
.astra-site-preview-on {
	overflow: hidden;
}

.filters-wrap {
	display: inline-block;
}
.spinner-wrap {
	text-align: center;
}
.spinner-wrap .spinner {
	float: none;
}
.hide-me {
	display: none !important;
}
#site-pages {
	z-index: 10;
}

.install-theme-info > .notice {
	margin: 5px 0 10px 0;
}

.astra-sites-suggestions:before {
	border: 5px dashed #ccc;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0px;
}

.astra-sites-suggestions {
	min-height: 390px;
	border: none !important;
}

.astra-sites-suggestions a {
	border: none;
	outline: none;
}

.astra-sites-suggestions .inner {
	border: 6px solid #ffffff !important;
	background: #f1f1f1;
	color: #63676b;
	padding: 58% 10% 80% 10%;
	text-align: center;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	cursor: auto;
}

.astra-sites-suggestions .inner a {
	color: #00b9eb;
}

.astra-sites-suggestions p {
	font-size: 1rem;
	margin: 0;
}

.astra-notice {
	margin: 2em 2em 0em 0em;
}

.no-themes .description {
	display: block;
}

/**
 * Responsive Button UI
 */
.astra-sites-preview .wp-full-overlay-footer .devices button.active:before,
.astra-sites-preview .wp-full-overlay-footer .devices button:hover:before {
	color: #0073aa;
}
.astra-sites-preview .wp-full-overlay-footer .devices button:before {
	color: #c1c1c1;
}
.astra-sites-preview .wp-full-overlay-footer .devices button:hover {
	background-color: transparent;
}
.astra-sites-preview .wp-full-overlay-footer .devices button {
	border: none;
}
.astra-sites-preview .wp-full-overlay-footer .devices button:focus,
.astra-sites-preview .wp-full-overlay-footer .devices button.active:hover {
	border-bottom-color: transparent;
	background-color: transparent;
}
.not-click-able {
	pointer-events: none !important;
}
body.page-builder-selected .select-page-builder,
body.loading-content .select-page-builder {
	display: none;
}
.select-page-builder .up-arrow {
	-webkit-transform: rotate( 90deg );
	-moz-transform: rotate( 90deg );
	-ms-transform: rotate( 90deg );
	transform: rotate( 90deg );
	display: inline-block;
	font-size: 1.5em;
	color: #797979;
	vertical-align: middle;
	margin-right: 10px;
	-webkit-transition: all linear 0.6s;
	-moz-transition: all linear 0.6s;
	-ms-transition: all linear 0.6s;
	transition: all linear 0.6s;
	-webkit-animation-duration: 1s;
	animation-duration: 1s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
	-webkit-animation-timing-function: ease-in-out;
	animation-timing-function: ease-in-out;
	animation-iteration-count: infinite;
	-webkit-animation-iteration-count: infinite;
	animation-name: bounce;
	-moz-animation-name: bounce;
}
.select-page-builder {
	margin-left: 6em;
}

.select-page-builder img {
	max-width: 100%;
}

.select-page-builder .note-wrap {
	position: absolute;
	padding: 40px 0;
	margin-left: 170px;
	right: 0;
	left: 0;
}

.select-page-builder h3 {
	margin: 0;
	font-size: 2em;
}
.select-page-builder {
	margin: -20px 0 0 -5px;
	position: absolute;
}
.select-page-builder .note {
	margin-left: 1.5em;
}

@keyframes bounce {
	0%,
	100%,
	20%,
	50%,
	80% {
		-webkit-transform: translateY( -0px ) rotate( 90deg );
		-moz-transform: translateY( -0px ) rotate( 90deg );
		-ms-transform: translateY( -0px ) rotate( 90deg );
		transform: translateY( -0px ) rotate( 90deg );
	}
	40% {
		-webkit-transform: translateY( -2px ) rotate( 90deg );
		-moz-transform: translateY( -2px ) rotate( 90deg );
		-ms-transform: translateY( -2px ) rotate( 90deg );
		transform: translateY( -2px ) rotate( 90deg );
	}
	60% {
		-webkit-transform: translateY( -1px ) rotate( 90deg );
		-moz-transform: translateY( -1px ) rotate( 90deg );
		-ms-transform: translateY( -1px ) rotate( 90deg );
		transform: translateY( -1px ) rotate( 90deg );
	}
}

/**
 * Processing Animation
 */
.astra-demo-import.disabled {
	pointer-events: none;
}
.astra-demo-import.button.updating-message:before,
.astra-demo-import.button.installing:before {
	-webkit-animation: cssAnimation 0.72s ease infinite;
	-moz-animation: cssAnimation 0.72s ease infinite;
	-o-animation: cssAnimation 0.72s ease infinite;
	-ms-animation: cssAnimation 0.72s ease infinite;
	animation: cssAnimation 0.72s ease infinite;
}

@-webkit-keyframes cssAnimation {
	from {
		-webkit-transform: rotate( 0 );
		-moz-transform: rotate( 0 );
		-o-transform: rotate( 0 );
		-ms-transform: rotate( 0 );
		transform: rotate( 0 );
	}
	to {
		-webkit-transform: rotate( 360deg );
		-moz-transform: rotate( 360deg );
		-o-transform: rotate( 360deg );
		-ms-transform: rotate( 360deg );
		transform: rotate( 360deg );
	}
}
@-moz-keyframes cssAnimation {
	from {
		-webkit-transform: rotate( 0 );
		-moz-transform: rotate( 0 );
		-o-transform: rotate( 0 );
		-ms-transform: rotate( 0 );
		transform: rotate( 0 );
	}
	to {
		-webkit-transform: rotate( 360deg );
		-moz-transform: rotate( 360deg );
		-o-transform: rotate( 360deg );
		-ms-transform: rotate( 360deg );
		transform: rotate( 360deg );
	}
}
@-o-keyframes cssAnimation {
	from {
		-webkit-transform: rotate( 0 );
		-moz-transform: rotate( 0 );
		-o-transform: rotate( 0 );
		-ms-transform: rotate( 0 );
		transform: rotate( 0 );
	}
	to {
		-webkit-transform: rotate( 360deg );
		-moz-transform: rotate( 360deg );
		-o-transform: rotate( 360deg );
		-ms-transform: rotate( 360deg );
		transform: rotate( 360deg );
	}
}

#astra-sites-filters .wp-filter {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.astra-pages-title {
	color: #9e9e9e;
	text-decoration: none;
	font-weight: 500;
	position: relative;
}
.single-site-pages-header .count {
	opacity: 0.7;
}

.single-site-pages-header .astra-site-title {
	font-size: 22px;
}
.astra-site-down {
	padding: 1em 2em;
	margin-top: 1em;
}

#astra-sites-menu-page .menu {
	padding: 0;
}

.astra-sites-nav-items a:hover,
.astra-sites-nav-items a:focus {
	outline: none;
	border: none;
	box-shadow: none;
}

.astra-sites-nav-items a {
	background: #f1f1f1;
	color: #4e555d;
	font-size: 14px;
	text-decoration: none;
	padding: 21px 20px 24px 43px;
	font-weight: 500;
}

.astra-sites-nav-items a:before {
	content: "\f105";
}

#astra-sites-filters .search-form {
	font-weight: normal;
	border: none;
	margin: 0;
	border-radius: 6px;
	display: flex;
	align-items: center;
	height: 46px;
}

.astra-sites-nav-items a:before {
	font-family: dashicons;
	display: inline-block;
	line-height: 1;
	font-weight: 400;
	font-style: normal;
	speak: none;
	text-decoration: inherit;
	text-transform: none;
	text-rendering: auto;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	font-size: 17px;
	vertical-align: top;
	text-align: center;
	transition: color 0.1s ease-in;
	position: absolute;
	margin-left: -22px;
	font-weight: normal;
}
.nav-tab-wrapper .button {
	height: 30px;
}
.back-to-layout:before {
	background: #fff;
	color: #777;
	padding: 3px;
	border-radius: 100%;
	font-size: 13px;
	border: 1px solid #777;
}

.back-to-layout:hover:before {
	background: #f7f7f7;
	border-color: #0073aa;
	color: #0073aa;
}

.back-to-layout {
	border-right: 1px solid #eeeeee;
	padding: 20px 14px;
	text-align: center;
	cursor: pointer;
	visibility: hidden;
	opacity: 0;
	font-size: 24px;
	line-height: normal;
}

.back-to-layout:hover {
	background-color: #f7f7f7;
}

.astra-sites-no-search-result .back-to-layout,
.astra-sites-showing-favorites .back-to-layout,
.astra-previewing-single-pages .back-to-layout {
	visibility: visible;
	opacity: 1;
}
.logo {
	cursor: pointer;
}
#astra-sites-menu-page .nav-tab-wrapper .logo {
	border-right: 1px solid #dddddd;
	padding: 13px 20px 14px 20px;
}
#astra-sites-menu-page .nav-tab-wrapper .logo,
#astra-sites-menu-page .form {
	display: flex;
	align-items: center;
}

#astra-sites-menu-page .nav-tab-wrapper {
	display: flex;
	justify-content: space-between;
	border: none;
	background: #fff;
	padding: 0;
	align-items: center;
	box-shadow: 25px 0 30px rgba( 28, 39, 60, 0.09 );
	border-bottom: 1px solid #dddddd;
	z-index: 88;
}
.theme-name .title,
.theme-name .type {
	transition: all 0.4s linear;
}
.searching .theme-name .type {
	padding: 4px 8px;
}
.theme-name .type {
	font-size: 12px;
	opacity: 0;
	visibility: hidden;
	color: #555;
	border: 1px solid #cccccc;
	background: #f7f7f7;
	vertical-align: top;
	border-radius: 2px;
	font-weight: normal;
}
.theme-name .type .dashicons {
	font-size: 16px;
}
.favorite-filters-wrap {
	border-right: 1px solid #dddddd;
	padding: 10px;
}

.header-actions a {
	text-decoration: none;
	font-size: 18px;
	line-height: 1;
	height: 15px;
	width: 18px;
}
.header-actions .filter-links li > a:hover,
.header-actions .filter-links li > a:focus {
	outline: none;
}
.filter-links li > .astra-sites-show-favorite-button.current:hover,
.filter-links li > .astra-sites-show-favorite-button.current,
.filter-links li > .astra-sites-show-favorite-button:hover,
.filter-links li > .astra-sites-show-favorite-button:focus,
.astra-sites-show-favorite-button.active {
	color: transparent;
}
.filter-links li > .astra-sites-show-favorite-button.current:hover .ast-icon-heart,
.filter-links li > .astra-sites-show-favorite-button.current .ast-icon-heart,
.filter-links li > .astra-sites-show-favorite-button:hover .ast-icon-heart,
.astra-sites-show-favorite-button.active .ast-icon-heart {
	color: #c34444;
}

.ast-icon-heart {
	color: #666;
}
.header-actions a:focus {
	box-shadow: none;
}
#astra-sites-filters {
	flex: 1;
}

#astra-sites-filters .wp-filter {
	margin-top: 0;
	justify-content: center;
	box-shadow: none;
	border: none;
	margin-bottom: 0;
	background: transparent;
	padding: 0;
}

.is-favorite .favorite-action-wrap i {
	color: #c34444;
}

.astra-sites-no-sites,
.astra-sites-no-favorites {
	padding: 7em 0;
}

.astra-sites-no-sites h3,
.astra-sites-no-favorites h3 {
	font-size: 1.3rem;
	font-weight: normal;
	margin-top: 0;
	text-align: center;
	margin-bottom: 3em;
	color: #666;
}

.astra-sites-no-sites .back-to-layout-button {
	margin-top: 25px;
}

.back-to-layout-button {
	margin-top: 15px;
}

.empty-collection-part {
	width: 220px;
}

.astra-sites-no-sites .arrow-img,
.astra-sites-no-favorites .arrow-img {
	width: 56px;
	margin-bottom: 1em;
	transform: rotate( -18deg );
}

.astra-sites-no-sites .content,
.astra-sites-no-favorites .content {
	display: flex;
	align-items: center;
}

.astra-sites-no-sites .description,
.astra-sites-no-favorites .description {
	margin-left: 3em;
}

.astra-sites-no-sites .inner,
.astra-sites-no-favorites .inner {
	margin: 0 auto;
	width: 610px;
}

/**
 * Welcome Screen
 */
.astra-sites-welcome {
	position: relative;
	max-height: 100vh;
	height: calc( 100vh - 32px );
}
.appearance_page_starter-templates.astra-sites-change-page-builder #wpbody-content {
	padding: 0;
}
.astra-sites-welcome a {
	text-decoration: none;
}
.astra-sites-welcome a:focus,
.astra-sites-welcome a {
	outline: none;
	box-shadow: none;
}
.astra-sites-welcome .header img {
	width: 30px;
}
.astra-sites-welcome .logo {
	display: flex;
	align-items: center;
	cursor: default;
}
.astra-sites-welcome .close {
	position: absolute;
	right: 0;
	top: 0;
	bottom: 0;
	width: 20px;
	text-align: center;
	padding: 15px;
	cursor: pointer;
	border-left: 1px solid #eee;
	color: #aaa;
}

.astra-sites-welcome .close:hover {
	color: #0073aa;
}

.astra-sites-welcome .close .dashicons {
	height: auto;
	width: auto;
	vertical-align: middle;
	font-size: 22px;
}

.astra-sites-welcome .header {
	text-align: left;
	padding: 10px 15px;
	display: flex;
	align-items: center;
	border-bottom: 1px solid #e5e5e5;
	box-shadow: 0 0 8px rgba( 0, 0, 0, 0.1 );
	justify-content: space-between;
	position: relative;
}
.astra-sites-welcome .header .title {
	margin: 0 0 0 10px;
	font-size: 13px;
	text-transform: uppercase;
}
.ast-importing-wrap {
	display: none;
}
.astra-sites-welcome .inner-wrap {
	position: absolute;
	left: 50%;
	top: 40%;
	transform: translate( -50%, -40% );
	border-radius: 2px;
	box-shadow: 0 1px 1px rgba( 0, 0, 0, 0.04 );
	border: 1px solid #e5e5e5;
	overflow: hidden;
}
.astra-sites-welcome .inner {
	vertical-align: middle;
	margin: 0 auto;
	display: inline-block;
	width: 750px;
	background: #fff;
	text-align: center;
}

.astra-sites-welcome h1 {
	margin-top: 0;
	font-size: 2em;
	margin-bottom: 1em;
}

.astra-sites-welcome p {
	font-size: 14px;
}

#astra-sites-welcome-form {
	padding: 6em 4em 2em 4em;
}

.astra-sites-welcome select {
	padding: 5px;
	height: 100%;
}

.astra-sites-welcome .submit {
	text-align: center;
	margin: 0;
	padding: 0;
	margin-left: 0.5em;
}

.astra-sites-welcome .disabled {
	pointer-events: none;
}

.astra-site-page-builder {
	opacity: 0;
	visibility: hidden;
}

.required-plugins-list,
.astra-sites-third-party-required-plugins {
	margin-left: 16px;
	margin-top: 0.5em;
	margin-bottom: 0.5em;
	list-style-type: disc;
}

.astra-sites-tooltip-message {
	margin-left: 24px;
}

.astra-sites-third-party-required-plugins .plugin-card,
.required-plugins-list .plugin-card {
	background: transparent;
	border: none;
	margin: 0;
	line-height: 2;
	float: none;
	width: 100%;
}

.required-plugins-list .spinner {
	float: none;
	margin: 0;
}

.astra-site-import-process-wrap {
	display: flex;
	align-items: center;
	margin-top: -2px;
	z-index: 999999;
	position: relative;
	overflow: hidden;
}

.astra-sites-result-preview .astra-site-import-process-wrap progress {
	background: #eeeeee;
}

.astra-sites-result-preview .astra-site-import-process-wrap progress::-webkit-progress-value {
	background: #0185ba;
}

.astra-site-import-process-wrap progress {
	padding: 0px;
	border: 0 none;
	background: #0085bd;
	border-radius: 5px;
	height: 4px;
	flex: 1;
}

.astra-sites-result-preview .dashicons {
	font-size: 1rem;
	height: auto;
	vertical-align: middle;
}

.astra-site-import-process-wrap progress::-webkit-progress-value {
	background: #00679b;
}

.astra-site-import-process-wrap progress::-webkit-progress-bar {
	background: transparent;
}

.theme-browser .theme .theme-screenshot {
	filter: blur( 0 );
	transition: filter 400ms linear;
}
.theme-browser .theme .site-preview > .theme-screenshot {
	background-position: center top;
	background-size: 100%;
	background-repeat: no-repeat;
	border-top-left-radius: 2px;
	border-top-right-radius: 2px;
	overflow: hidden;
	background-color: #e5e5e5;
}

.astra-sites-tooltip-icon {
	cursor: pointer;
}

.astra-sites-preview .disabled {
	pointer-events: none;
}

.astra-sites-preview input[type="checkbox"].disabled {
	background: #eeeeee;
	opacity: 1;
	color: #fff;
	box-shadow: none;
	border-color: #b4b9be;
}

.astra-sites-tooltip-icon .dashicons {
	color: #757575;
	font-size: 15px;
	vertical-align: middle;
	height: auto;
}
#astra-sites-admin {
	margin-right: 25px;
	margin-left: 25px;
	margin-top: 45px;
}
.astra-previewing-single-pages #wpfooter {
	display: none;
}

.astra-previewing-single-pages #astra-sites-admin {
	margin-top: 58px;
}

#astra-sites-welcome-form-inline select {
	text-align-last: right;
}

#astra-sites-welcome-form-inline option {
	direction: rtl;
}
#astra-sites-admin .filter-links li > a:first-child {
	margin-left: 0;
}
#astra-sites-welcome-form-inline {
	margin-left: 0;
	border-radius: 3px;
	padding: 0px 5px 2px 5px;
	outline: 2px solid transparent;
	outline-offset: 0;
	background: #ffffff;
	color: #72777c;
	font-weight: normal;
	font-size: 13px;
	line-height: 26px;
	height: 28px;
	cursor: pointer;
}

#astra-sites-welcome-form-inline select,
#astra-sites-welcome-form-inline select:focus {
	border: none;
	outline: none;
	box-shadow: none;
	color: #72777c;
}

#astra-sites-menu-page .wp-full-overlay-main:before {
	content: "";
	display: none;
}

.theme-screenshot-wrap {
	overflow: hidden;
	max-height: 300px;
	margin: 15px 0;
	border: 1px solid #ccc;
}

.astra-sites-preview .install-theme-info .theme-screenshot {
	width: 100%;
	border: none;
	margin: 0;
}

#astra-sites-welcome-form .title {
	font-size: 14px;
	font-weight: 500;
	margin-top: 5px;
}

#astra-sites-welcome-form #submit {
	padding: 0em 4rem;
}

.page-builders {
	margin: 35px 0;
}

.page-builders li {
	cursor: pointer;
	display: inline-block;
	padding: 1.2em;
}

.page-builders [type="radio"] {
	position: absolute;
	opacity: 0;
	width: 0;
	height: 0;
}

.page-builders [type="radio"] + img.active,
.page-builders [type="radio"] + img:hover {
	transform: translateY( -1px );
	box-shadow: 0 2px 6px rgba( 0, 115, 170, 0.18 );
	border-color: #0073aa;
}

.page-builders [type="radio"] + img {
	cursor: pointer;
	border: 1px solid #dcdcdc;
	border-radius: 4px;
	transition: all linear 0.2s;
	height: 100px;
	padding: 6px;
}

.astra-demo-import.button.updating-message:before,
.astra-demo-import.button.installing:before {
	vertical-align: text-top;
	margin: 0 5px 0 0;
}

.current-importing-status-wrap hr {
	border-color: #f1f1f1;
}

.astra-sites-result-preview {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	background: transparent;
	overflow-y: auto;
}

.astra-sites-result-preview .button {
	margin-top: 1em;
}

.preview-page-from-search-result #astra-sites-tooltip-plugins-settings,
.astra-sites-page-import-popup #astra-sites-tooltip-plugins-settings {
	display: block !important;
}

.astra-sites-result-preview h3 {
	font-size: 1.3em;
	margin: 0;
}
.astra-sites-result-preview .heading {
	background: #ffffff;
	padding: 16px 35px;
	margin-top: 0;
	box-shadow: 0 0 8px rgba( 0, 0, 0, 0.1 );
	margin-bottom: 10px;
	display: flex;
	justify-content: space-between;
}
.astra-sites-result-preview .close {
	position: absolute;
	right: 0;
	top: 0;
	bottom: 0;
	height: 53px;
	line-height: 53px;
	width: 53px;
	cursor: pointer;
	border-left: 1px solid #eee;
	color: #aaa;
	font-size: 22px;
	opacity: 1;
	visibility: visible;
}
.astra-sites-result-preview .close:hover {
	color: #0073aa;
}
.astra-sites-result-preview .overlay {
	background: rgba( 0, 0, 0, 0.7 );
	filter: alpha( opacity=70 );
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 100050;
}
.astra-sites-result-preview .inner {
	margin: 0 auto;
	width: 660px;
	max-height: 420px;
	-webkit-transform: translate( -50%, -50% );
	-ms-transform: translate( -50%, -50% );
	transform: translate( -50%, -50% );
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	position: fixed;
	background-color: #f1f3f5;
	z-index: 100059;
	text-align: left;
	top: 50%;
	left: 50%;
	-webkit-box-shadow: 0 3px 6px rgba( 0, 0, 0, 0.3 );
	box-shadow: 0 3px 6px rgba( 0, 0, 0, 0.3 );
	border-radius: 2px;
}
.astra-sites-result-preview .button {
	line-height: 40px;
}

#single-pages .astra-theme {
	padding: 0;
}

.astra-sites-import-content > p:first-child,
.ast-importing-wrap > p:first-child {
	margin-top: 0;
}

.current-importing-status {
	background: #fff;
	padding: 1.3em;
}

.current-importing-status ul {
	list-style-type: disc;
	margin: 1em 0em 0em 2em;
}

.appearance_page_starter-templates .rotating {
	height: auto;
	animation: rotation 2s infinite linear;
	width: auto;
	color: #c1c1c1;
}

.agency-ribbons {
	color: #000;
	text-align: center;
	display: flex;
	align-items: center;
	position: absolute;
	z-index: 9;
	top: -7px;
	text-transform: uppercase;
	font-weight: 600;
	font-size: 9.5px;
	letter-spacing: 0.2px;
	right: -7px;
	border-radius: 2px;
	line-height: 15px;
	padding: 4px 7px;
	background-color: #fcaf2a;
	cursor: default;
}

.premium-crown-icon {
	height: auto;
	width: 17px;
	margin-right: 5px;
}

.theme-browser .theme:hover .theme-actions,
.theme-browser .theme.focus .theme-actions,
.theme-browser .theme:focus .theme-actions {
	opacity: 0;
}

.theme-browser .theme .theme-screenshot:after {
	padding-top: 118%;
}

@-webkit-keyframes rotation {
	from {
		-webkit-transform: rotate( 0deg );
	}
	to {
		-webkit-transform: rotate( 359deg );
	}
}
.import-time {
	display: inline-block;
	background: #0185ba;
	color: #fff;
	padding: 2px 10px;
	border-radius: 3px;
	animation: astra-scale 0.5s alternate infinite ease-in;
}

.theme-browser .theme:hover .theme-actions,
.theme-browser .theme.focus .theme-actions,
.theme-browser .theme:focus .theme-actions {
	opacity: 0;
}
@keyframes astra-scale {
	0% {
		transform: scale( 1 );
	}
	100% {
		transform: scale( 1.03 );
	}
}

.current-importing-status p {
	margin: 0;
}

@media ( min-width: 960px ) and ( max-width: 1020px ) {
	#astra-sites-admin .theme-action-wrap {
		padding: 5px;
		width: 100%;
	}
}

@media ( min-width: 781px ) and ( max-width: 920px ) {
	#astra-sites-admin .theme-action-wrap {
		padding: 5px;
		width: 100%;
	}
	.theme-browser .theme .theme-actions button:first-child {
		margin-bottom: 5px;
	}
}

.theme-browser .theme .site-preview > .theme-screenshot.two:hover,
.theme-browser .theme .site-preview > .theme-screenshot.three:hover {
	background-position: center top;
}

.theme-browser .theme .theme-screenshot.two,
.theme-browser .theme .theme-screenshot.three {
	position: absolute;
	top: 0;
	right: 0;
	left: 0px;
}

.theme-browser .theme .theme-screenshot.two {
	width: calc( 100% - 10px );
	opacity: 0.8;
	left: 0px;
	transform: translate( 5px, -5px );
}

.theme-browser .theme .theme-screenshot.three {
	width: calc( 100% - 20px );
	opacity: 0.4;
	transform: translate( 10px, -10px );
}

.theme-browser .theme {
	box-shadow: 0 1px 7px 0 rgba( 0, 0, 0, 0.08 );
	margin: 0 3% 3.6% 0;
	border: none;
}

.single-site {
	display: flex;
}

.single-site-pages {
	overflow-y: auto;
	height: 100vh;
	max-height: calc( 100vh - 280px );
}

.single-site-pages::-webkit-scrollbar {
	width: 5px;
	background-color: #f1f1f1;
}

.single-site-pages::-webkit-scrollbar-thumb {
	border-radius: 0;
	background-color: #d8d8d8;
}

.single-site-pages::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 0 rgba( 0, 0, 0, 0.3 );
	width: 20px;
}

.astra-sites-import-content::-webkit-scrollbar,
.single-site-preview::-webkit-scrollbar {
	width: 5px;
	background-color: #f1f1f1;
}

.astra-sites-import-content::-webkit-scrollbar-thumb,
.single-site-preview::-webkit-scrollbar-thumb {
	border-radius: 0;
	background-color: #d8d8d8;
}

.astra-sites-import-content::-webkit-scrollbar-track,
.single-site-preview::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 0 rgba( 0, 0, 0, 0.3 );
	width: 20px;
}

.single-site-preview {
	margin-right: 4em;
	max-height: calc( 100vh - 280px );
	height: 100vh;
	overflow-y: auto;
	border-radius: 2px;
	box-shadow: 0 0 16px 0 rgba( 0, 0, 0, 0.12 );
}

.single-site-preview img {
	width: 100%;
	vertical-align: middle;
}

#astra-sites-menu-page .nav-tab-wrapper.stick {
	position: fixed;
	left: 0;
	right: 0;
}

.single-site-footer {
	position: fixed;
	padding: 15px 50px 15px 35px;
	bottom: 0;
	background: #fff;
	right: 0;
	left: 0;
	z-index: 88;
	box-shadow: 0 -2px 8px rgb( 0 0 0 / 5% );
}

.site-action-buttons-wrap {
	display: flex;
	justify-content: center;
}

.site-action-buttons-wrap .site-preview-button {
	margin-right: 5px;
}

.single-site-pages-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 0 0 32px 0;
}

.astra-site-title {
	margin: 0;
}

.astra-pages-title-wrap {
	margin: 0 0 30px 0;
	display: flex;
	justify-content: space-between;
}

.single-site-preview-wrap {
	width: 55%;
}

.single-site-pages-wrap {
	width: 45%;
}

@media only screen and ( min-width: 768px ) {
	#site-pages {
		padding: 0 0 0 24px;
	}
}

/**
 * 3 Grid for Pages
 */
@media only screen and ( min-width: 1640px ) {
	.theme-browser #site-pages .theme {
		width: 31.06%;
		margin: 0 3% 3% 0;
	}
}

@media only screen and ( min-width: 1640px ) {
	.theme-browser #site-pages .theme:nth-child( 3n ) {
		margin-right: 0;
	}
}
#single-pages .astra-theme {
	border: none;
}

#single-pages .current_page .inner {
	border-color: #5b9dd9;
	border-radius: 2px;
}
.theme-browser #site-pages .theme {
	margin: 0 3.4% 3.4% 0;
	border-radius: 2px;
	border: none;
	box-shadow: none;
}
.theme-browser #single-pages .site-single .theme-screenshot:after {
	padding-top: 100%;
}

.site-action-buttons-wrap .dashicons {
	vertical-align: middle;
	font-size: 1rem;
	margin-left: 0.2em;
}

.single-site-footer .dashicons-editor-help {
	font-size: 20px;
	color: #555d66;
	margin-left: 10px;
}
.site-action-buttons-right {
	display: flex;
	align-items: center;
}
.astra-sites-result-preview.astra-sites-import-complete .inner {
	overflow: hidden;
	height: auto;
}

.site-action-buttons-wrap .button.button-primary.button-hero {
	box-shadow: none;
}
.wp-core-ui .astra-sites-result-preview .button.button-hero,
.wp-core-ui .astra-sites-result-preview .button.button-hero:focus,
.site-action-buttons-wrap .button.button-hero,
.site-action-buttons-wrap .button.button-hero:focus {
	font-size: 13px;
	height: 34px;
	min-height: 34px;
	line-height: 33px;
	padding: 0 20px;
	box-shadow: none;
}

.astra-hide-site {
	display: none;
}

.astra-sites-no-sites .button.astra-sites-back,
.astra-sites-no-favorites .button.astra-sites-back {
	box-shadow: none;
	height: 30px;
}

.astra-sites-no-sites h3 {
	font-size: 1.3rem;
	font-weight: normal;
	color: #666;
	margin-top: 0;
}

.astra-sites-import-plugins .disabled {
	pointer-events: none;
}
.theme-browser .theme .site-preview > .theme-screenshot.loading,
.theme-browser .theme .theme-screenshot.loading {
	animation-duration: 2s;
	animation-fill-mode: forwards;
	animation-iteration-count: infinite;
	animation-name: image-placeholder;
	animation-timing-function: linear;
	background: #f6f7f8;
	background: linear-gradient( to right, #fafafa 8%, #f4f4f4 38%, #fafafa 54% );
	position: relative;
}
@keyframes image-placeholder {
	0% {
		background-position: -150px 0;
	}
	100% {
		background-position: 150px 0;
	}
}
.searching .theme-browser .theme .theme-screenshot.loading {
	opacity: 0.5;
}
.searching .theme-browser .theme .theme-screenshot {
	filter: blur( 0 );
}
.inner {
	overflow: hidden;
}
.site-import-layout-button.disabled {
	pointer-events: none;
}

.theme-browser .themes {
	clear: both;
	margin: -1%;
}

@media only screen and ( min-width: 768px ) {
	.theme-browser .theme {
		width: 25%;
		padding: 1.1%;
		margin: 0 0 1em 0em;
		box-shadow: none;
	}
}

@media only screen and ( min-width: 1640px ) {
	.theme-browser .theme:nth-child( 3n ) {
		margin-right: 0;
	}
	.theme-browser .theme:nth-child( 4n ) {
		margin-right: 0;
	}
}

#astra-sites-filters .search-form {
	position: relative;
}

.filters-wrap-page-categories {
	position: absolute;
	background: #fff;
	padding: 10px;
	z-index: 999;
	border: 1px solid #ccc;
	border-top: none;
	right: 0;
	left: 0;
	margin: 0;
	visibility: hidden;
	opacity: 0;
}

.filters-wrap-page-categories.show {
	visibility: visible;
	opacity: 1;
}

.filters-wrap-page-categories .filter-links li {
	display: list-item;
}

#astra-sites-admin .filter-links .current {
	border: none;
}

.filters-wrap-page-categories .filter-links {
	display: block;
}

.filters-wrap-page-categories .filter-links li > a {
	display: block;
	margin: 0 0 5px 5px;
}

.page-builder-icon .page-builders {
	position: absolute;
	top: 100%;
	background: #fff;
	z-index: 10;
	margin: 0;
	border: 1px solid #ddd;
	opacity: 0;
	visibility: hidden;
	box-shadow: 0px 9px 21px 0px rgba( 0, 0, 0, 0.2 );
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
	left: -1px;
	right: 0px;
}
.page-builder-icon.active .page-builders {
	opacity: 1;
	visibility: visible;
}
.page-builder-icon .page-builders img {
	height: 17px;
	width: 17px;
	border-radius: 50%;
	margin-right: 10px;
}

.page-builder-icon .page-builders li {
	display: flex;
	padding: 10px 15px;
	margin: 0;
}

.page-builder-icon {
	display: inline-block;
	font-size: 12px;
	padding: 23px 10px;
	width: 164px;
	position: relative;
	cursor: pointer;
}

.page-builder-icon img {
	height: auto;
	width: 22px;
	border-radius: 50%;
	margin-right: 8px;
}

.selected-page-builder {
	display: flex;
	justify-content: center;
	align-items: center;
}

.selected-page-builder .dashicons {
	margin-left: 2px;
}

.page-builder-icon .page-builders .active,
.page-builder-icon .page-builders li:hover {
	background-color: #f7f7f7;
}

.appearance_page_starter-templates .astra-sites-sync-library-message {
	margin-left: 25px;
	margin-right: 25px;
}

.astra-sites-sync-library-message.notice .notice-dismiss:before {
	font-size: 14px;
}

.astra-loading-wrap {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate( -60%, -60% );
}

.ast-retry-text {
	position: absolute;
	left: 50%;
	top: 60%;
	transform: translate( -50%, -50% );
}

.astra-previewing-single-pages #wpbody-content {
	position: fixed;
}

.appearance_page_starter-templates div#setting-error-tgmpa,
.appearance_page_starter-templates .update-nag,
.appearance_page_starter-templates .notice,
.appearance_page_starter-templates div.error {
	display: none;
}

.appearance_page_starter-templates .astra-sites-notice {
	display: block;
	top: 20px;
	position: relative;
}

.astra-previewing-single-pages .astra-sites-menu-page-wrapper > .notice.astra-sites-sync-library-message.success,
.astra-previewing-single-pages .astra-sites-menu-page-wrapper > .notice.astra-sites-sync-library-message.notice-success {
	display: none !important;
}

.astra-previewing-single-pages .astra-sites-menu-page-wrapper > .notice.astra-sites-sync-library-message {
	width: calc( 100% - 250px );
}

.astra-previewing-single-pages.folded #wpbody-content > .notice.astra-sites-sync-library-message {
	width: calc( 100% - 150px );
}

.appearance_page_starter-templates.astra-previewing-single-pages #wpbody-content {
	padding-top: 0;
}

.astra-previewing-single-pages #wpbody-content > .notice.astra-sites-sync-library-message,
.astra-previewing-single-pages #wpbody-content > .notice.astra-sites-sync-library-start-message {
	display: inline-block !important;
	top: 90px;
	right: 0;
	width: calc( 100% - 260px ) !important;
}

.folded.astra-previewing-single-pages #wpbody-content > .notice.astra-sites-sync-library-message,
.folded.astra-previewing-single-pages #wpbody-content > .notice.astra-sites-sync-library-start-message {
	width: calc( 100% - 140px ) !important;
}

.appearance_page_starter-templates #wpcontent {
	padding-left: 0;
}

.appearance_page_starter-templates.auto-fold #wpcontent {
	padding-left: 0;
}

.pages-count,
.page-title {
	font-weight: 300;
	font-size: 13px;
	display: none;
}

.site-pages-not-import-notice {
	clear: both;
}

.astra-loading-icon,
.astra-loading-icon:after {
	border-radius: 50%;
	width: 20px;
	height: 20px;
}
.astra-loading-icon {
	border-top: 3px solid rgb( 192, 192, 192 );
	border-right: 3px solid rgb( 192, 192, 192 );
	border-bottom: 3px solid rgb( 192, 192, 192 );
	border-left: 3px solid #0185ba;
	-webkit-transform: translateZ( 0 );
	-ms-transform: translateZ( 0 );
	transform: translateZ( 0 );
	-webkit-animation: load8 1.1s infinite linear;
	animation: load8 1.1s infinite linear;
}
@-webkit-keyframes load8 {
	0% {
		-webkit-transform: rotate( 0deg );
		transform: rotate( 0deg );
	}
	100% {
		-webkit-transform: rotate( 360deg );
		transform: rotate( 360deg );
	}
}
@keyframes load8 {
	0% {
		-webkit-transform: rotate( 0deg );
		transform: rotate( 0deg );
	}
	100% {
		-webkit-transform: rotate( 360deg );
		transform: rotate( 360deg );
	}
}

.preparing .install-theme-info {
	display: none !important;
}

#astra-sites.temp .placeholder-site .theme-id-container .title {
	background: #f1f1f1;
}

#astra-sites.temp .placeholder-site .theme-id-container .site-title {
	color: transparent;
}

.ast-sites__filter-wrap-checkbox input[type="radio"].active:before {
	content: "";
	border-radius: 50%;
	width: 0.5rem;
	height: 0.5rem;
	margin: 0.1875rem;
	background-color: #1e8cbe;
	line-height: 1.14285714;
	float: left;
	display: inline-block;
	vertical-align: middle;
	speak: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

@media screen and ( min-width: 783px and max-width: 1180px ) {
	#astra-sites-filters .search-form {
		width: 100%;
	}
	#astra-sites .theme {
		width: 50%;
	}

	#astra-sites .theme:nth-child( odd ) {
		margin: 0;
	}
	.theme-browser #site-pages .theme {
		width: 46%;
	}
}
@media screen and ( max-width: 782px ) {
	.ast-sites__filter-wrap-checkbox input[type="radio"] {
		height: 1rem;
		width: 1rem;
	}
	.ast-sites__filter-wrap-checkbox input[type="radio"]:checked:before {
		margin: 0.25rem;
		height: 0.4rem;
		width: 0.4rem;
	}

	.page-builder-title {
		display: none;
	}

	.page-builder-icon {
		width: 100px;
	}

	.page-builder-icon .page-builders {
		width: 140px;
		margin-left: -20px;
	}
	.single-site-footer {
		margin-left: 0;
	}
}
@media only screen and ( max-width: 768px ) {
	.astra-previewing-single-pages #wpbody-content > .notice.astra-sites-sync-library-message,
	.astra-previewing-single-pages #wpbody-content > .notice.astra-sites-sync-library-start-message {
		top: 0;
		width: auto !important;
	}
}
@media only screen and ( max-width: 767px ) {
	.appearance_page_starter-templates #wpbody-content {
		padding-top: 0;
	}

	#astra-sites-menu-page .nav-tab-wrapper .logo {
		padding: 10px;
	}

	.back-to-layout {
		padding: 20px 5px;
	}
}
@media only screen and ( max-width: 1120px ) {
	.theme-browser .theme:nth-child( odd ) {
		margin-right: 0;
	}
}
@media only screen and ( min-width: 780px ) and ( max-width: 1200px ) {
	.theme-browser .theme:nth-child( odd ) {
		margin-right: 0;
	}
	.theme-browser .theme {
		width: 33.33%;
	}
}

@media only screen and ( min-width: 1200px ) {
	#astra-sites-filters .search-form {
		width: 600px;
		margin: 0 auto;
	}
}
@media only screen and ( min-width: 1000px ) and ( max-width: 1200px ) {
	.theme-browser #site-pages .theme:nth-child( 3n ) {
		margin-right: 0;
	}

	.theme-browser #site-pages .theme {
		width: 30.33%;
	}
}

@media only screen and ( min-width: 780px ) and ( max-width: 999px ) {
	.theme-browser #site-pages .theme:nth-child( 2n ) {
		margin-right: 0;
	}

	.theme-browser #site-pages .theme {
		width: 48%;
	}
}

@media only screen and ( max-width: 960px ) {
	#site-pages {
		position: relative;
		margin: 0 70px 0 0;
	}
}
@media only screen and ( max-width: 1200px ) {
	.back-to-layout {
		margin-right: 20px;
	}
}
@media screen and ( max-width: 600px ) {
	#astra-sites-filters .search-form {
		width: auto;
	}
}
@media screen and ( max-width: 782px ) {
	#site-pages {
		margin: 0;
	}
	.theme-browser #site-pages .theme {
		width: 46%;
	}
	.single-site-footer {
		padding: 10px 20px;
	}
	.single-site {
		flex-direction: column;
	}

	.single-site-preview-wrap {
		width: 100%;
	}

	.single-site-preview {
		margin: 0;
		height: 40vh;
	}

	.single-site-pages-wrap {
		width: 100%;
		margin-top: 3em;
	}
}

.astra-sites__category-filter-items {
	z-index: 99;
}

@media screen and ( min-width: 481px ) {
	.hide-on-desktop {
		display: none;
	}
	.astra-sites__category-filter-items {
		z-index: 99;
	}
}
@media screen and ( max-width: 480px ) {
	#astra-sites-admin .astra-sites-popup .inner,
	.astra-sites-result-preview .inner {
		width: 95%;
	}
	.single-site-footer .button {
		text-align: center;
		width: 100%;
	}
	.single-site-pages {
		overflow: initial;
	}

	#astra-sites-filters.hide-on-desktop {
		box-shadow: 25px 0 30px rgba( 28, 39, 60, 0.09 );
		border-bottom: 1px solid #dddddd;
		background: #fff;
		padding: 10px;
	}

	#astra-sites-menu-page .form {
		flex: 1;
	}

	.favorite-filters-wrap {
		flex: 1;
		text-align: right;
		padding: 5px 10px;
	}

	.back-to-layout {
		padding: 13px 10px;
	}

	.page-builder-icon {
		padding: 18px 8px 18px 15px;
	}

	#astra-sites-menu-page .nav-tab-wrapper .logo {
		padding: 14px 10px;
	}

	#astra-sites__category-filter .astra-sites__category-filter-anchor {
		width: 90px;
	}

	.header-actions li > a {
		margin: 0 7px;
	}

	.astra-previewing-single-pages #wpbody-content {
		position: relative;
	}

	.astra-sites-no-sites .content,
	.astra-sites-no-favorites .content {
		flex-direction: column;
	}

	.astra-sites-no-sites .inner,
	.astra-sites-no-favorites .inner {
		width: auto;
	}

	.site-action-buttons-right {
		flex-direction: column;
		align-items: flex-start;
	}

	.site-import-layout-button {
		margin-left: 0 !important;
	}

	.astra-sites-no-sites .content,
	.astra-sites-no-favorites .content {
		flex-direction: column;
	}

	.astra-sites-no-sites .inner,
	.astra-sites-no-favorites .inner {
		width: auto;
	}

	.hide-on-mobile {
		display: none;
	}
	.astra-sites-logo-wrap {
		width: 30px;
		height: 30px;
	}

	.header-actions li > a {
		margin: 0 5px;
	}

	.page-builder-icon {
		width: auto;
	}

	.page-builder-icon .page-builders {
		margin-left: -95%;
	}

	.theme-browser .theme:nth-child( odd ) {
		margin-right: 0;
	}

	#astra-sites-admin {
		margin-top: 30px;
		margin-left: 20px;
		margin-right: 20px;
	}
	.appearance_page_starter-templates.auto-fold #wpcontent {
		padding-left: 0;
	}

	.back-to-layout {
		margin-right: 10px;
	}

	.single-site-footer {
		position: relative;
	}

	#wpbody-content {
		padding-bottom: 30px;
	}

	.site-action-buttons-wrap {
		flex-direction: column;
	}

	.site-action-buttons-wrap > .button:first-child {
		margin-bottom: 5px;
	}

	.appearance_page_starter-templates .astra-sites-sync-library-message {
		margin: 10px;
	}
}

#astra-sites-filters .section-left {
	flex: 1;
}

#astra-sites-filters .search-form {
	float: none;
}

/**
 * Popup
 */

.ast-actioms-wrap {
	padding: 13px 35px 13px 35px;
	z-index: 30;
	box-sizing: border-box;
	border-top: 1px solid #dcdcdc;
}
.wp-core-ui .ast-actioms-wrap .button,
.wp-core-ui .ast-actioms-wrap .button:focus {
	font-size: 13px;
	height: 36px;
	min-height: 36px;
	line-height: 34px;
	padding: 0 20px;
	box-shadow: none;
}
.ast-actioms-wrap .button {
	margin: 0;
}
.ast-actioms-wrap .button {
	margin-right: 0.5em;
}
.astra-site-contents > li {
	margin-bottom: 12px;
}

.astra-sites-popup {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	background: #fff;
	overflow-y: auto;
}

.astra-sites-popup .dashicons {
	vertical-align: middle;
	font-size: 1rem;
}
.astra-sites-popup .button {
	margin-top: 1em;
}

.astra-sites-popup h3 {
	font-size: 1.4em;
	margin: 0;
}
.astra-sites-popup .heading {
	background: #ffffff;
	padding: 16px 35px;
	margin-top: 0;
	box-shadow: 0 0 8px rgba( 0, 0, 0, 0.1 );
	margin-bottom: 10px;
	display: flex;
	justify-content: space-between;
}
.astra-sites-popup .close {
	position: absolute;
	right: 0;
	top: 0;
	bottom: 0;
	height: 53px;
	line-height: 53px;
	width: 53px;
	cursor: pointer;
	border-left: 1px solid #eee;
	color: #aaa;
	font-size: 21px;
}
.astra-sites-popup .close:hover {
	color: #444;
}
.astra-sites-popup .overlay {
	background: rgba( 0, 0, 0, 0.7 );
	filter: alpha( opacity=70 );
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 100050;
}
.astra-sites-popup .inner {
	margin: 0 auto;
	width: 660px;
	max-height: 420px;
	-webkit-transform: translate( -50%, -50% );
	-ms-transform: translate( -50%, -50% );
	transform: translate( -50%, -50% );
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	position: fixed;
	background-color: #f3f3f3;
	z-index: 100059;
	text-align: left;
	top: 50%;
	left: 50%;
	-webkit-box-shadow: 0 3px 6px rgba( 0, 0, 0, 0.3 );
	box-shadow: 0 3px 6px rgba( 0, 0, 0, 0.3 );
	border-radius: 2px;
}
.astra-sites-popup .button {
	line-height: 40px;
}

.ast-sites__search-title {
	padding: 0 1.1%;
	font-size: 18px;
	margin-bottom: 10px;
}

.ast-sites__search-wrap {
	display: flex;
	flex-wrap: wrap;
}

/**
 * License Form
 */
#astra-pro-sites-license-form p:first-child {
	margin-top: 0;
}

#astra-pro-sites-license-form p {
	margin: 0.3em 0;
}

#astra-pro-sites-license-form .bsf-license-key-registration {
	margin-top: 1em;
}

#astra-pro-sites-license-form .inner {
	position: relative;
	box-shadow: none;
	height: auto;
	width: auto;
	min-height: auto;
	transition: none;
	top: 0;
	left: 0;
	transform: none;
}

#astra-pro-sites-license-form h3 {
	display: none;
}

#astra-pro-sites-license-form .astra-product-license {
	line-height: normal;
	margin-top: 0;
	margin-left: 0.5em;
	padding: 0.45em;
}

#astra-pro-sites-license-form .license-form-field {
	float: left;
	width: 100%;
	width: calc( 100% - 140px );
}

#astra-pro-sites-license-form .regular-text {
	width: 100%;
}

#astra-pro-sites-license-form .submit-button-wrap p {
	margin-top: 1em;
}

.appearance_page_starter-templates.astra-sites-change-page-builder .astra-sites-notice {
	display: none;
}

.astra-sites-log p:first-child {
	margin-top: 0;
}

.astra-sites-log p:last-child {
	margin-bottom: 0;
}

.astra-sites-log .batch-log {
	background: #fff;
	padding: 2em;
	margin-bottom: 1em;
}

.astra-sites-log table td {
	vertical-align: top;
	padding: 15px;
	text-align: left;
}

.white-label-enabled .astra-sites-logo-wrap {
	width: auto;
	display: flex;
	align-items: center;
	font-size: 1.2rem;
	font-weight: 500;
}

.appearance_page_starter-templates .notice.astra-sites-must-notices {
	display: block;
}

.astra-previewing-single-pages.appearance_page_starter-templates .notice.astra-sites-must-notices {
	display: none;
}

#astra-pro-sites-license-form .astra-pro-sites-fail-message {
	color: #f44336;
}

#astra-pro-sites-license-form .bsf-current-license-success-astra-pro-sites,
#astra-pro-sites-license-form .bsf-current-license-error-astra-pro-sites {
	display: none;
}

.bsf-current-license-error-astra-pro-sites,
.bsf-current-license-success-astra-pro-sites {
	display: block;
	margin-bottom: 0.5em;
}

.astra-pro-sites-license-form-status-success .astra-product-license {
	display: none;
}

#astra-pro-sites-license-form.astra-pro-sites-license-form-status-success .license-form-field {
	width: calc( 100% - 2px );
}

#astra-pro-sites-license-form .astra-pro-sites-success-message {
	color: #008000;
}

.bsf-current-license-error-astra-pro-sites {
	color: #f44336;
}

.skip-and-import .astra-site-contents {
	list-style-type: circle;
	margin-left: 1.7em;
}

.astra-site-contents .astra-theme-module {
	margin-left: 2em;
}

.dont-use-astra-theme .plugin-card-astra-addon,
.required-plugins-count-1.dont-use-astra-theme .astra-sites-import-plugins {
	display: none;
}

.subscription-popup {
	display: none;
}

.subscription-form {
	margin-top: 2em;
}

.subscription-field select,
.subscription-field input[type="text"],
.subscription-field input[type="email"] {
	width: 100%;
	border-radius: 3px;
	border: 1px solid #8b959d;
	font-size: 13px;
	height: 40px;
	padding: 0 10px;
}

.subscription-fields .subscription-field {
	margin-bottom: 1.4em;
}

.subscription-wp-user-type .label {
	margin-right: 1em;
	margin-left: 2px;
}

.button-subscription-skip:focus {
	box-shadow: none;
}

.button-subscription-skip {
	margin-top: 0.8em;
	display: inline-block;
}

.import-page #astra-sites-subscription-form-two .subscription-field-wrap {
	margin-bottom: 5px;
}

#astra-sites-subscription-form-two .subscription-field-wrap {
	margin-bottom: 0.8em;
}

.subscription-actions {
	display: flex;
	align-items: center;
}

.subscription-wp-user-type {
	display: flex;
	margin-bottom: 2em;
}

.subscription-field .subscription-input .error {
	border-color: red;
}

.subscription-input input[type="email"] {
	width: 100%;
}

.subscription-popup .astra-sites-import-content {
	height: 250px;
	padding: 20px 35px 20px 35px;
}

.subscription-actions .submitting .dashicons,
.subscription-actions .submitted .dashicons {
	opacity: 1;
	visibility: visible;
}

.subscription-actions .dashicons {
	opacity: 0;
	visibility: hidden;
}

.subscription-actions .submitting .dashicons {
	animation: rotation 2s infinite linear;
}

.wp-core-ui .subscription-field select {
	background-position: right 7px top 55%;
}

.wp-core-ui .astra-sites-result-preview .subscription-actions .button.button-hero {
	margin: 0;
	box-shadow: none;
	font-size: 14px;
	min-height: 44px;
	padding: 0 18px;
	width: 100%;
}

.wp-core-ui .subscription-wp-user-type select:focus,
.subscription-field input[type="text"]:focus,
.subscription-field input[type="email"]:focus,
.wp-core-ui .subscription-wp-user-type select:hover,
.subscription-field input[type="text"]:hover,
.subscription-field input[type="email"]:hover {
	border-color: #444c53;
}

.subscription-input input[type="email"],
.subscription-input input[type="text"] {
	color: #444;
}

.wp-core-ui .subscription-wp-user-type select:focus,
.subscription-field input[type="text"]:focus,
.subscription-field input[type="email"]:focus {
	box-shadow: none;
}

.wp-core-ui .subscription-wp-user-type select,
.subscription-wp-user-type .subscription-input {
	width: 100%;
	color: #72777c;
}

.subscription-actions + p {
	margin: 30px 0 0 0;
}

.subscription-footer {
	margin-top: 0.8em;
	text-align: center;
}

.subscription-footer p {
	margin: 0;
}

.subscription-fields .subscription-input:focus {
	outline: none;
	box-shadow: none;
}

.subscription-fields .subscription-input {
	width: 100%;
	max-width: 100%;
	border-radius: 3px;
	border: 1px solid #585f66;
	padding: 0 10px;
	padding: 14px 14px;
	box-shadow: none;
	padding: 6px 12px;
	min-height: 44px;
	font-size: 14px;
	font-weight: 400;
	outline: none;
	background-color: transparent;
	background-position: right 10px top 55%;
	margin: 0;
}

.subscription-label {
	color: #585f66;
	margin: 12px 10px;
	position: absolute;
	top: 0;
	font-weight: 400;
	padding: 0 5px;
	font-size: 14px;
	left: 0;
	-webkit-transition: all 0.25s ease;
	transition: all 0.25s ease;
	pointer-events: none;
}

.subscription-field-wrap {
	position: relative;
}

.subscription-fields .subscription-field-wrap:not( .subscription-success, .subscription-error ) .subscription-input:hover {
	border-color: #2271b1;
}

.subscription-fields .subscription-input:hover,
.subscription-fields .subscription-input:focus {
	color: #2c3338;
}

.subscription-anim .subscription-label {
	background-color: #f1f3f5;
	font-size: 12px;
	line-height: 12px;
	margin-top: 0;
	padding: 0 4px;
	top: -5px;
	transition: all 0.125s ease;
	-webkit-transition: all 0.125s ease;
	color: #2271b1;
}

.subscription-error .subscription-input,
.subscription-error .subscription-input:focus {
	border-color: red;
}

.subscription-error .subscription-desc {
	color: red;
}

.subscription-anim.subscription-error .subscription-label {
	color: red;
}

.subscription-field-wrap .subscription-desc {
	padding-left: 2px;
	opacity: 0;
	visibility: hidden;
}

.subscription-success .subscription-input,
.subscription-success .subscription-input:focus {
	border-color: #66a700;
}

.subscription-anim.subscription-success .subscription-label {
	color: #66a700;
}

.subscription-field-wrap {
	margin-bottom: 1em;
}

.subscription-field-wrap.subscription-error .subscription-desc {
	opacity: 1;
	visibility: visible;
}

.subscription-fields {
	display: flex;
}

.subscription-fields .subscription-field-wrap {
	flex: 1;
}

.subscription-enabled .astra-sites-advanced-options {
	display: none;
}

.astra-sites-advanced-options-heading {
	display: none;
}

.subscription-enabled .astra-sites-advanced-options-heading {
	display: inline-block;
	margin: 0;
	cursor: pointer;
	font-size: 15px;
}

.subscription-enabled .astra-sites-subscription-form-one {
	display: block;
}

.astra-sites-subscription-form-one {
	display: none;
}

.astra-sites-advanced-options-heading .dashicons {
	opacity: 1;
	visibility: visible;
}

.astra-sites-subscription-form-one {
	margin-top: 1.8em;
	max-width: 580px;
}

.import-page .subscription-field-wrap {
	margin-bottom: 0.5em;
}

.import-page .subscription-popup .astra-sites-import-content {
	height: 285px;
}

.highlighted-note {
	font-size: 14px;
	font-weight: 500;
}

@media screen and ( max-width: 768px ) {
	.wp-core-ui .astra-sites-result-preview .subscription-actions .button.button-hero {
		height: auto;
		word-break: break-word;
		white-space: initial;
		line-height: 1.5;
		padding: 5px 18px;
	}
	.astra-sites-result-preview input[type="checkbox"] {
		height: 1rem;
		width: 1rem;
	}
	.astra-sites-result-preview input[type="checkbox"]:checked:before {
		height: 1.2rem;
		width: 1.2rem;
		margin: -0.1rem -0.2rem;
	}
	.subscription-popup .astra-sites-import-content {
		padding: 20px;
	}
	#astra-sites-subscription-form-one .subscription-field-wrap {
		margin-bottom: 0.8em;
	}
	.subscription-fields {
		flex-direction: column;
	}
}

@media screen and ( min-width: 768px ) {
	.subscription-fields .subscription-field-wrap:first-child {
		padding-right: 20px;
	}
}
