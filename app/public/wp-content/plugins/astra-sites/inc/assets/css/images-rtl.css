@font-face {
	font-family: "icomoon";
	src: url( "../fonts/astra-sites.eot?9i8jex" );
	src: url( "../fonts/astra-sites.eot?9i8jex#iefix" ) format( "embedded-opentype" ), url( "../fonts/astra-sites.ttf?9i8jex" ) format( "truetype" ), url( "../fonts/astra-sites.woff?9i8jex" ) format( "woff" ), url( "../fonts/astra-sites.svg?9i8jex#icomoon" ) format( "svg" );
	font-weight: normal;
	font-style: normal;
}

[class^="ast-icon-"],
[class*=" ast-icon-"] {
	font-family: "icomoon" !important;
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.ast-icon-chevron-left:before {
	content: "\e904";
}

.ast-icon-search:before {
	content: "\e900";
}

.ast-attachments-browser .ast-image__search.has-input + .search-icon {
	display: none;
}

.ast-attachments-browser .search-icon {
	position: absolute;
	top: 0;
	left: 0;
	color: #2b2b2b;
	opacity: 0.3;
	padding: 12px;
}

.ast-attachments-browser .ast-image__skeleton {
	column-count: 5;
	column-gap: 20px;
}

.ast-attachments-browser .ast-image__search-wrap {
	position: relative;
	display: inline-block;
	margin-left: 1%;
}

.ast-attachments-browser input.ast-image__search {
	line-height: 1.8em;
	width: 620px;
	height: 35px;
	background: #fff;
	border-color: #d5dadf;
}

.ast-attachments-browser .ast-image__list-wrap.loaded .ast-image__list-img-wrap {
	opacity: 1;
}

.ast-attachments-browser .ast-image__list-wrap.loaded .imported.ast-image__list-inner-wrap:before {
	display: block;
}

.fl-builder-edit .media-modal .media-frame-content .ast-image__list-img-overlay span,
.fl-builder-edit .media-modal .media-frame-content .ast-image__save {
	color: #fff;
}
.fl-builder-edit .media-modal .media-frame-content .ast-image__list-img-overlay .ast-image__download-icon {
	color: #767676;
}

.ast-attachments-browser .ast-image__list-img-overlay span {
	position: absolute;
	bottom: 0;
	right: 0;
	color: #fff;
	padding: 10px;
	font-size: 13px;
	width: calc( 100% - 20px );
	text-align: right;
	text-transform: capitalize;
}

.ast-attachments-browser .ast-image__list-img-overlay .ast-image__download-icon {
	margin: 10px;
	padding: 5px 7px;
	width: auto;
	border-radius: 2px;
	left: 0;
	text-align: left;
	font-size: 16px;
	right: auto;
	background: #fff;
	color: #767676;
}

.ast-attachments-browser .ast-image__list-img-overlay .ast-image__download-icon:hover {
	color: #111;
	box-shadow: rgb( 0 0 0 / 30% ) 0px 4px 14px;
}

.ast-attachments-browser .ast-image__list-img-overlay .ast-image__download-icon.installing:before {
	content: "\f463";
	font: normal 20px/1 "dashicons";
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	vertical-align: middle;
	display: inline-block;
	-webkit-animation: cssAnimation 2s linear infinite;
	-moz-animation: cssAnimation 2s linear infinite;
	-o-animation: cssAnimation 2s linear infinite;
	-ms-animation: cssAnimation 2s linear infinite;
	animation: cssAnimation 2s linear infinite;
}

.ast-attachments-browser .ast-image__list-img-wrap {
	display: flex;
	opacity: 0;
	position: relative;
}

.ast-attachments-browser .ast-image__list-img-wrap:hover {
	box-shadow: 0 3px 10px rgba( 0, 0, 0, 0.12 );
}

.ast-attachments-browser .ast-image__list-img-overlay {
	position: absolute;
	top: 0;
	right: 0;
	width: 100%;
	height: 100%;
	background: rgba( 0, 0, 0, 0.4 );
	opacity: 0;
	transition: opacity 0.5s, visibility 0s 0.5s;
	cursor: pointer;
}

.ast-attachments-browser .ast-image__list-inner-wrap:hover .ast-image__list-img-overlay {
	opacity: 1;
}

.ast-attachments-browser .ast-image__filter-wrap {
	display: inline-block;
	vertical-align: top;
}

.ast-powered-by-pixabay-wrap {
	margin-left: 25px;
	display: flex;
	align-items: center;
	float: left;
	margin-top: 10px;
}

.ast-powered-by-pixabay-wrap > span {
	font-size: 12px;
	color: #50575e;
	margin-left: 5px;
}

.ast-powered-by-pixabay-wrap > img {
	height: 1.2em;
}

.ast-attachments-browser .ast-attachments-search-wrap {
	padding: 10px 20px;
	border-bottom: 1px solid #ddd;
	display: block;
	height: 36px;
	align-items: center;
	padding-left: 0px;
}

.ast-attachments-browser .ast-image__filter {
	display: flex;
	padding: 0;
	margin: 0;
	list-style-type: none;
	align-items: center;
}

.ast-attachments-browser .ast-image__filter li {
	padding: 0 8px;
	margin: 0;
}

.ast-attachments-browser .ast-image__filter li:first-child {
	padding-right: 0;
}

.ast-attachments-browser .ast-image__filter li select {
	height: 35px;
	width: auto;
}

.fl-builder .ast-attachments-browser .ast-image__filter li select {
	padding: 0 10px;
}

.ast-attachments-browser .ast-image__skeleton-inner-wrap {
	overflow-y: scroll;
	height: inherit;
	padding: 10px;
	padding-bottom: 0;
	height: inherit;
}

.ast-attachments-browser .button.ast-image__validate-btn {
	height: 38px;
	line-height: 37px;
	padding: 0 15px 2px;
}

.ast-attachments-browser.preview-mode .ast-image__skeleton-inner-wrap {
	overflow-y: auto;
}

.ast-attachments-browser .ast-image__list-wrap {
	padding: 0;
	margin: 0;
	display: grid;
	grid-template-rows: 1fr auto;
	margin-bottom: 20px;
	break-inside: avoid;
}
.ast-attachments-browser .ast-image__skeleton-wrap {
	height: 100%;
}

.ast-attachments-browser .ast-image__list-inner-wrap img {
	width: 100%;
	height: auto;
}

.ast-attachments-browser .single-site-wrap {
	display: flex;
}

.ast-attachments-browser .single-site {
	width: 100%;
	display: flex;
}

.ast-attachments-browser .single-site-preview {
	height: auto;
	margin-left: 0;
	max-height: calc( 100vh - 280px );
	overflow-y: auto;
	border-radius: 2px;
	box-shadow: none;
}

.ast-attachments-browser .single-site-preview img {
	vertical-align: middle;
}

.ast-attachments-browser .ast-image__preview-skeleton {
	padding: 10px;
}

.ast-attachments-browser .single-site-preview img,
.ast-attachments-browser .single-site-preview-wrap {
	width: 100%;
}
.ast-image__list-inner-wrap.imported {
	position: relative;
}

.ast-attachments-browser .imported.ast-image__list-inner-wrap:before {
	content: "IMPORTED";
	background: rgba( 0, 0, 0, 0.5 );
	color: #fff;
	top: 10px;
	right: 10px;
	width: auto;
	height: auto;
	padding: 5px;
	margin: 0;
	z-index: 9;
	position: absolute;
	display: none;
}

.ast-attachments-browser .ast-image__go-back {
	font-weight: 600;
	font-size: 12px;
	cursor: pointer;
	display: inline-block;
	align-items: center;
	margin-top: 7px;
}

.ast-attachments-browser .ast-image__go-back i {
	font-size: 14px;
	height: 14px;
	width: 14px;
	vertical-align: text-bottom;
}

.ast-attachments-browser .ast-image__save-wrap {
	padding: 0 20px;
	display: inline-block;
	float: left;
}

.ast-attachments-browser .ast-image__loader-wrap {
	display: none;
	margin: 0 auto;
	min-height: 58px;
	line-height: 58px;
	width: 160px;
	text-align: center;
	position: absolute;
	right: 50%;
	bottom: 0;
	transform: translateX( 50% );
}

.ast-attachments-browser .ast-image__loader-wrap > div {
	width: 18px;
	height: 18px;
	background-color: #0085ba;
	-webkit-border-radius: 100%;
	border-radius: 100%;
	display: inline-block;
	-webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
	animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.ast-attachments-browser .ast-image__loader-wrap .ast-image__loader-1 {
	-webkit-animation-delay: -0.32s;
	animation-delay: -0.32s;
}

.ast-attachments-browser .ast-image__loader-wrap .ast-image__loader-2 {
	-webkit-animation-delay: -0.16s;
	animation-delay: -0.16s;
}

.ast-attachments-browser .ast-image__license-heading {
	margin: 0;
	text-align: center;
	font-size: 1.4em;
	font-weight: 600;
	margin-top: 0;
	color: #23282d;
}

.ast-attachments-browser .ast-image__license-description {
	font-size: 13px;
	line-height: 1.5;
	margin: 2em 4em;
	color: #444;
}

.ast-attachments-browser .ast-image__license-get-wrap {
	font-size: 1em;
	margin-top: 40px;
	padding: 18px;
	border-top: 1px solid #eae5e5;
}

.ast-attachments-browser .ast-image__license-input-wrap {
	margin-bottom: 20px;
}

.ast-attachments-browser .ast-image__license-get-wrap h4 {
	color: #444;
	margin: 0;
}

.ast-attachments-browser .astra-sites-no-sites {
	width: 100%;
	padding: 7em 0;
}

.ast-attachments-browser .astra-sites-no-sites h3 {
	font-size: 1.3rem;
	font-weight: normal;
	color: #666;
	margin-top: 0;
	text-align: center;
	margin-bottom: 3em;
}

.ast-attachments-browser .ast-image__license-heading-wrap {
	background: #fff;
	margin-bottom: 50px;
	padding: 16px 35px;
	box-shadow: 0 0 8px rgba( 0, 0, 0, 0.1 );
	border-radius: 2px 2px 0 0;
}

.ast-attachments-browser .ast-image__license-wrap {
	position: absolute;
	background: #fcfcfc;
	top: 50%;
	right: 50%;
	transform: translateX( 50% ) translateY( -40% );
	width: 600px;
	box-shadow: 0 2px 10px rgba( 0, 0, 0, 0.15 );
	text-align: center;
	border-radius: 2px;
}

.ast-attachments-browser .ast-image__license-input-inner-wrap {
	display: inline-grid;
	position: relative;
}

.ast-attachments-browser .ast-image__license-input-inner-wrap .ast-image__license-msg {
	padding: 5px 10px;
	border: 1px solid #cc3333;
	border-radius: 2px;
	text-align: right;
	margin-left: 10px;
	margin-top: 5px;
	margin-left: 10px;
	margin-right: 0;
	display: none;
	color: #cc3333;
	background: #ffeeee;
}

.ast-attachments-browser .ast-image__license-msg i {
	margin-left: 5px;
}

.ast-attachments-browser .ast-image__license-msg span {
	vertical-align: middle;
}

.ast-attachments-browser input.ast-image__license {
	margin-left: 10px;
	width: 300px;
	height: 40px;
	padding-right: 13px;
	border-color: #d6d6d6;
}

.ast-attachments-browser li.ast-image__license-edit-key {
	display: flex;
	justify-content: center;
	margin-right: 5px;
	align-items: center;
	border-right: 1px solid #ededed;
	padding: 0 15px;
}

.ast-attachments-browser .ast-image__license-edit-key + div {
	padding: 0 10px;
}

.ast-attachments-browser li.ast-image__license-edit-key a:focus {
	box-shadow: none;
}

.ast-attachments-browser .ast-image-valid-license {
	height: 40px;
	position: absolute;
	left: 20px;
	top: 0;
	color: green;
	display: flex;
	align-items: center;
}

.ast-attachments-browser .ast-image__browse-images {
	cursor: pointer;
}

.ast-image__save.button-primary {
	display: flex;
	align-items: center;
}

.ast-image__filter-safesearch > label input {
	margin-left: 5px;
}

.ast-attachments-browser .ast-image__save.installing:before {
	display: none;
}

.ast-attachments-browser .ast-image__save.installing:after {
	content: "\f463";
	display: inline-block;
	font: normal 20px/1 "dashicons";
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	vertical-align: middle;
	-webkit-animation: cssAnimation 0.72s ease infinite;
	-moz-animation: cssAnimation 0.72s ease infinite;
	-o-animation: cssAnimation 0.72s ease infinite;
	-ms-animation: cssAnimation 0.72s ease infinite;
	animation: cssAnimation 0.72s ease infinite;
	color: #fff;
	margin: 0;
	padding: 0;
	margin-right: 10px;
	vertical-align: middle;
}

@-webkit-keyframes cssAnimation {
	from {
		-webkit-transform: rotate( 0 );
		-moz-transform: rotate( 0 );
		-o-transform: rotate( 0 );
		-ms-transform: rotate( 0 );
		transform: rotate( 0 );
	}
	to {
		-webkit-transform: rotate( -360deg );
		-moz-transform: rotate( -360deg );
		-o-transform: rotate( -360deg );
		-ms-transform: rotate( -360deg );
		transform: rotate( -360deg );
	}
}
@-moz-keyframes cssAnimation {
	from {
		-webkit-transform: rotate( 0 );
		-moz-transform: rotate( 0 );
		-o-transform: rotate( 0 );
		-ms-transform: rotate( 0 );
		transform: rotate( 0 );
	}
	to {
		-webkit-transform: rotate( -360deg );
		-moz-transform: rotate( -360deg );
		-o-transform: rotate( -360deg );
		-ms-transform: rotate( -360deg );
		transform: rotate( -360deg );
	}
}
@-o-keyframes cssAnimation {
	from {
		-webkit-transform: rotate( 0 );
		-moz-transform: rotate( 0 );
		-o-transform: rotate( 0 );
		-ms-transform: rotate( 0 );
		transform: rotate( 0 );
	}
	to {
		-webkit-transform: rotate( -360deg );
		-moz-transform: rotate( -360deg );
		-o-transform: rotate( -360deg );
		-ms-transform: rotate( -360deg );
		transform: rotate( -360deg );
	}
}

@-webkit-keyframes sk-bouncedelay {
	0%,
	80%,
	100% {
		-webkit-transform: scale( 0 );
		transform: scale( 0 );
	}
	40% {
		-webkit-transform: scale( 1 );
		transform: scale( 1 );
	}
}

@keyframes sk-bouncedelay {
	0%,
	80%,
	100% {
		-webkit-transform: scale( 0 );
		transform: scale( 0 );
	}
	40% {
		-webkit-transform: scale( 1 );
		transform: scale( 1 );
	}
}

@media ( max-width: 767px ) {
	.ast-attachments-browser .ast-image__list-wrap {
		width: 33.33%;
	}
	.ast-attachments-browser:not( .preview-mode ) .ast-attachments-search-wrap {
		display: inline-block;
		height: 76px;
		width: 100%;
	}
	.ast-attachments-browser .search-icon {
		right: 265px;
	}
	.ast-attachments-browser .imported.ast-image__list-inner-wrap:before {
		top: 15px;
		padding: 4px;
		margin: 0 15px 0 5px;
		font-size: 11px;
	}
}

@media ( min-width: 768px ) and ( max-width: 1024px ) {
	.ast-attachments-browser .ast-image__list-wrap {
		width: 33.33%;
	}
	.ast-attachments-browser .ast-image__filter li select {
		width: 100px;
	}
	.ast-attachments-browser input.ast-image__search {
		width: 240px;
	}
}
@media ( min-width: 1025px ) and ( max-width: 1200px ) {
	.ast-attachments-search-wrap .ast-image__search-wrap input {
		width: 220px;
	}
}
@media ( min-width: 1201px ) and ( max-width: 1321px ) {
	.ast-attachments-search-wrap .ast-image__search-wrap input {
		width: 360px;
	}
}
@media ( min-width: 1322px ) and ( max-width: 1430px ) {
	.ast-attachments-search-wrap .ast-image__search-wrap input {
		width: 500px;
	}
}
