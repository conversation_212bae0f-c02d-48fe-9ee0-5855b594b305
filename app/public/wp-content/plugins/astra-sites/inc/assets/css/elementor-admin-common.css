.dialog-widget-content {
	background-color: #fff;
	position: absolute;
	-webkit-border-radius: 3px;
	border-radius: 3px;
	-webkit-box-shadow: 2px 8px 23px 3px rgba( 0, 0, 0, 0.2 );
	box-shadow: 2px 8px 23px 3px rgba( 0, 0, 0, 0.2 );
	overflow: hidden;
}

.dialog-message {
	font-size: 12px;
	line-height: 1.5;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.dialog-type-lightbox {
	position: fixed;
	height: 100%;
	width: 100%;
	top: 0;
	left: 0;
	background-color: rgba( 0, 0, 0, 0.8 );
	z-index: 9999;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.dialog-type-lightbox .dialog-widget-content {
	margin: auto;
}

.dialog-type-lightbox .dialog-header {
	font-size: 15px;
	color: #495157;
	padding: 30px 0 10px;
	font-weight: 500;
}

.dialog-type-lightbox .dialog-message {
	padding: 0 30px 30px;
	min-height: 50px;
}

.dialog-type-lightbox:not( .elementor-popup-modal ) .dialog-header,
.dialog-type-lightbox:not( .elementor-popup-modal ) .dialog-message {
	text-align: center;
}

.dialog-type-lightbox .dialog-buttons-wrapper {
	border-top: 1px solid #e6e9ec;
	text-align: center;
}

.dialog-type-lightbox .dialog-buttons-wrapper > .dialog-button {
	font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
	width: 50%;
	border: none;
	background: none;
	font-size: 15px;
	cursor: pointer;
	padding: 13px 0;
	outline: 0;
}
.dialog-type-lightbox .dialog-buttons-wrapper > .dialog-button:hover {
	background-color: #f4f6f7;
}
.dialog-type-lightbox .dialog-buttons-wrapper > .dialog-button.dialog-ok {
	color: #b01b1b;
}
.dialog-type-lightbox .dialog-buttons-wrapper > .dialog-button.dialog-take_over {
	color: #39b54a;
}
.dialog-type-lightbox .dialog-buttons-wrapper > .dialog-button:active {
	background-color: rgba( 230, 233, 236, 0.5 );
}
.dialog-type-lightbox .dialog-buttons-wrapper > .dialog-button::-moz-focus-inner {
	border: 0;
}

.ast-sites-modal .dialog-widget-content {
	font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
	background-color: #f1f3f5;
	width: 100%;
}
@media ( max-width: 1439px ) {
	.ast-sites-modal .dialog-widget-content {
		max-width: 990px;
	}
}
@media ( min-width: 1440px ) {
	.ast-sites-modal .dialog-widget-content {
		max-width: 1200px;
	}
}

.ast-sites-modal .dialog-header {
	padding: 0;
	background-color: #fff;
	-webkit-box-shadow: 0 0 8px rgba( 0, 0, 0, 0.1 );
	box-shadow: 0 0 8px rgba( 0, 0, 0, 0.1 );
	position: relative;
	z-index: 1;
}

.ast-sites-modal .dialog-buttons-wrapper {
	background-color: #fff;
	border: none;
	display: none;
	-webkit-box-pack: end;
	-webkit-justify-content: flex-end;
	-ms-flex-pack: end;
	justify-content: flex-end;
	padding: 5px;
	-webkit-box-shadow: 0 0 8px rgba( 0, 0, 0, 0.1 );
	box-shadow: 0 0 8px rgba( 0, 0, 0, 0.1 );
	position: relative;
}
.ast-sites-modal .dialog-buttons-wrapper .elementor-button {
	height: 40px;
	margin-left: 5px;
}
.ast-sites-modal .dialog-buttons-wrapper .elementor-button-success {
	padding: 12px 36px;
	color: #fff;
	width: initial;
	font-size: 15px;
}
.ast-sites-modal .dialog-buttons-wrapper .elementor-button-success:hover {
	background-color: #39b54a;
}

.ast-sites-modal .dialog-message {
	height: 750px;
	max-height: 85vh;
	overflow: auto;
	padding-top: 25px;
}

.ast-sites-modal .dialog-content {
	height: 100%;
}

.ast-sites-modal .dialog-loading {
	display: none;
}

.ast-sites-modal__header {
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-ms-flex-pack: justify;
	justify-content: space-between;
	height: 50px;
}
.ast-sites-modal__header__logo {
	line-height: 1;
	text-transform: uppercase;
	font-weight: bold;
	cursor: pointer;
}
.ast-sites-modal__header__logo-area {
	text-align: left;
	padding-left: 15px;
}
.ast-sites-modal__header__logo-area > * {
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
}
.ast-sites-modal__header__logo__icon-wrapper {
	padding: 4px 7px 6px;
	background-image: -webkit-linear-gradient( 225deg, #f2295b, #434363 );
	background-image: -o-linear-gradient( 225deg, #f2295b, #434363 );
	background-image: linear-gradient( -135deg, #f2295b, #434363 );
	-webkit-border-radius: 2px;
	border-radius: 2px;
	margin-right: 10px;
}
.ast-sites-modal__header__logo__title {
	padding-top: 2px;
}
.ast-sites-modal__header__logo i {
	color: #fff;
	font-size: 10px;
}
.ast-sites-modal__header__items-area {
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: reverse;
	-webkit-flex-direction: row-reverse;
	-ms-flex-direction: row-reverse;
	flex-direction: row-reverse;
}
.ast-sites-modal__header__item {
	position: relative;
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
}
.ast-sites-modal__header__item > i {
	font-size: 20px;
	-webkit-transition: all 0.3s;
	-o-transition: all 0.3s;
	transition: all 0.3s;
	cursor: pointer;
}
.ast-sites-modal__header__item > i:not( :hover ) {
	color: #a4afb7;
}
.ast-sites-modal__header__close--normal {
	width: 45px;
	border-left: 1px solid #e6e9ec;
}
.ast-sites-modal__header__close--normal i {
	font-size: 18px;
}
.ast-sites-modal__header__close--skip {
	padding: 10px;
	padding-left: 20px;
	margin-right: 10px;
	color: #fff;
	background-color: #a4afb7;
	font-size: 11px;
	font-weight: normal;
	line-height: 1;
	text-transform: uppercase;
	-webkit-border-radius: 2px;
	border-radius: 2px;
	cursor: pointer;
}
.ast-sites-modal__header__close--skip > i {
	font-size: inherit;
	padding-left: 10px;
	margin-left: 15px;
	border-left: 1px solid;
}
.ast-sites-modal__header__close--skip > i:not( :hover ) {
	color: #fff;
}

.ast-sites-modal__sidebar {
	width: 25%;
	background-color: rgba( 255, 255, 255, 0.3 );
}

.ast-sites-modal__content {
	-webkit-box-flex: 1;
	-webkit-flex-grow: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
	-webkit-box-shadow: 0 0 13px inset rgba( 0, 0, 0, 0.05 );
	box-shadow: 0 0 13px inset rgba( 0, 0, 0, 0.05 );
}
