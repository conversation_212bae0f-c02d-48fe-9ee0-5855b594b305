.notice:not( .ast-img-notice ) {
	display: none;
}
.ast-img-flex {
	-js-display: flex;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-align-content: center;
	-ms-flex-line-pack: center;
	align-content: center;
}

.form-field p.ast-img-p {
	margin: 1em 0;
}

.ast-img-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
	box-sizing: border-box;
}
.form-wrap .ast-img-response-warning {
	display: block;
	color: #d02222;
}
.form-wrap .ast-img-google-error-response {
	margin-top: 5px;
	display: block;
}
.form-wrap .ast-img-google-error-response .ast-img-response-warning {
	display: inline-block;
}
.form-wrap .ast-img-response-success {
	display: block;
	color: #2dbe15;
}
.postbox .inside .form-wrap .ast-img-integration-heading {
	font-size: 15px;
	font-weight: 600;
}
#ast-img-menu-page .ast-img-notice {
	max-width: 860px;
	margin: 0 auto;
}

.ast-img-menu-page-wrapper a {
	text-decoration: none;
}

.ast-img-general-form-wrap {
	width: 680px;
}

.ast-img-bulk-actions-wrap {
	flex: auto;
	text-align: right;
}

.ast-img-general-list .inside {
	padding: 0;
	margin: 0;
}

.ast-img-list-section .ast-img-widget-list li {
	-js-display: flex;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-align-content: center;
	-ms-flex-line-pack: center;
	align-content: center;
	padding: 10px 12px;
	-moz-box-shadow: 0px 0px 2px rgba( 0, 0, 0, 0.1 ) inset;
	-webkit-box-shadow: 0px 0px 2px rgba( 0, 0, 0, 0.1 ) inset;
	box-shadow: 0px 0px 2px rgba( 0, 0, 0, 0.1 ) inset;
	margin-bottom: 0;
}

.ast-img-list-section .ast-img-widget-list li:nth-child( even ) {
	background-color: #fbfbfb;
}

.ast-img-list-section .ast-img-widget-list .activate {
	border-left: 3px solid #008ec2;
	padding-left: 12px;
}

.ast-img-widget-list a {
	text-decoration: none;
}

.ast-img-widget-link-wrapper {
	-js-display: flex;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-align-content: center;
	-ms-flex-line-pack: center;
	align-content: center;
	display: -webkit-flex;
	-webkit-flex-direction: row-reverse;
	flex-direction: row-reverse;
	flex: auto;
}

.ast-img-widget-list li a:before {
	display: inline-block;
	margin-right: 2px;
	font: normal 20px/1 "dashicons";
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	vertical-align: top;
}

.ast-img-widget-link-wrapper a:nth-child( even ) {
	margin: 0 10px;
}

.ast-img-container .postbox .hndle {
	background: #f7f7f7;
	border-bottom: 2px solid #e6e6e6;
}
.ast-img-container .postbox ul {
	margin: 0;
}
.ast-img-widget-list a {
	line-height: 1.6;
}
.ast-img-widget-list a:focus {
	box-shadow: none;
}

.ast-img-branding-list {
	margin: 0;
}
.ast-img-menu-page-wrapper .ast-img-container .clear {
	overflow: auto;
	visibility: visible;
	width: auto;
	height: auto;
}

/* Update icon. */
.ast-img-widget-link-wrapper a.updating-message:before {
	color: #f56e28;
	content: "\f463";
}

/* Spins the update icon. */
.ast-img-widget-link-wrapper a.updating-message:before {
	-webkit-animation: rotation 2s infinite linear;
	animation: rotation 2s infinite linear;
}

.settings_page_astra-images #wpcontent {
	padding: 0;
}

/* Header */
.ast-img-menu-page-header.general .ast-img-container,
.ast-img-menu-page-header.branding .ast-img-container,
.ast-img-container.ast-img-general,
.ast-img-container.ast-img-branding-wrapper {
	max-width: 930px;
}
.ast-img-title {
	flex: auto;
	align-content: flex-start;
	text-align: left;
	margin: 0;
}
.ast-img-title a,
.ast-img-title span {
	display: block;
	outline: none;
	box-shadow: none;
	font-size: 1.5rem;
	color: #333333;
	font-weight: 500;
}

.ast-img-header-icon {
	width: 50px;
	vertical-align: bottom;
}
.ast-img-top-links {
	flex: auto;
	text-align: right;
	font-weight: bold;
}
.ast-img-menu-page-header {
	background-color: #fff;
	text-align: center;
	padding: 10px 0;
	margin-bottom: 20px;
	box-shadow: 0 2px 0 #efefef, 0 2px 2px #ececec;
}

/* White Label link*/
.ast-img-widget-list .ast-img-white-label {
	border-left: 3px solid #008ec2;
	padding-left: 12px;
}

.ast-img-hide-branding #poststuff #post-body.columns-2 {
	margin: 0 auto;
	max-width: 590px;
}

/* Integration */
.ast-img-integration-wrapper #poststuff {
	width: 640px;
	margin: 0 auto;
	min-width: 640px;
}

.ast-img-integration-form-wrap .form-field select {
	border-style: solid;
	border-width: 1px;
	width: 95%;
}

/* Spinner */
.ast-img-button-spinner:before {
	font: normal 20px/0.5 dashicons;
	speak: none;
	display: inline-block;
	padding: 0;
	top: 8px;
	left: -4px;
	position: relative;
	vertical-align: top;
	content: "\f463";
}

.ast-img-button-spinner.loading:before {
	-webkit-animation: rotation 1s infinite linear;
	animation: rotation 1s infinite linear;
}

.ast-img-title > a {
	display: flex;
	align-items: center;
}
.ast-img-title .ast-img-plugin-version {
	background-color: #e5e5e5;
	border-radius: 3px;
	font-size: 0.5em;
	font-weight: 400;
	margin-left: 10px;
	padding: 2px 7px;
	color: #333;
}

/* New extensions badge */
#Particles .ast-img-widget-title:after {
	content: "extension";
	color: #fff;
	background: #2ecc71;
	font-size: 0.7em;
	font-weight: 600;
	position: relative;
	padding: 0.3em 0.6em;
	top: -1px;
	left: 12px;
	letter-spacing: 0.5px;
	line-height: 1em;
	text-transform: none;
	text-transform: uppercase !important;
	border-radius: 2px;
}
