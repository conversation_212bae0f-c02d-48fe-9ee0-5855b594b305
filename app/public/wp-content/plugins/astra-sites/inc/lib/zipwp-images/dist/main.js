(()=>{"use strict";var e,t,n,r={472:(e,t,n)=>{var r=n(609),o=n.t(r,2),i=n.n(r);const a=window.wp.element,s=e=>{let t;const n=new Set,r=(e,r)=>{const o="function"==typeof e?e(t):e;if(!Object.is(o,t)){const e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach((n=>n(t,e)))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=t=e(r,o,i);return i};var l=n(242);const{useDebugValue:u}=r,{useSyncExternalStoreWithSelector:c}=l;let d=!1;const f=e=>e,p=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t="function"==typeof e?(e=>e?s(e):s)(e):e,n=(e,n)=>function(e,t=f,n){n&&!d&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),d=!0);const r=c(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return u(r),r}(t,e,n);return Object.assign(n,t),n},m="SET_PREVIEW_IMAGE",h=(g=(e,{type:t,payload:n})=>t===m?{...e,imagePreview:n}:e,b={imagePreview:null},v=(e,t,n)=>(n.dispatch=t=>(e((e=>g(e,t)),!1,t),t),n.dispatchFromDevtools=!0,{dispatch:(...e)=>n.dispatch(...e),...b}),v?p(v):p);var v,g,b,y=e=>"checkbox"===e.type,w=e=>e instanceof Date,x=e=>null==e;const E=e=>"object"==typeof e;var k=e=>!x(e)&&!Array.isArray(e)&&E(e)&&!w(e),S=e=>{const t=e.constructor&&e.constructor.prototype;return k(t)&&t.hasOwnProperty("isPrototypeOf")},O="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function T(e){let t;const n=Array.isArray(e);if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(O&&(e instanceof Blob||e instanceof FileList)||!n&&!k(e))return e;if(t=n?[]:{},n||S(e))for(const n in e)e.hasOwnProperty(n)&&(t[n]=T(e[n]));else t=e}return t}var C=e=>Array.isArray(e)?e.filter(Boolean):[],F=e=>void 0===e,A=(e,t,n)=>{if(!t||!k(e))return n;const r=C(t.split(/[,[\].]+?/)).reduce(((e,t)=>x(e)?e:e[t]),e);return F(r)||r===e?F(e[t])?n:e[t]:r},R=e=>"boolean"==typeof e;const P="blur",L="focusout",N="onBlur",M="onChange",I="onSubmit",D="onTouched",j="all",V="pattern",z="required";r.createContext(null);var H=e=>k(e)&&!Object.keys(e).length,B=e=>Array.isArray(e)?e:[e];var W=e=>"string"==typeof e,$=e=>/^\w*$/.test(e),U=e=>C(e.replace(/["|']|\]/g,"").split(/\.|\[/)),q=(e,t,n)=>{let r=-1;const o=$(t)?[t]:U(t),i=o.length,a=i-1;for(;++r<i;){const t=o[r];let i=n;if(r!==a){const n=e[t];i=k(n)||Array.isArray(n)?n:isNaN(+o[r+1])?{}:[]}e[t]=i,e=e[t]}return e},G=(e,t,n,r,o)=>t?{...n[e],types:{...n[e]&&n[e].types?n[e].types:{},[r]:o||!0}}:{},K=e=>({isOnSubmit:!e||e===I,isOnBlur:e===N,isOnChange:e===M,isOnAll:e===j,isOnTouch:e===D}),Y=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const X=(e,t,n,r)=>{for(const o of n||Object.keys(e)){const n=A(e,o);if(n){const{_f:e,...i}=n;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],o)&&!r)break;if(e.ref&&t(e.ref,e.name)&&!r)break;X(i,t)}else k(i)&&X(i,t)}}};var Q=(e,t,n)=>{const r=C(A(e,n));return q(r,"root",t[n]),q(e,n,r),e},Z=e=>"file"===e.type,J=e=>"function"==typeof e,ee=e=>{if(!O)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},te=e=>W(e),ne=e=>"radio"===e.type,re=e=>e instanceof RegExp;const oe={value:!1,isValid:!1},ie={value:!0,isValid:!0};var ae=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!F(e[0].attributes.value)?F(e[0].value)||""===e[0].value?ie:{value:e[0].value,isValid:!0}:ie:oe}return oe};const se={isValid:!1,value:null};var le=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),se):se;function ue(e,t,n="validate"){if(te(e)||Array.isArray(e)&&e.every(te)||R(e)&&!e)return{type:n,message:te(e)?e:"",ref:t}}var ce=e=>k(e)&&!re(e)?e:{value:e,message:""},de=async(e,t,n,r,o)=>{const{ref:i,refs:a,required:s,maxLength:l,minLength:u,min:c,max:d,pattern:f,validate:p,name:m,valueAsNumber:h,mount:v,disabled:g}=e._f,b=A(t,m);if(!v||g)return{};const w=a?a[0]:i,E=e=>{r&&w.reportValidity&&(w.setCustomValidity(R(e)?"":e||""),w.reportValidity())},S={},O=ne(i),T=y(i),C=O||T,P=(h||Z(i))&&F(i.value)&&F(b)||ee(i)&&""===i.value||""===b||Array.isArray(b)&&!b.length,_=G.bind(null,m,n,S),L=(e,t,n,r="maxLength",o="minLength")=>{const a=e?t:n;S[m]={type:e?r:o,message:a,ref:i,..._(e?r:o,a)}};if(o?!Array.isArray(b)||!b.length:s&&(!C&&(P||x(b))||R(b)&&!b||T&&!ae(a).isValid||O&&!le(a).isValid)){const{value:e,message:t}=te(s)?{value:!!s,message:s}:ce(s);if(e&&(S[m]={type:z,message:t,ref:w,..._(z,t)},!n))return E(t),S}if(!(P||x(c)&&x(d))){let e,t;const r=ce(d),o=ce(c);if(x(b)||isNaN(b)){const n=i.valueAsDate||new Date(b),a=e=>new Date((new Date).toDateString()+" "+e),s="time"==i.type,l="week"==i.type;W(r.value)&&b&&(e=s?a(b)>a(r.value):l?b>r.value:n>new Date(r.value)),W(o.value)&&b&&(t=s?a(b)<a(o.value):l?b<o.value:n<new Date(o.value))}else{const n=i.valueAsNumber||(b?+b:b);x(r.value)||(e=n>r.value),x(o.value)||(t=n<o.value)}if((e||t)&&(L(!!e,r.message,o.message,"max","min"),!n))return E(S[m].message),S}if((l||u)&&!P&&(W(b)||o&&Array.isArray(b))){const e=ce(l),t=ce(u),r=!x(e.value)&&b.length>+e.value,o=!x(t.value)&&b.length<+t.value;if((r||o)&&(L(r,e.message,t.message),!n))return E(S[m].message),S}if(f&&!P&&W(b)){const{value:e,message:t}=ce(f);if(re(e)&&!b.match(e)&&(S[m]={type:V,message:t,ref:i,..._(V,t)},!n))return E(t),S}if(p)if(J(p)){const e=ue(await p(b,t),w);if(e&&(S[m]={...e,..._("validate",e.message)},!n))return E(e.message),S}else if(k(p)){let e={};for(const r in p){if(!H(e)&&!n)break;const o=ue(await p[r](b,t),w,r);o&&(e={...o,..._(r,o.message)},E(o.message),n&&(S[m]=e))}if(!H(e)&&(S[m]={ref:w,...e},!n))return S}return E(!0),S};function fe(e,t){const n=Array.isArray(t)?t:$(t)?[t]:U(t),r=1===n.length?e:function(e,t){const n=t.slice(0,-1).length;let r=0;for(;r<n;)e=F(e)?r++:e[t[r++]];return e}(e,n),o=n.length-1,i=n[o];return r&&delete r[i],0!==o&&(k(r)&&H(r)||Array.isArray(r)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!F(e[t]))return!1;return!0}(r))&&fe(e,n.slice(0,-1)),e}var pe=()=>{let e=[];return{get observers(){return e},next:t=>{for(const n of e)n.next&&n.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},me=e=>x(e)||!E(e);function he(e,t){if(me(e)||me(t))return e===t;if(w(e)&&w(t))return e.getTime()===t.getTime();const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(const o of n){const n=e[o];if(!r.includes(o))return!1;if("ref"!==o){const e=t[o];if(w(n)&&w(e)||k(n)&&k(e)||Array.isArray(n)&&Array.isArray(e)?!he(n,e):n!==e)return!1}}return!0}var ve=e=>"select-multiple"===e.type,ge=e=>ee(e)&&e.isConnected,be=e=>{for(const t in e)if(J(e[t]))return!0;return!1};function ye(e,t={}){const n=Array.isArray(e);if(k(e)||n)for(const n in e)Array.isArray(e[n])||k(e[n])&&!be(e[n])?(t[n]=Array.isArray(e[n])?[]:{},ye(e[n],t[n])):x(e[n])||(t[n]=!0);return t}function we(e,t,n){const r=Array.isArray(e);if(k(e)||r)for(const r in e)Array.isArray(e[r])||k(e[r])&&!be(e[r])?F(t)||me(n[r])?n[r]=Array.isArray(e[r])?ye(e[r],[]):{...ye(e[r])}:we(e[r],x(t)?{}:t[r],n[r]):n[r]=!he(e[r],t[r]);return n}var xe=(e,t)=>we(e,t,ye(t)),Ee=(e,{valueAsNumber:t,valueAsDate:n,setValueAs:r})=>F(e)?e:t?""===e?NaN:e?+e:e:n&&W(e)?new Date(e):r?r(e):e;function ke(e){const t=e.ref;if(!(e.refs?e.refs.every((e=>e.disabled)):t.disabled))return Z(t)?t.files:ne(t)?le(e.refs).value:ve(t)?[...t.selectedOptions].map((({value:e})=>e)):y(t)?ae(e.refs).value:Ee(F(t.value)?e.ref.value:t.value,e)}var Se=e=>F(e)?e:re(e)?e.source:k(e)?re(e.value)?e.value.source:e.value:e;function Oe(e,t,n){const r=A(e,n);if(r||$(n))return{error:r,name:n};const o=n.split(".");for(;o.length;){const r=o.join("."),i=A(t,r),a=A(e,r);if(i&&!Array.isArray(i)&&n!==r)return{name:n};if(a&&a.type)return{name:r,error:a};o.pop()}return{name:n}}const Te={mode:I,reValidateMode:M,shouldFocusError:!0};function Ce(e={}){let t,n={...Te,...e},r={submitCount:0,isDirty:!1,isLoading:J(n.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:n.errors||{},disabled:n.disabled||!1},o={},i=(k(n.defaultValues)||k(n.values))&&T(n.defaultValues||n.values)||{},a=n.shouldUnregister?{}:T(i),s={action:!1,mount:!1,watch:!1},l={mount:new Set,unMount:new Set,array:new Set,watch:new Set},u=0;const c={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},d={values:pe(),array:pe(),state:pe()},f=K(n.mode),p=K(n.reValidateMode),m=n.criteriaMode===j,h=async e=>{if(c.isValid||e){const e=n.resolver?H((await E()).errors):await S(o,!0);e!==r.isValid&&d.state.next({isValid:e})}},v=(e,t)=>{(c.isValidating||c.validatingFields)&&((e||Array.from(l.mount)).forEach((e=>{e&&(t?q(r.validatingFields,e,t):fe(r.validatingFields,e))})),d.state.next({validatingFields:r.validatingFields,isValidating:!H(r.validatingFields)}))},g=(e,t,n,r)=>{const l=A(o,e);if(l){const o=A(a,e,F(n)?A(i,e):n);F(o)||r&&r.defaultChecked||t?q(a,e,t?o:ke(l._f)):M(e,o),s.mount&&h()}},b=(e,t,n,a,s)=>{let l=!1,u=!1;const f={name:e},p=!(!A(o,e)||!A(o,e)._f.disabled);if(!n||a){c.isDirty&&(u=r.isDirty,r.isDirty=f.isDirty=_(),l=u!==f.isDirty);const n=p||he(A(i,e),t);u=!(p||!A(r.dirtyFields,e)),n||p?fe(r.dirtyFields,e):q(r.dirtyFields,e,!0),f.dirtyFields=r.dirtyFields,l=l||c.dirtyFields&&u!==!n}if(n){const t=A(r.touchedFields,e);t||(q(r.touchedFields,e,n),f.touchedFields=r.touchedFields,l=l||c.touchedFields&&t!==n)}return l&&s&&d.state.next(f),l?f:{}},E=async e=>{v(e,!0);const t=await n.resolver(a,n.context,((e,t,n,r)=>{const o={};for(const n of e){const e=A(t,n);e&&q(o,n,e._f)}return{criteriaMode:n,names:[...e],fields:o,shouldUseNativeValidation:r}})(e||l.mount,o,n.criteriaMode,n.shouldUseNativeValidation));return v(e),t},S=async(e,t,o={valid:!0})=>{for(const i in e){const s=e[i];if(s){const{_f:e,...u}=s;if(e){const u=l.array.has(e.name);v([i],!0);const c=await de(s,a,m,n.shouldUseNativeValidation&&!t,u);if(v([i]),c[e.name]&&(o.valid=!1,t))break;!t&&(A(c,e.name)?u?Q(r.errors,c,e.name):q(r.errors,e.name,c[e.name]):fe(r.errors,e.name))}u&&await S(u,t,o)}}return o.valid},_=(e,t)=>(e&&t&&q(a,e,t),!he(U(),i)),N=(e,t,n)=>((e,t,n,r,o)=>W(e)?(r&&t.watch.add(e),A(n,e,o)):Array.isArray(e)?e.map((e=>(r&&t.watch.add(e),A(n,e)))):(r&&(t.watchAll=!0),n))(e,l,{...s.mount?a:F(t)?i:W(e)?{[e]:t}:t},n,t),M=(e,t,n={})=>{const r=A(o,e);let i=t;if(r){const n=r._f;n&&(!n.disabled&&q(a,e,Ee(t,n)),i=ee(n.ref)&&x(t)?"":t,ve(n.ref)?[...n.ref.options].forEach((e=>e.selected=i.includes(e.value))):n.refs?y(n.ref)?n.refs.length>1?n.refs.forEach((e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(i)?!!i.find((t=>t===e.value)):i===e.value))):n.refs[0]&&(n.refs[0].checked=!!i):n.refs.forEach((e=>e.checked=e.value===i)):Z(n.ref)?n.ref.value="":(n.ref.value=i,n.ref.type||d.values.next({name:e,values:{...a}})))}(n.shouldDirty||n.shouldTouch)&&b(e,i,n.shouldTouch,n.shouldDirty,!0),n.shouldValidate&&$(e)},I=(e,t,n)=>{for(const r in t){const i=t[r],a=`${e}.${r}`,s=A(o,a);!l.array.has(e)&&me(i)&&(!s||s._f)||w(i)?M(a,i,n):I(a,i,n)}},D=(e,t,n={})=>{const u=A(o,e),f=l.array.has(e),p=T(t);q(a,e,p),f?(d.array.next({name:e,values:{...a}}),(c.isDirty||c.dirtyFields)&&n.shouldDirty&&d.state.next({name:e,dirtyFields:xe(i,a),isDirty:_(e,p)})):!u||u._f||x(p)?M(e,p,n):I(e,p,n),Y(e,l)&&d.state.next({...r}),d.values.next({name:s.mount?e:void 0,values:{...a}})},V=async i=>{s.mount=!0;const g=i.target;let w=g.name,x=!0;const O=A(o,w),T=e=>{x=Number.isNaN(e)||e===A(a,w,e)};if(O){let s,F;const _=g.type?ke(O._f):(e=>k(e)&&e.target?y(e.target)?e.target.checked:e.target.value:e)(i),N=i.type===P||i.type===L,M=!((C=O._f).mount&&(C.required||C.min||C.max||C.maxLength||C.minLength||C.pattern||C.validate)||n.resolver||A(r.errors,w)||O._f.deps)||((e,t,n,r,o)=>!o.isOnAll&&(!n&&o.isOnTouch?!(t||e):(n?r.isOnBlur:o.isOnBlur)?!e:!(n?r.isOnChange:o.isOnChange)||e))(N,A(r.touchedFields,w),r.isSubmitted,p,f),I=Y(w,l,N);q(a,w,_),N?(O._f.onBlur&&O._f.onBlur(i),t&&t(0)):O._f.onChange&&O._f.onChange(i);const D=b(w,_,N,!1),j=!H(D)||I;if(!N&&d.values.next({name:w,type:i.type,values:{...a}}),M)return c.isValid&&h(),j&&d.state.next({name:w,...I?{}:D});if(!N&&I&&d.state.next({...r}),n.resolver){const{errors:e}=await E([w]);if(T(_),x){const t=Oe(r.errors,o,w),n=Oe(e,o,t.name||w);s=n.error,w=n.name,F=H(e)}}else v([w],!0),s=(await de(O,a,m,n.shouldUseNativeValidation))[w],v([w]),T(_),x&&(s?F=!1:c.isValid&&(F=await S(o,!0)));x&&(O._f.deps&&$(O._f.deps),((n,o,i,a)=>{const s=A(r.errors,n),l=c.isValid&&R(o)&&r.isValid!==o;var f;if(e.delayError&&i?(f=()=>((e,t)=>{q(r.errors,e,t),d.state.next({errors:r.errors})})(n,i),t=e=>{clearTimeout(u),u=setTimeout(f,e)},t(e.delayError)):(clearTimeout(u),t=null,i?q(r.errors,n,i):fe(r.errors,n)),(i?!he(s,i):s)||!H(a)||l){const e={...a,...l&&R(o)?{isValid:o}:{},errors:r.errors,name:n};r={...r,...e},d.state.next(e)}})(w,F,s,D))}var C},z=(e,t)=>{if(A(r.errors,t)&&e.focus)return e.focus(),1},$=async(e,t={})=>{let i,a;const s=B(e);if(n.resolver){const t=await(async e=>{const{errors:t}=await E(e);if(e)for(const n of e){const e=A(t,n);e?q(r.errors,n,e):fe(r.errors,n)}else r.errors=t;return t})(F(e)?e:s);i=H(t),a=e?!s.some((e=>A(t,e))):i}else e?(a=(await Promise.all(s.map((async e=>{const t=A(o,e);return await S(t&&t._f?{[e]:t}:t)})))).every(Boolean),(a||r.isValid)&&h()):a=i=await S(o);return d.state.next({...!W(e)||c.isValid&&i!==r.isValid?{}:{name:e},...n.resolver||!e?{isValid:i}:{},errors:r.errors}),t.shouldFocus&&!a&&X(o,z,e?s:l.mount),a},U=e=>{const t={...s.mount?a:i};return F(e)?t:W(e)?A(t,e):e.map((e=>A(t,e)))},G=(e,t)=>({invalid:!!A((t||r).errors,e),isDirty:!!A((t||r).dirtyFields,e),isTouched:!!A((t||r).touchedFields,e),isValidating:!!A((t||r).validatingFields,e),error:A((t||r).errors,e)}),te=(e,t,n)=>{const i=(A(o,e,{_f:{}})._f||{}).ref;q(r.errors,e,{...t,ref:i}),d.state.next({name:e,errors:r.errors,isValid:!1}),n&&n.shouldFocus&&i&&i.focus&&i.focus()},re=(e,t={})=>{for(const s of e?B(e):l.mount)l.mount.delete(s),l.array.delete(s),t.keepValue||(fe(o,s),fe(a,s)),!t.keepError&&fe(r.errors,s),!t.keepDirty&&fe(r.dirtyFields,s),!t.keepTouched&&fe(r.touchedFields,s),!t.keepIsValidating&&fe(r.validatingFields,s),!n.shouldUnregister&&!t.keepDefaultValue&&fe(i,s);d.values.next({values:{...a}}),d.state.next({...r,...t.keepDirty?{isDirty:_()}:{}}),!t.keepIsValid&&h()},oe=({disabled:e,name:t,field:n,fields:r,value:o})=>{if(R(e)){const i=e?void 0:F(o)?ke(n?n._f:A(r,t)._f):o;q(a,t,i),b(t,i,!1,!1,!0)}},ie=(e,t={})=>{let r=A(o,e);const a=R(t.disabled);return q(o,e,{...r||{},_f:{...r&&r._f?r._f:{ref:{name:e}},name:e,mount:!0,...t}}),l.mount.add(e),r?oe({field:r,disabled:t.disabled,name:e,value:t.value}):g(e,!0,t.value),{...a?{disabled:t.disabled}:{},...n.progressive?{required:!!t.required,min:Se(t.min),max:Se(t.max),minLength:Se(t.minLength),maxLength:Se(t.maxLength),pattern:Se(t.pattern)}:{},name:e,onChange:V,onBlur:V,ref:a=>{if(a){ie(e,t),r=A(o,e);const n=F(a.value)&&a.querySelectorAll&&a.querySelectorAll("input,select,textarea")[0]||a,s=(e=>ne(e)||y(e))(n),l=r._f.refs||[];if(s?l.find((e=>e===n)):n===r._f.ref)return;q(o,e,{_f:{...r._f,...s?{refs:[...l.filter(ge),n,...Array.isArray(A(i,e))?[{}]:[]],ref:{type:n.type,name:e}}:{ref:n}}}),g(e,!1,void 0,n)}else r=A(o,e,{}),r._f&&(r._f.mount=!1),(n.shouldUnregister||t.shouldUnregister)&&(!((e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)))(l.array,e)||!s.action)&&l.unMount.add(e)}}},ae=()=>n.shouldFocusError&&X(o,z,l.mount),se=(e,t)=>async i=>{let s;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let l=T(a);if(d.state.next({isSubmitting:!0}),n.resolver){const{errors:e,values:t}=await E();r.errors=e,l=t}else await S(o);if(fe(r.errors,"root"),H(r.errors)){d.state.next({errors:{}});try{await e(l,i)}catch(e){s=e}}else t&&await t({...r.errors},i),ae(),setTimeout(ae);if(d.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:H(r.errors)&&!s,submitCount:r.submitCount+1,errors:r.errors}),s)throw s},le=(t,n={})=>{const u=t?T(t):i,f=T(u),p=H(t),m=p?i:f;if(n.keepDefaultValues||(i=u),!n.keepValues){if(n.keepDirtyValues)for(const e of l.mount)A(r.dirtyFields,e)?q(m,e,A(a,e)):D(e,A(m,e));else{if(O&&F(t))for(const e of l.mount){const t=A(o,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(ee(e)){const t=e.closest("form");if(t){t.reset();break}}}}o={}}a=e.shouldUnregister?n.keepDefaultValues?T(i):{}:T(m),d.array.next({values:{...m}}),d.values.next({values:{...m}})}l={mount:n.keepDirtyValues?l.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},s.mount=!c.isValid||!!n.keepIsValid||!!n.keepDirtyValues,s.watch=!!e.shouldUnregister,d.state.next({submitCount:n.keepSubmitCount?r.submitCount:0,isDirty:!p&&(n.keepDirty?r.isDirty:!(!n.keepDefaultValues||he(t,i))),isSubmitted:!!n.keepIsSubmitted&&r.isSubmitted,dirtyFields:p?[]:n.keepDirtyValues?n.keepDefaultValues&&a?xe(i,a):r.dirtyFields:n.keepDefaultValues&&t?xe(i,t):{},touchedFields:n.keepTouched?r.touchedFields:{},errors:n.keepErrors?r.errors:{},isSubmitSuccessful:!!n.keepIsSubmitSuccessful&&r.isSubmitSuccessful,isSubmitting:!1})},ue=(e,t)=>le(J(e)?e(a):e,t);return{control:{register:ie,unregister:re,getFieldState:G,handleSubmit:se,setError:te,_executeSchema:E,_getWatch:N,_getDirty:_,_updateValid:h,_removeUnmounted:()=>{for(const e of l.unMount){const t=A(o,e);t&&(t._f.refs?t._f.refs.every((e=>!ge(e))):!ge(t._f.ref))&&re(e)}l.unMount=new Set},_updateFieldArray:(e,t=[],n,l,u=!0,f=!0)=>{if(l&&n){if(s.action=!0,f&&Array.isArray(A(o,e))){const t=n(A(o,e),l.argA,l.argB);u&&q(o,e,t)}if(f&&Array.isArray(A(r.errors,e))){const t=n(A(r.errors,e),l.argA,l.argB);u&&q(r.errors,e,t),((e,t)=>{!C(A(e,t)).length&&fe(e,t)})(r.errors,e)}if(c.touchedFields&&f&&Array.isArray(A(r.touchedFields,e))){const t=n(A(r.touchedFields,e),l.argA,l.argB);u&&q(r.touchedFields,e,t)}c.dirtyFields&&(r.dirtyFields=xe(i,a)),d.state.next({name:e,isDirty:_(e,t),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else q(a,e,t)},_updateDisabledField:oe,_getFieldArray:t=>C(A(s.mount?a:i,t,e.shouldUnregister?A(i,t,[]):[])),_reset:le,_resetDefaultValues:()=>J(n.defaultValues)&&n.defaultValues().then((e=>{ue(e,n.resetOptions),d.state.next({isLoading:!1})})),_updateFormState:e=>{r={...r,...e}},_disableForm:e=>{R(e)&&(d.state.next({disabled:e}),X(o,((t,n)=>{let r=e;const i=A(o,n);i&&R(i._f.disabled)&&(r||(r=i._f.disabled)),t.disabled=r}),0,!1))},_subjects:d,_proxyFormState:c,_setErrors:e=>{r.errors=e,d.state.next({errors:r.errors,isValid:!1})},get _fields(){return o},get _formValues(){return a},get _state(){return s},set _state(e){s=e},get _defaultValues(){return i},get _names(){return l},set _names(e){l=e},get _formState(){return r},set _formState(e){r=e},get _options(){return n},set _options(e){n={...n,...e}}},trigger:$,register:ie,handleSubmit:se,watch:(e,t)=>J(e)?d.values.subscribe({next:n=>e(N(void 0,t),n)}):N(e,t,!0),setValue:D,getValues:U,reset:ue,resetField:(e,t={})=>{A(o,e)&&(F(t.defaultValue)?D(e,T(A(i,e))):(D(e,t.defaultValue),q(i,e,T(t.defaultValue))),t.keepTouched||fe(r.touchedFields,e),t.keepDirty||(fe(r.dirtyFields,e),r.isDirty=t.defaultValue?_(e,T(A(i,e))):_()),t.keepError||(fe(r.errors,e),c.isValid&&h()),d.state.next({...r}))},clearErrors:e=>{e&&B(e).forEach((e=>fe(r.errors,e))),d.state.next({errors:e?r.errors:{}})},unregister:re,setError:te,setFocus:(e,t={})=>{const n=A(o,e),r=n&&n._f;if(r){const e=r.refs?r.refs[0]:r.ref;e.focus&&(e.focus(),t.shouldSelect&&e.select())}},getFieldState:G}}function Fe(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Fe(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}const Ae="-";function Re(e){const t=function(e){const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]},o=function(e,t){return t?e.map((([e,n])=>[e,n.map((e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map((([e,n])=>[t+e,n]))):e))])):e}(Object.entries(e.classGroups),n);return o.forEach((([e,n])=>{Le(n,r,e,t)})),r}(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:function(e){const n=e.split(Ae);return""===n[0]&&1!==n.length&&n.shift(),Pe(n,t)||function(e){if(_e.test(e)){const t=_e.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}(e)},getConflictingClassGroupIds:function(e,t){const o=n[e]||[];return t&&r[e]?[...o,...r[e]]:o}}}function Pe(e,t){if(0===e.length)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Pe(e.slice(1),r):void 0;if(o)return o;if(0===t.validators.length)return;const i=e.join(Ae);return t.validators.find((({validator:e})=>e(i)))?.classGroupId}const _e=/^\[(.+)\]$/;function Le(e,t,n,r){e.forEach((e=>{if("string"!=typeof e){if("function"==typeof e)return e.isThemeGetter?void Le(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach((([e,o])=>{Le(o,Ne(t,e),n,r)}))}else(""===e?t:Ne(t,e)).classGroupId=n}))}function Ne(e,t){let n=e;return t.split(Ae).forEach((e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)})),n}function Me(e){if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;function o(o,i){n.set(o,i),t++,t>e&&(t=0,r=n,n=new Map)}return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(o(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):o(e,t)}}}const Ie="!";function De(e){const t=e.separator,n=1===t.length,r=t[0],o=t.length;return function(e){const i=[];let a,s=0,l=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===s){if(c===r&&(n||e.slice(u,u+o)===t)){i.push(e.slice(l,u)),l=u+o;continue}if("/"===c){a=u;continue}}"["===c?s++:"]"===c&&s--}const u=0===i.length?e:e.substring(l),c=u.startsWith(Ie);return{modifiers:i,hasImportantModifier:c,baseClassName:c?u.substring(1):u,maybePostfixModifierPosition:a&&a>l?a-l:void 0}}}const je=/\s+/;function Ve(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=ze(e))&&(r&&(r+=" "),r+=t);return r}function ze(e){if("string"==typeof e)return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=ze(e[r]))&&(n&&(n+=" "),n+=t);return n}function He(e,...t){let n,r,o,i=function(s){const l=t.reduce(((e,t)=>t(e)),e());return n=function(e){return{cache:Me(e.cacheSize),splitModifiers:De(e),...Re(e)}}(l),r=n.cache.get,o=n.cache.set,i=a,a(s)};function a(e){const t=r(e);if(t)return t;const i=function(e,t){const{splitModifiers:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=new Set;return e.trim().split(je).map((e=>{const{modifiers:t,hasImportantModifier:o,baseClassName:i,maybePostfixModifierPosition:a}=n(e);let s=r(a?i.substring(0,a):i),l=Boolean(a);if(!s){if(!a)return{isTailwindClass:!1,originalClassName:e};if(s=r(i),!s)return{isTailwindClass:!1,originalClassName:e};l=!1}const u=function(e){if(e.length<=1)return e;const t=[];let n=[];return e.forEach((e=>{"["===e[0]?(t.push(...n.sort(),e),n=[]):n.push(e)})),t.push(...n.sort()),t}(t).join(":");return{isTailwindClass:!0,modifierId:o?u+Ie:u,classGroupId:s,originalClassName:e,hasPostfixModifier:l}})).reverse().filter((e=>{if(!e.isTailwindClass)return!0;const{modifierId:t,classGroupId:n,hasPostfixModifier:r}=e,a=t+n;return!i.has(a)&&(i.add(a),o(n,r).forEach((e=>i.add(t+e))),!0)})).reverse().map((e=>e.originalClassName)).join(" ")}(e,n);return o(e,i),i}return function(){return i(Ve.apply(null,arguments))}}function Be(e){const t=t=>t[e]||[];return t.isThemeGetter=!0,t}const We=/^\[(?:([a-z-]+):)?(.+)\]$/i,$e=/^\d+\/\d+$/,Ue=new Set(["px","full","screen"]),qe=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ge=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Ke=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Ye=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Xe=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function Qe(e){return Je(e)||Ue.has(e)||$e.test(e)}function Ze(e){return ft(e,"length",pt)}function Je(e){return Boolean(e)&&!Number.isNaN(Number(e))}function et(e){return ft(e,"number",Je)}function tt(e){return Boolean(e)&&Number.isInteger(Number(e))}function nt(e){return e.endsWith("%")&&Je(e.slice(0,-1))}function rt(e){return We.test(e)}function ot(e){return qe.test(e)}const it=new Set(["length","size","percentage"]);function at(e){return ft(e,it,mt)}function st(e){return ft(e,"position",mt)}const lt=new Set(["image","url"]);function ut(e){return ft(e,lt,vt)}function ct(e){return ft(e,"",ht)}function dt(){return!0}function ft(e,t,n){const r=We.exec(e);return!!r&&(r[1]?"string"==typeof t?r[1]===t:t.has(r[1]):n(r[2]))}function pt(e){return Ge.test(e)&&!Ke.test(e)}function mt(){return!1}function ht(e){return Ye.test(e)}function vt(e){return Xe.test(e)}function gt(){const e=Be("colors"),t=Be("spacing"),n=Be("blur"),r=Be("brightness"),o=Be("borderColor"),i=Be("borderRadius"),a=Be("borderSpacing"),s=Be("borderWidth"),l=Be("contrast"),u=Be("grayscale"),c=Be("hueRotate"),d=Be("invert"),f=Be("gap"),p=Be("gradientColorStops"),m=Be("gradientColorStopPositions"),h=Be("inset"),v=Be("margin"),g=Be("opacity"),b=Be("padding"),y=Be("saturate"),w=Be("scale"),x=Be("sepia"),E=Be("skew"),k=Be("space"),S=Be("translate"),O=()=>["auto",rt,t],T=()=>[rt,t],C=()=>["",Qe,Ze],F=()=>["auto",Je,rt],A=()=>["","0",rt],R=()=>[Je,et],P=()=>[Je,rt];return{cacheSize:500,separator:":",theme:{colors:[dt],spacing:[Qe,Ze],blur:["none","",ot,rt],brightness:R(),borderColor:[e],borderRadius:["none","","full",ot,rt],borderSpacing:T(),borderWidth:C(),contrast:R(),grayscale:A(),hueRotate:P(),invert:A(),gap:T(),gradientColorStops:[e],gradientColorStopPositions:[nt,Ze],inset:O(),margin:O(),opacity:R(),padding:T(),saturate:R(),scale:R(),sepia:A(),skew:P(),space:T(),translate:T()},classGroups:{aspect:[{aspect:["auto","square","video",rt]}],container:["container"],columns:[{columns:[ot]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",rt]}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",tt,rt]}],basis:[{basis:O()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",rt]}],grow:[{grow:A()}],shrink:[{shrink:A()}],order:[{order:["first","last","none",tt,rt]}],"grid-cols":[{"grid-cols":[dt]}],"col-start-end":[{col:["auto",{span:["full",tt,rt]},rt]}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":[dt]}],"row-start-end":[{row:["auto",{span:[tt,rt]},rt]}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",rt]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",rt]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal","start","end","center","between","around","evenly","stretch"]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",rt,t]}],"min-w":[{"min-w":[rt,t,"min","max","fit"]}],"max-w":[{"max-w":[rt,t,"none","full","min","max","fit","prose",{screen:[ot]},ot]}],h:[{h:[rt,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[rt,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[rt,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[rt,t,"auto","min","max","fit"]}],"font-size":[{text:["base",ot,Ze]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",et]}],"font-family":[{font:[dt]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",rt]}],"line-clamp":[{"line-clamp":["none",Je,et]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Qe,rt]}],"list-image":[{"list-image":["none",rt]}],"list-style-type":[{list:["none","disc","decimal",rt]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","none","wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Qe,Ze]}],"underline-offset":[{"underline-offset":["auto",Qe,rt]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",rt]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",rt]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",st]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",at]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},ut]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:["solid","dashed","dotted","double","none","hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["","solid","dashed","dotted","double","none"]}],"outline-offset":[{"outline-offset":[Qe,rt]}],"outline-w":[{outline:[Qe,Ze]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:C()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[Qe,Ze]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",ot,ct]}],"shadow-color":[{shadow:[dt]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",ot,rt]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[y]}],sepia:[{sepia:[x]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",rt]}],duration:[{duration:P()}],ease:[{ease:["linear","in","out","in-out",rt]}],delay:[{delay:P()}],animate:[{animate:["none","spin","ping","pulse","bounce",rt]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[tt,rt]}],"translate-x":[{"translate-x":[S]}],"translate-y":[{"translate-y":[S]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",rt]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",rt]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",rt]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Qe,Ze,et]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}Symbol.toStringTag;const bt=He(gt),yt=(...e)=>bt(function(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Fe(e))&&(r&&(r+=" "),r+=t);return r}(e)),wt=(()=>{let e;return Object.seal({get:()=>e,set:t=>{e=t}})})(),xt=e=>{try{return new URL(e).pathname.split("/").pop()}catch(e){return""}},Et=async({id:e,url:t,name:n})=>{const r=wt.get();var o;if(r)try{const i=new FormData;i.append("action","zipwp_images_insert_image"),i.append("id",e),i.append("url",t),i.append("name",null!=n?n:xt(t)),i.append("_ajax_nonce",zipwpImages._ajax_nonce);const a=await fetch(zipwpImages.ajaxurl,{method:"POST",body:i});if(200!==a?.status)return void console.error(a);o=e,window?.zipwpImages?.saved_images&&window.zipwpImages.saved_images.push(o);const s=await a.json();r.model.frame.content.mode("browse"),r.model.get("selection").add(s.data.attachmentData),r.model.frame.trigger("library:selection:add"),r.model.get("selection"),r.controller.el.querySelector(".media-button-select").click()}catch(e){console.error(e)}},kt=({className:e,name:t,prefixIcon:n,suffixIcon:o,register:i,validations:a,error:s,...l})=>(0,r.createElement)("div",{className:"space-y-1"},(0,r.createElement)("div",{className:yt("relative flex items-center justify-start h-10 border border-solid border-border-primary shadow-sm rounded focus-within:border-accent focus-within:ring-1 focus-within:ring-accent",!!s&&"border-alert-error",e)},!!n&&n,(0,r.createElement)("input",{type:"text",className:yt("h-full !py-2 w-full px-3 !rounded-md !outline-none !text-base placeholder:!text-base placeholder:!text-zip-app-inactive-icon !bg-transparent disabled:!cursor-not-allowed !border-0 focus:!ring-0 focus:!outline-none"),...l,...i(t,a),"aria-invalid":!!s}),!!o&&o),!!s&&(0,r.createElement)("p",{className:"m-0 p-0 text-sm text-alert-error"},s.message)),St=r.forwardRef((function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})),Ot=r.forwardRef((function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})),Tt=r.forwardRef((function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"}))})),Ct=r.forwardRef((function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 8.25H7.5a2.25 2.25 0 0 0-2.25 2.25v9a2.25 2.25 0 0 0 2.25 2.25h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25H15M9 12l3 3m0 0 3-3m-3 3V2.25"}))})),Ft=window.wp.i18n,At=window.wp.apiFetch;var Rt=n.n(At),Pt=n(848),_t=[640,786,1024,1280,1536],Lt=(e,t)=>{let n=Object.keys((e=>Array.isArray(e)?(e=>e.reduce(((e,t,n)=>"number"!=typeof t?e:{...e,[_t[n]]:t}),{}))(e):e)(e)).map(Number).sort(((e,t)=>e-t)),r=null;for(let e of n)t>e&&(r=e);return null!=r?r:n[0]},Nt=e=>{let t="object"==typeof e,n=((e=!0)=>{let[t,n]=(0,r.useState)(window.innerWidth),o=(0,r.useCallback)((()=>{n(window.innerWidth)}),[]);return(0,r.useEffect)((()=>(e?window.addEventListener("resize",o):window.removeEventListener("resize",o),()=>{window.removeEventListener("resize",o)})),[e,o]),t})(t);return(0,r.useMemo)((()=>{var r;if(!t)return null!=e?e:3;let o=Lt(e,n);return null!=(r=e[o])?r:3}),[t,n,e])},Mt=(0,r.createContext)({column:0,position:0}),It=(0,r.forwardRef)(((e,t)=>{let{gap:n,as:o="div",columnProps:i,columns:a,...s}=e,l=(0,r.useId)(),u=((e,t)=>{let n=Nt(t);return(0,r.useMemo)((()=>{let t=(e=>Array.from({length:e},(()=>[])))(n);return r.Children.forEach(e,((e,o)=>{(0,r.isValidElement)(e)&&t[o%n].push(e)})),t}),[n,e])})(e.children,a);return(0,Pt.jsx)(o,{"data-masonry-id":`Masonry-${l}`,...s,style:{display:"flex",gap:n,...s.style},ref:t,children:u.map(((e,t)=>(0,Pt.jsx)(o,{"data-masonry-column":t+1,...i,style:{display:"flex",flex:1,flexDirection:"column",gap:n,...null==i?void 0:i.style},children:e.map(((e,n)=>(0,Pt.jsx)(Mt.Provider,{value:{column:t,position:n},children:e},`Masonry__Column_Child_${l}_${n}`)))},`Masonry__Column_${l}_${t}`)))})})),Dt=It;function jt(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Vt(e){return e instanceof jt(e).Element||e instanceof Element}function zt(e){return e instanceof jt(e).HTMLElement||e instanceof HTMLElement}function Ht(e){return"undefined"!=typeof ShadowRoot&&(e instanceof jt(e).ShadowRoot||e instanceof ShadowRoot)}var Bt=Math.max,Wt=Math.min,$t=Math.round;function Ut(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function qt(){return!/^((?!chrome|android).)*safari/i.test(Ut())}function Gt(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,i=1;t&&zt(e)&&(o=e.offsetWidth>0&&$t(r.width)/e.offsetWidth||1,i=e.offsetHeight>0&&$t(r.height)/e.offsetHeight||1);var a=(Vt(e)?jt(e):window).visualViewport,s=!qt()&&n,l=(r.left+(s&&a?a.offsetLeft:0))/o,u=(r.top+(s&&a?a.offsetTop:0))/i,c=r.width/o,d=r.height/i;return{width:c,height:d,top:u,right:l+c,bottom:u+d,left:l,x:l,y:u}}function Kt(e){var t=jt(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Yt(e){return e?(e.nodeName||"").toLowerCase():null}function Xt(e){return((Vt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Qt(e){return Gt(Xt(e)).left+Kt(e).scrollLeft}function Zt(e){return jt(e).getComputedStyle(e)}function Jt(e){var t=Zt(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function en(e,t,n){void 0===n&&(n=!1);var r,o,i=zt(t),a=zt(t)&&function(e){var t=e.getBoundingClientRect(),n=$t(t.width)/e.offsetWidth||1,r=$t(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),s=Xt(t),l=Gt(e,a,n),u={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(i||!i&&!n)&&(("body"!==Yt(t)||Jt(s))&&(u=(r=t)!==jt(r)&&zt(r)?{scrollLeft:(o=r).scrollLeft,scrollTop:o.scrollTop}:Kt(r)),zt(t)?((c=Gt(t,!0)).x+=t.clientLeft,c.y+=t.clientTop):s&&(c.x=Qt(s))),{x:l.left+u.scrollLeft-c.x,y:l.top+u.scrollTop-c.y,width:l.width,height:l.height}}function tn(e){var t=Gt(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function nn(e){return"html"===Yt(e)?e:e.assignedSlot||e.parentNode||(Ht(e)?e.host:null)||Xt(e)}function rn(e){return["html","body","#document"].indexOf(Yt(e))>=0?e.ownerDocument.body:zt(e)&&Jt(e)?e:rn(nn(e))}function on(e,t){var n;void 0===t&&(t=[]);var r=rn(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),i=jt(r),a=o?[i].concat(i.visualViewport||[],Jt(r)?r:[]):r,s=t.concat(a);return o?s:s.concat(on(nn(a)))}function an(e){return["table","td","th"].indexOf(Yt(e))>=0}function sn(e){return zt(e)&&"fixed"!==Zt(e).position?e.offsetParent:null}function ln(e){for(var t=jt(e),n=sn(e);n&&an(n)&&"static"===Zt(n).position;)n=sn(n);return n&&("html"===Yt(n)||"body"===Yt(n)&&"static"===Zt(n).position)?t:n||function(e){var t=/firefox/i.test(Ut());if(/Trident/i.test(Ut())&&zt(e)&&"fixed"===Zt(e).position)return null;var n=nn(e);for(Ht(n)&&(n=n.host);zt(n)&&["html","body"].indexOf(Yt(n))<0;){var r=Zt(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}var un="top",cn="bottom",dn="right",fn="left",pn="auto",mn=[un,cn,dn,fn],hn="start",vn="end",gn="viewport",bn="popper",yn=mn.reduce((function(e,t){return e.concat([t+"-"+hn,t+"-"+vn])}),[]),wn=[].concat(mn,[pn]).reduce((function(e,t){return e.concat([t,t+"-"+hn,t+"-"+vn])}),[]),xn=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function En(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||o(e)})),r}var kn={placement:"bottom",modifiers:[],strategy:"absolute"};function Sn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function On(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,i=void 0===o?kn:o;return function(e,t,n){void 0===n&&(n=i);var o,a,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},kn,i),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],u=!1,c={state:s,setOptions:function(n){var o="function"==typeof n?n(s.options):n;d(),s.options=Object.assign({},i,s.options,o),s.scrollParents={reference:Vt(e)?on(e):e.contextElement?on(e.contextElement):[],popper:on(t)};var a,u,f=function(e){var t=En(e);return xn.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((a=[].concat(r,s.options.modifiers),u=a.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(u).map((function(e){return u[e]}))));return s.orderedModifiers=f.filter((function(e){return e.enabled})),s.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,o=e.effect;if("function"==typeof o){var i=o({state:s,name:t,instance:c,options:r});l.push(i||function(){})}})),c.update()},forceUpdate:function(){if(!u){var e=s.elements,t=e.reference,n=e.popper;if(Sn(t,n)){s.rects={reference:en(t,ln(n),"fixed"===s.options.strategy),popper:tn(n)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach((function(e){return s.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<s.orderedModifiers.length;r++)if(!0!==s.reset){var o=s.orderedModifiers[r],i=o.fn,a=o.options,l=void 0===a?{}:a,d=o.name;"function"==typeof i&&(s=i({state:s,options:l,name:d,instance:c})||s)}else s.reset=!1,r=-1}}},update:(o=function(){return new Promise((function(e){c.forceUpdate(),e(s)}))},function(){return a||(a=new Promise((function(e){Promise.resolve().then((function(){a=void 0,e(o())}))}))),a}),destroy:function(){d(),u=!0}};if(!Sn(e,t))return c;function d(){l.forEach((function(e){return e()})),l=[]}return c.setOptions(n).then((function(e){!u&&n.onFirstUpdate&&n.onFirstUpdate(e)})),c}}var Tn={passive:!0};function Cn(e){return e.split("-")[0]}function Fn(e){return e.split("-")[1]}function An(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Rn(e){var t,n=e.reference,r=e.element,o=e.placement,i=o?Cn(o):null,a=o?Fn(o):null,s=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(i){case un:t={x:s,y:n.y-r.height};break;case cn:t={x:s,y:n.y+n.height};break;case dn:t={x:n.x+n.width,y:l};break;case fn:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var u=i?An(i):null;if(null!=u){var c="y"===u?"height":"width";switch(a){case hn:t[u]=t[u]-(n[c]/2-r[c]/2);break;case vn:t[u]=t[u]+(n[c]/2-r[c]/2)}}return t}var Pn={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ln(e){var t,n=e.popper,r=e.popperRect,o=e.placement,i=e.variation,a=e.offsets,s=e.position,l=e.gpuAcceleration,u=e.adaptive,c=e.roundOffsets,d=e.isFixed,f=a.x,p=void 0===f?0:f,m=a.y,h=void 0===m?0:m,v="function"==typeof c?c({x:p,y:h}):{x:p,y:h};p=v.x,h=v.y;var g=a.hasOwnProperty("x"),b=a.hasOwnProperty("y"),y=fn,w=un,x=window;if(u){var E=ln(n),k="clientHeight",S="clientWidth";E===jt(n)&&"static"!==Zt(E=Xt(n)).position&&"absolute"===s&&(k="scrollHeight",S="scrollWidth"),(o===un||(o===fn||o===dn)&&i===vn)&&(w=cn,h-=(d&&E===x&&x.visualViewport?x.visualViewport.height:E[k])-r.height,h*=l?1:-1),o!==fn&&(o!==un&&o!==cn||i!==vn)||(y=dn,p-=(d&&E===x&&x.visualViewport?x.visualViewport.width:E[S])-r.width,p*=l?1:-1)}var O,T=Object.assign({position:s},u&&Pn),C=!0===c?function(e,t){var n=e.x,r=e.y,o=t.devicePixelRatio||1;return{x:$t(n*o)/o||0,y:$t(r*o)/o||0}}({x:p,y:h},jt(n)):{x:p,y:h};return p=C.x,h=C.y,l?Object.assign({},T,((O={})[w]=b?"0":"",O[y]=g?"0":"",O.transform=(x.devicePixelRatio||1)<=1?"translate("+p+"px, "+h+"px)":"translate3d("+p+"px, "+h+"px, 0)",O)):Object.assign({},T,((t={})[w]=b?h+"px":"",t[y]=g?p+"px":"",t.transform="",t))}const Nn={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];zt(o)&&Yt(o)&&(Object.assign(o.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],o=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});zt(r)&&Yt(r)&&(Object.assign(r.style,i),Object.keys(o).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]},Mn={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,i=void 0===o?[0,0]:o,a=wn.reduce((function(e,n){return e[n]=function(e,t,n){var r=Cn(e),o=[fn,un].indexOf(r)>=0?-1:1,i="function"==typeof n?n(Object.assign({},t,{placement:e})):n,a=i[0],s=i[1];return a=a||0,s=(s||0)*o,[fn,dn].indexOf(r)>=0?{x:s,y:a}:{x:a,y:s}}(n,t.rects,i),e}),{}),s=a[t.placement],l=s.x,u=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=a}};var In={left:"right",right:"left",bottom:"top",top:"bottom"};function Dn(e){return e.replace(/left|right|bottom|top/g,(function(e){return In[e]}))}var jn={start:"end",end:"start"};function Vn(e){return e.replace(/start|end/g,(function(e){return jn[e]}))}function zn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Ht(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Hn(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Bn(e,t,n){return t===gn?Hn(function(e,t){var n=jt(e),r=Xt(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,s=0,l=0;if(o){i=o.width,a=o.height;var u=qt();(u||!u&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:i,height:a,x:s+Qt(e),y:l}}(e,n)):Vt(t)?function(e,t){var n=Gt(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):Hn(function(e){var t,n=Xt(e),r=Kt(e),o=null==(t=e.ownerDocument)?void 0:t.body,i=Bt(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),a=Bt(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),s=-r.scrollLeft+Qt(e),l=-r.scrollTop;return"rtl"===Zt(o||n).direction&&(s+=Bt(n.clientWidth,o?o.clientWidth:0)-i),{width:i,height:a,x:s,y:l}}(Xt(e)))}function Wn(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function $n(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}function Un(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=void 0===r?e.placement:r,i=n.strategy,a=void 0===i?e.strategy:i,s=n.boundary,l=void 0===s?"clippingParents":s,u=n.rootBoundary,c=void 0===u?gn:u,d=n.elementContext,f=void 0===d?bn:d,p=n.altBoundary,m=void 0!==p&&p,h=n.padding,v=void 0===h?0:h,g=Wn("number"!=typeof v?v:$n(v,mn)),b=f===bn?"reference":bn,y=e.rects.popper,w=e.elements[m?b:f],x=function(e,t,n,r){var o="clippingParents"===t?function(e){var t=on(nn(e)),n=["absolute","fixed"].indexOf(Zt(e).position)>=0&&zt(e)?ln(e):e;return Vt(n)?t.filter((function(e){return Vt(e)&&zn(e,n)&&"body"!==Yt(e)})):[]}(e):[].concat(t),i=[].concat(o,[n]),a=i[0],s=i.reduce((function(t,n){var o=Bn(e,n,r);return t.top=Bt(o.top,t.top),t.right=Wt(o.right,t.right),t.bottom=Wt(o.bottom,t.bottom),t.left=Bt(o.left,t.left),t}),Bn(e,a,r));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}(Vt(w)?w:w.contextElement||Xt(e.elements.popper),l,c,a),E=Gt(e.elements.reference),k=Rn({reference:E,element:y,strategy:"absolute",placement:o}),S=Hn(Object.assign({},y,k)),O=f===bn?S:E,T={top:x.top-O.top+g.top,bottom:O.bottom-x.bottom+g.bottom,left:x.left-O.left+g.left,right:O.right-x.right+g.right},C=e.modifiersData.offset;if(f===bn&&C){var F=C[o];Object.keys(T).forEach((function(e){var t=[dn,cn].indexOf(e)>=0?1:-1,n=[un,cn].indexOf(e)>=0?"y":"x";T[e]+=F[n]*t}))}return T}const qn={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,i=void 0===o||o,a=n.altAxis,s=void 0===a||a,l=n.fallbackPlacements,u=n.padding,c=n.boundary,d=n.rootBoundary,f=n.altBoundary,p=n.flipVariations,m=void 0===p||p,h=n.allowedAutoPlacements,v=t.options.placement,g=Cn(v),b=l||(g!==v&&m?function(e){if(Cn(e)===pn)return[];var t=Dn(e);return[Vn(e),t,Vn(t)]}(v):[Dn(v)]),y=[v].concat(b).reduce((function(e,n){return e.concat(Cn(n)===pn?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,i=n.rootBoundary,a=n.padding,s=n.flipVariations,l=n.allowedAutoPlacements,u=void 0===l?wn:l,c=Fn(r),d=c?s?yn:yn.filter((function(e){return Fn(e)===c})):mn,f=d.filter((function(e){return u.indexOf(e)>=0}));0===f.length&&(f=d);var p=f.reduce((function(t,n){return t[n]=Un(e,{placement:n,boundary:o,rootBoundary:i,padding:a})[Cn(n)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}(t,{placement:n,boundary:c,rootBoundary:d,padding:u,flipVariations:m,allowedAutoPlacements:h}):n)}),[]),w=t.rects.reference,x=t.rects.popper,E=new Map,k=!0,S=y[0],O=0;O<y.length;O++){var T=y[O],C=Cn(T),F=Fn(T)===hn,A=[un,cn].indexOf(C)>=0,R=A?"width":"height",P=Un(t,{placement:T,boundary:c,rootBoundary:d,altBoundary:f,padding:u}),_=A?F?dn:fn:F?cn:un;w[R]>x[R]&&(_=Dn(_));var L=Dn(_),N=[];if(i&&N.push(P[C]<=0),s&&N.push(P[_]<=0,P[L]<=0),N.every((function(e){return e}))){S=T,k=!1;break}E.set(T,N)}if(k)for(var M=function(e){var t=y.find((function(t){var n=E.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return S=t,"break"},I=m?3:1;I>0&&"break"!==M(I);I--);t.placement!==S&&(t.modifiersData[r]._skip=!0,t.placement=S,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Gn(e,t,n){return Bt(e,Wt(t,n))}function Kn(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Yn(e){return[un,dn,cn,fn].some((function(t){return e[t]>=0}))}var Xn=On({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,i=void 0===o||o,a=r.resize,s=void 0===a||a,l=jt(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&u.forEach((function(e){e.addEventListener("scroll",n.update,Tn)})),s&&l.addEventListener("resize",n.update,Tn),function(){i&&u.forEach((function(e){e.removeEventListener("scroll",n.update,Tn)})),s&&l.removeEventListener("resize",n.update,Tn)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Rn({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,i=n.adaptive,a=void 0===i||i,s=n.roundOffsets,l=void 0===s||s,u={placement:Cn(t.placement),variation:Fn(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Ln(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Ln(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},Nn,Mn,qn,{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,i=void 0===o||o,a=n.altAxis,s=void 0!==a&&a,l=n.boundary,u=n.rootBoundary,c=n.altBoundary,d=n.padding,f=n.tether,p=void 0===f||f,m=n.tetherOffset,h=void 0===m?0:m,v=Un(t,{boundary:l,rootBoundary:u,padding:d,altBoundary:c}),g=Cn(t.placement),b=Fn(t.placement),y=!b,w=An(g),x="x"===w?"y":"x",E=t.modifiersData.popperOffsets,k=t.rects.reference,S=t.rects.popper,O="function"==typeof h?h(Object.assign({},t.rects,{placement:t.placement})):h,T="number"==typeof O?{mainAxis:O,altAxis:O}:Object.assign({mainAxis:0,altAxis:0},O),C=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,F={x:0,y:0};if(E){if(i){var A,R="y"===w?un:fn,P="y"===w?cn:dn,_="y"===w?"height":"width",L=E[w],N=L+v[R],M=L-v[P],I=p?-S[_]/2:0,D=b===hn?k[_]:S[_],j=b===hn?-S[_]:-k[_],V=t.elements.arrow,z=p&&V?tn(V):{width:0,height:0},H=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},B=H[R],W=H[P],$=Gn(0,k[_],z[_]),U=y?k[_]/2-I-$-B-T.mainAxis:D-$-B-T.mainAxis,q=y?-k[_]/2+I+$+W+T.mainAxis:j+$+W+T.mainAxis,G=t.elements.arrow&&ln(t.elements.arrow),K=G?"y"===w?G.clientTop||0:G.clientLeft||0:0,Y=null!=(A=null==C?void 0:C[w])?A:0,X=L+q-Y,Q=Gn(p?Wt(N,L+U-Y-K):N,L,p?Bt(M,X):M);E[w]=Q,F[w]=Q-L}if(s){var Z,J="x"===w?un:fn,ee="x"===w?cn:dn,te=E[x],ne="y"===x?"height":"width",re=te+v[J],oe=te-v[ee],ie=-1!==[un,fn].indexOf(g),ae=null!=(Z=null==C?void 0:C[x])?Z:0,se=ie?re:te-k[ne]-S[ne]-ae+T.altAxis,le=ie?te+k[ne]+S[ne]-ae-T.altAxis:oe,ue=p&&ie?function(e,t,n){var r=Gn(e,t,n);return r>n?n:r}(se,te,le):Gn(p?se:re,te,p?le:oe);E[x]=ue,F[x]=ue-te}t.modifiersData[r]=F}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,s=Cn(n.placement),l=An(s),u=[fn,dn].indexOf(s)>=0?"height":"width";if(i&&a){var c=function(e,t){return Wn("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:$n(e,mn))}(o.padding,n),d=tn(i),f="y"===l?un:fn,p="y"===l?cn:dn,m=n.rects.reference[u]+n.rects.reference[l]-a[l]-n.rects.popper[u],h=a[l]-n.rects.reference[l],v=ln(i),g=v?"y"===l?v.clientHeight||0:v.clientWidth||0:0,b=m/2-h/2,y=c[f],w=g-d[u]-c[p],x=g/2-d[u]/2+b,E=Gn(y,x,w),k=l;n.modifiersData[r]=((t={})[k]=E,t.centerOffset=E-x,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&zn(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=Un(t,{elementContext:"reference"}),s=Un(t,{altBoundary:!0}),l=Kn(a,r),u=Kn(s,o,i),c=Yn(l),d=Yn(u);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":d})}}]}),Qn="tippy-content",Zn="tippy-backdrop",Jn="tippy-arrow",er="tippy-svg-arrow",tr={passive:!0,capture:!0},nr=function(){return document.body};function rr(e,t,n){if(Array.isArray(e)){var r=e[t];return null==r?Array.isArray(n)?n[t]:n:r}return e}function or(e,t){var n={}.toString.call(e);return 0===n.indexOf("[object")&&n.indexOf(t+"]")>-1}function ir(e,t){return"function"==typeof e?e.apply(void 0,t):e}function ar(e,t){return 0===t?e:function(r){clearTimeout(n),n=setTimeout((function(){e(r)}),t)};var n}function sr(e){return[].concat(e)}function lr(e,t){-1===e.indexOf(t)&&e.push(t)}function ur(e){return[].slice.call(e)}function cr(e){return Object.keys(e).reduce((function(t,n){return void 0!==e[n]&&(t[n]=e[n]),t}),{})}function dr(){return document.createElement("div")}function fr(e){return["Element","Fragment"].some((function(t){return or(e,t)}))}function pr(e,t){e.forEach((function(e){e&&(e.style.transitionDuration=t+"ms")}))}function mr(e,t){e.forEach((function(e){e&&e.setAttribute("data-state",t)}))}function hr(e,t,n){var r=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(t){e[r](t,n)}))}function vr(e,t){for(var n=t;n;){var r;if(e.contains(n))return!0;n=null==n.getRootNode||null==(r=n.getRootNode())?void 0:r.host}return!1}var gr={isTouch:!1},br=0;function yr(){gr.isTouch||(gr.isTouch=!0,window.performance&&document.addEventListener("mousemove",wr))}function wr(){var e=performance.now();e-br<20&&(gr.isTouch=!1,document.removeEventListener("mousemove",wr)),br=e}function xr(){var e,t=document.activeElement;if((e=t)&&e._tippy&&e._tippy.reference===e){var n=t._tippy;t.blur&&!n.state.isVisible&&t.blur()}}var Er=!("undefined"==typeof window||"undefined"==typeof document||!window.msCrypto),kr=Object.assign({appendTo:nr,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Sr=Object.keys(kr);function Or(e){var t=(e.plugins||[]).reduce((function(t,n){var r,o=n.name,i=n.defaultValue;return o&&(t[o]=void 0!==e[o]?e[o]:null!=(r=kr[o])?r:i),t}),{});return Object.assign({},e,t)}function Tr(e,t){var n=Object.assign({},t,{content:ir(t.content,[e])},t.ignoreAttributes?{}:function(e,t){return(t?Object.keys(Or(Object.assign({},kr,{plugins:t}))):Sr).reduce((function(t,n){var r=(e.getAttribute("data-tippy-"+n)||"").trim();if(!r)return t;if("content"===n)t[n]=r;else try{t[n]=JSON.parse(r)}catch(e){t[n]=r}return t}),{})}(e,t.plugins));return n.aria=Object.assign({},kr.aria,n.aria),n.aria={expanded:"auto"===n.aria.expanded?t.interactive:n.aria.expanded,content:"auto"===n.aria.content?t.interactive?null:"describedby":n.aria.content},n}var Cr=function(){return"innerHTML"};function Fr(e,t){e[Cr()]=t}function Ar(e){var t=dr();return!0===e?t.className=Jn:(t.className=er,fr(e)?t.appendChild(e):Fr(t,e)),t}function Rr(e,t){fr(t.content)?(Fr(e,""),e.appendChild(t.content)):"function"!=typeof t.content&&(t.allowHTML?Fr(e,t.content):e.textContent=t.content)}function Pr(e){var t=e.firstElementChild,n=ur(t.children);return{box:t,content:n.find((function(e){return e.classList.contains(Qn)})),arrow:n.find((function(e){return e.classList.contains(Jn)||e.classList.contains(er)})),backdrop:n.find((function(e){return e.classList.contains(Zn)}))}}function _r(e){var t=dr(),n=dr();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var r=dr();function o(n,r){var o=Pr(t),i=o.box,a=o.content,s=o.arrow;r.theme?i.setAttribute("data-theme",r.theme):i.removeAttribute("data-theme"),"string"==typeof r.animation?i.setAttribute("data-animation",r.animation):i.removeAttribute("data-animation"),r.inertia?i.setAttribute("data-inertia",""):i.removeAttribute("data-inertia"),i.style.maxWidth="number"==typeof r.maxWidth?r.maxWidth+"px":r.maxWidth,r.role?i.setAttribute("role",r.role):i.removeAttribute("role"),n.content===r.content&&n.allowHTML===r.allowHTML||Rr(a,e.props),r.arrow?s?n.arrow!==r.arrow&&(i.removeChild(s),i.appendChild(Ar(r.arrow))):i.appendChild(Ar(r.arrow)):s&&i.removeChild(s)}return r.className=Qn,r.setAttribute("data-state","hidden"),Rr(r,e.props),t.appendChild(n),n.appendChild(r),o(e.props,e.props),{popper:t,onUpdate:o}}_r.$$tippy=!0;var Lr=1,Nr=[],Mr=[];function Ir(e,t){var n,r,o,i,a,s,l,u,c=Tr(e,Object.assign({},kr,Or(cr(t)))),d=!1,f=!1,p=!1,m=!1,h=[],v=ar(G,c.interactiveDebounce),g=Lr++,b=(u=c.plugins).filter((function(e,t){return u.indexOf(e)===t})),y={id:g,reference:e,popper:dr(),popperInstance:null,props:c,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:b,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(r),cancelAnimationFrame(o)},setProps:function(t){if(!y.state.isDestroyed){L("onBeforeUpdate",[y,t]),U();var n=y.props,r=Tr(e,Object.assign({},n,cr(t),{ignoreAttributes:!0}));y.props=r,$(),n.interactiveDebounce!==r.interactiveDebounce&&(I(),v=ar(G,r.interactiveDebounce)),n.triggerTarget&&!r.triggerTarget?sr(n.triggerTarget).forEach((function(e){e.removeAttribute("aria-expanded")})):r.triggerTarget&&e.removeAttribute("aria-expanded"),M(),_(),E&&E(n,r),y.popperInstance&&(Q(),J().forEach((function(e){requestAnimationFrame(e._tippy.popperInstance.forceUpdate)}))),L("onAfterUpdate",[y,t])}},setContent:function(e){y.setProps({content:e})},show:function(){var e=y.state.isVisible,t=y.state.isDestroyed,n=!y.state.isEnabled,r=gr.isTouch&&!y.props.touch,o=rr(y.props.duration,0,kr.duration);if(!(e||t||n||r||F().hasAttribute("disabled")||(L("onShow",[y],!1),!1===y.props.onShow(y)))){if(y.state.isVisible=!0,C()&&(x.style.visibility="visible"),_(),z(),y.state.isMounted||(x.style.transition="none"),C()){var i=R();pr([i.box,i.content],0)}var a,l,u;s=function(){var e;if(y.state.isVisible&&!m){if(m=!0,x.offsetHeight,x.style.transition=y.props.moveTransition,C()&&y.props.animation){var t=R(),n=t.box,r=t.content;pr([n,r],o),mr([n,r],"visible")}N(),M(),lr(Mr,y),null==(e=y.popperInstance)||e.forceUpdate(),L("onMount",[y]),y.props.animation&&C()&&function(e,t){B(e,(function(){y.state.isShown=!0,L("onShown",[y])}))}(o)}},l=y.props.appendTo,u=F(),(a=y.props.interactive&&l===nr||"parent"===l?u.parentNode:ir(l,[u])).contains(x)||a.appendChild(x),y.state.isMounted=!0,Q()}},hide:function(){var e=!y.state.isVisible,t=y.state.isDestroyed,n=!y.state.isEnabled,r=rr(y.props.duration,1,kr.duration);if(!(e||t||n)&&(L("onHide",[y],!1),!1!==y.props.onHide(y))){if(y.state.isVisible=!1,y.state.isShown=!1,m=!1,d=!1,C()&&(x.style.visibility="hidden"),I(),H(),_(!0),C()){var o=R(),i=o.box,a=o.content;y.props.animation&&(pr([i,a],r),mr([i,a],"hidden"))}N(),M(),y.props.animation?C()&&function(e,t){B(e,(function(){!y.state.isVisible&&x.parentNode&&x.parentNode.contains(x)&&t()}))}(r,y.unmount):y.unmount()}},hideWithInteractivity:function(e){A().addEventListener("mousemove",v),lr(Nr,v),v(e)},enable:function(){y.state.isEnabled=!0},disable:function(){y.hide(),y.state.isEnabled=!1},unmount:function(){y.state.isVisible&&y.hide(),y.state.isMounted&&(Z(),J().forEach((function(e){e._tippy.unmount()})),x.parentNode&&x.parentNode.removeChild(x),Mr=Mr.filter((function(e){return e!==y})),y.state.isMounted=!1,L("onHidden",[y]))},destroy:function(){y.state.isDestroyed||(y.clearDelayTimeouts(),y.unmount(),U(),delete e._tippy,y.state.isDestroyed=!0,L("onDestroy",[y]))}};if(!c.render)return y;var w=c.render(y),x=w.popper,E=w.onUpdate;x.setAttribute("data-tippy-root",""),x.id="tippy-"+y.id,y.popper=x,e._tippy=y,x._tippy=y;var k=b.map((function(e){return e.fn(y)})),S=e.hasAttribute("aria-expanded");return $(),M(),_(),L("onCreate",[y]),c.showOnCreate&&ee(),x.addEventListener("mouseenter",(function(){y.props.interactive&&y.state.isVisible&&y.clearDelayTimeouts()})),x.addEventListener("mouseleave",(function(){y.props.interactive&&y.props.trigger.indexOf("mouseenter")>=0&&A().addEventListener("mousemove",v)})),y;function O(){var e=y.props.touch;return Array.isArray(e)?e:[e,0]}function T(){return"hold"===O()[0]}function C(){var e;return!(null==(e=y.props.render)||!e.$$tippy)}function F(){return l||e}function A(){var e,t,n=F().parentNode;return n?null!=(t=sr(n)[0])&&null!=(e=t.ownerDocument)&&e.body?t.ownerDocument:document:document}function R(){return Pr(x)}function P(e){return y.state.isMounted&&!y.state.isVisible||gr.isTouch||i&&"focus"===i.type?0:rr(y.props.delay,e?0:1,kr.delay)}function _(e){void 0===e&&(e=!1),x.style.pointerEvents=y.props.interactive&&!e?"":"none",x.style.zIndex=""+y.props.zIndex}function L(e,t,n){var r;void 0===n&&(n=!0),k.forEach((function(n){n[e]&&n[e].apply(n,t)})),n&&(r=y.props)[e].apply(r,t)}function N(){var t=y.props.aria;if(t.content){var n="aria-"+t.content,r=x.id;sr(y.props.triggerTarget||e).forEach((function(e){var t=e.getAttribute(n);if(y.state.isVisible)e.setAttribute(n,t?t+" "+r:r);else{var o=t&&t.replace(r,"").trim();o?e.setAttribute(n,o):e.removeAttribute(n)}}))}}function M(){!S&&y.props.aria.expanded&&sr(y.props.triggerTarget||e).forEach((function(e){y.props.interactive?e.setAttribute("aria-expanded",y.state.isVisible&&e===F()?"true":"false"):e.removeAttribute("aria-expanded")}))}function I(){A().removeEventListener("mousemove",v),Nr=Nr.filter((function(e){return e!==v}))}function D(t){if(!gr.isTouch||!p&&"mousedown"!==t.type){var n=t.composedPath&&t.composedPath()[0]||t.target;if(!y.props.interactive||!vr(x,n)){if(sr(y.props.triggerTarget||e).some((function(e){return vr(e,n)}))){if(gr.isTouch)return;if(y.state.isVisible&&y.props.trigger.indexOf("click")>=0)return}else L("onClickOutside",[y,t]);!0===y.props.hideOnClick&&(y.clearDelayTimeouts(),y.hide(),f=!0,setTimeout((function(){f=!1})),y.state.isMounted||H())}}}function j(){p=!0}function V(){p=!1}function z(){var e=A();e.addEventListener("mousedown",D,!0),e.addEventListener("touchend",D,tr),e.addEventListener("touchstart",V,tr),e.addEventListener("touchmove",j,tr)}function H(){var e=A();e.removeEventListener("mousedown",D,!0),e.removeEventListener("touchend",D,tr),e.removeEventListener("touchstart",V,tr),e.removeEventListener("touchmove",j,tr)}function B(e,t){var n=R().box;function r(e){e.target===n&&(hr(n,"remove",r),t())}if(0===e)return t();hr(n,"remove",a),hr(n,"add",r),a=r}function W(t,n,r){void 0===r&&(r=!1),sr(y.props.triggerTarget||e).forEach((function(e){e.addEventListener(t,n,r),h.push({node:e,eventType:t,handler:n,options:r})}))}function $(){var e;T()&&(W("touchstart",q,{passive:!0}),W("touchend",K,{passive:!0})),(e=y.props.trigger,e.split(/\s+/).filter(Boolean)).forEach((function(e){if("manual"!==e)switch(W(e,q),e){case"mouseenter":W("mouseleave",K);break;case"focus":W(Er?"focusout":"blur",Y);break;case"focusin":W("focusout",Y)}}))}function U(){h.forEach((function(e){var t=e.node,n=e.eventType,r=e.handler,o=e.options;t.removeEventListener(n,r,o)})),h=[]}function q(e){var t,n=!1;if(y.state.isEnabled&&!X(e)&&!f){var r="focus"===(null==(t=i)?void 0:t.type);i=e,l=e.currentTarget,M(),!y.state.isVisible&&or(e,"MouseEvent")&&Nr.forEach((function(t){return t(e)})),"click"===e.type&&(y.props.trigger.indexOf("mouseenter")<0||d)&&!1!==y.props.hideOnClick&&y.state.isVisible?n=!0:ee(e),"click"===e.type&&(d=!n),n&&!r&&te(e)}}function G(e){var t=e.target,n=F().contains(t)||x.contains(t);if("mousemove"!==e.type||!n){var r=J().concat(x).map((function(e){var t,n=null==(t=e._tippy.popperInstance)?void 0:t.state;return n?{popperRect:e.getBoundingClientRect(),popperState:n,props:c}:null})).filter(Boolean);(function(e,t){var n=t.clientX,r=t.clientY;return e.every((function(e){var t=e.popperRect,o=e.popperState,i=e.props.interactiveBorder,a=o.placement.split("-")[0],s=o.modifiersData.offset;if(!s)return!0;var l="bottom"===a?s.top.y:0,u="top"===a?s.bottom.y:0,c="right"===a?s.left.x:0,d="left"===a?s.right.x:0,f=t.top-r+l>i,p=r-t.bottom-u>i,m=t.left-n+c>i,h=n-t.right-d>i;return f||p||m||h}))})(r,e)&&(I(),te(e))}}function K(e){X(e)||y.props.trigger.indexOf("click")>=0&&d||(y.props.interactive?y.hideWithInteractivity(e):te(e))}function Y(e){y.props.trigger.indexOf("focusin")<0&&e.target!==F()||y.props.interactive&&e.relatedTarget&&x.contains(e.relatedTarget)||te(e)}function X(e){return!!gr.isTouch&&T()!==e.type.indexOf("touch")>=0}function Q(){Z();var t=y.props,n=t.popperOptions,r=t.placement,o=t.offset,i=t.getReferenceClientRect,a=t.moveTransition,l=C()?Pr(x).arrow:null,u=i?{getBoundingClientRect:i,contextElement:i.contextElement||F()}:e,c=[{name:"offset",options:{offset:o}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!a}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(e){var t=e.state;if(C()){var n=R().box;["placement","reference-hidden","escaped"].forEach((function(e){"placement"===e?n.setAttribute("data-placement",t.placement):t.attributes.popper["data-popper-"+e]?n.setAttribute("data-"+e,""):n.removeAttribute("data-"+e)})),t.attributes.popper={}}}}];C()&&l&&c.push({name:"arrow",options:{element:l,padding:3}}),c.push.apply(c,(null==n?void 0:n.modifiers)||[]),y.popperInstance=Xn(u,x,Object.assign({},n,{placement:r,onFirstUpdate:s,modifiers:c}))}function Z(){y.popperInstance&&(y.popperInstance.destroy(),y.popperInstance=null)}function J(){return ur(x.querySelectorAll("[data-tippy-root]"))}function ee(e){y.clearDelayTimeouts(),e&&L("onTrigger",[y,e]),z();var t=P(!0),r=O(),o=r[0],i=r[1];gr.isTouch&&"hold"===o&&i&&(t=i),t?n=setTimeout((function(){y.show()}),t):y.show()}function te(e){if(y.clearDelayTimeouts(),L("onUntrigger",[y,e]),y.state.isVisible){if(!(y.props.trigger.indexOf("mouseenter")>=0&&y.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(e.type)>=0&&d)){var t=P(!1);t?r=setTimeout((function(){y.state.isVisible&&y.hide()}),t):o=requestAnimationFrame((function(){y.hide()}))}}else H()}}function Dr(e,t){void 0===t&&(t={});var n=kr.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",yr,tr),window.addEventListener("blur",xr);var r,o=Object.assign({},t,{plugins:n}),i=(r=e,fr(r)?[r]:function(e){return or(e,"NodeList")}(r)?ur(r):Array.isArray(r)?r:ur(document.querySelectorAll(r))).reduce((function(e,t){var n=t&&Ir(t,o);return n&&e.push(n),e}),[]);return fr(e)?i[0]:i}Dr.defaultProps=kr,Dr.setDefaultProps=function(e){Object.keys(e).forEach((function(t){kr[t]=e[t]}))},Dr.currentInput=gr,Object.assign({},Nn,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}}),Dr.setDefaultProps({render:_r});const jr=Dr,Vr=window.ReactDOM;function zr(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}var Hr="undefined"!=typeof window&&"undefined"!=typeof document;function Br(e,t){e&&("function"==typeof e&&e(t),{}.hasOwnProperty.call(e,"current")&&(e.current=t))}function Wr(){return Hr&&document.createElement("div")}function $r(e,t){if(e===t)return!0;if("object"==typeof e&&null!=e&&"object"==typeof t&&null!=t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var n in e){if(!t.hasOwnProperty(n))return!1;if(!$r(e[n],t[n]))return!1}return!0}return!1}function Ur(e){var t=[];return e.forEach((function(e){t.find((function(t){return $r(e,t)}))||t.push(e)})),t}var qr=Hr?r.useLayoutEffect:r.useEffect;function Gr(e,t,n){n.split(/\s+/).forEach((function(n){n&&e.classList[t](n)}))}var Kr={name:"className",defaultValue:"",fn:function(e){var t=e.popper.firstElementChild,n=function(){var t;return!!(null==(t=e.props.render)?void 0:t.$$tippy)};function r(){e.props.className&&!n()||Gr(t,"add",e.props.className)}return{onCreate:r,onBeforeUpdate:function(){n()&&Gr(t,"remove",e.props.className)},onAfterUpdate:r}}};function Yr(e){return function(t){var n,o,a=t.children,s=t.content,l=t.visible,u=t.singleton,c=t.render,d=t.reference,f=t.disabled,p=void 0!==f&&f,m=t.ignoreAttributes,h=void 0===m||m,v=(t.__source,t.__self,zr(t,["children","content","visible","singleton","render","reference","disabled","ignoreAttributes","__source","__self"])),g=void 0!==l,b=void 0!==u,y=(0,r.useState)(!1),w=y[0],x=y[1],E=(0,r.useState)({}),k=E[0],S=E[1],O=(0,r.useState)(),T=O[0],C=O[1],F=(n=function(){return{container:Wr(),renders:1}},(o=(0,r.useRef)()).current||(o.current="function"==typeof n?n():n),o.current),A=Object.assign({ignoreAttributes:h},v,{content:F.container});g&&(A.trigger="manual",A.hideOnClick=!1),b&&(p=!0);var R=A,P=A.plugins||[];c&&(R=Object.assign({},A,{plugins:b&&null!=u.data?[].concat(P,[{fn:function(){return{onTrigger:function(e,t){var n=u.data.children.find((function(e){return e.instance.reference===t.currentTarget}));e.state.$$activeSingletonInstance=n.instance,C(n.content)}}}}]):P,render:function(){return{popper:F.container}}}));var _=[d].concat(a?[a.type]:[]);return qr((function(){var t=d;d&&d.hasOwnProperty("current")&&(t=d.current);var n=e(t||F.ref||Wr(),Object.assign({},R,{plugins:[Kr].concat(A.plugins||[])}));return F.instance=n,p&&n.disable(),l&&n.show(),b&&u.hook({instance:n,content:s,props:R,setSingletonContent:C}),x(!0),function(){n.destroy(),null==u||u.cleanup(n)}}),_),qr((function(){var e,t,n,r,o;if(1!==F.renders){var i=F.instance;i.setProps((t=i.props,n=R,Object.assign({},n,{popperOptions:Object.assign({},t.popperOptions,n.popperOptions,{modifiers:Ur([].concat((null==(r=t.popperOptions)?void 0:r.modifiers)||[],(null==(o=n.popperOptions)?void 0:o.modifiers)||[]))})}))),null==(e=i.popperInstance)||e.forceUpdate(),p?i.disable():i.enable(),g&&(l?i.show():i.hide()),b&&u.hook({instance:i,content:s,props:R,setSingletonContent:C})}else F.renders++})),qr((function(){var e;if(c){var t=F.instance;t.setProps({popperOptions:Object.assign({},t.props.popperOptions,{modifiers:[].concat(((null==(e=t.props.popperOptions)?void 0:e.modifiers)||[]).filter((function(e){return"$$tippyReact"!==e.name})),[{name:"$$tippyReact",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(e){var t,n=e.state,r=null==(t=n.modifiersData)?void 0:t.hide;k.placement===n.placement&&k.referenceHidden===(null==r?void 0:r.isReferenceHidden)&&k.escaped===(null==r?void 0:r.hasPopperEscaped)||S({placement:n.placement,referenceHidden:null==r?void 0:r.isReferenceHidden,escaped:null==r?void 0:r.hasPopperEscaped}),n.attributes.popper={}}}])})})}}),[k.placement,k.referenceHidden,k.escaped].concat(_)),i().createElement(i().Fragment,null,a?(0,r.cloneElement)(a,{ref:function(e){F.ref=e,Br(a.ref,e)}}):null,w&&(0,Vr.createPortal)(c?c(function(e){var t={"data-placement":e.placement};return e.referenceHidden&&(t["data-reference-hidden"]=""),e.escaped&&(t["data-escaped"]=""),t}(k),T,F.instance):s,F.container))}}var Xr=function(e,t){return(0,r.forwardRef)((function(n,o){var a=n.children,s=zr(n,["children"]);return i().createElement(e,Object.assign({},t,s),a?(0,r.cloneElement)(a,{ref:function(e){Br(o,e),Br(a.ref,e)}}):null)}))};const Qr=Xr(Yr(jr)),Zr=({children:e,content:t,interactive:n=!1,arrow:o=!1,...i})=>t?(0,r.createElement)(Qr,{className:"zipwp-images-tooltip",content:t,interactive:n,arrow:o,...i},e):e;function Jr(){return e=/^Mac/i,"undefined"!=typeof window&&null!=window.navigator&&e.test((null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.platform)||window.navigator.platform);var e,t}function eo(e){return!(0!==e.mozInputSource||!e.isTrusted)||(t=/Android/i,"undefined"!=typeof window&&null!=window.navigator&&((null===(n=window.navigator.userAgentData)||void 0===n?void 0:n.brands.some((e=>t.test(e.brand))))||t.test(window.navigator.userAgent))&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType);var t,n}const to=e=>{var t;return null!==(t=null==e?void 0:e.ownerDocument)&&void 0!==t?t:document},no=e=>e&&"window"in e&&e.window===e?e:to(e).defaultView||window;let ro=null,oo=new Set,io=new Map,ao=!1,so=!1;const lo={Tab:!0,Escape:!0};function uo(e,t){for(let n of oo)n(e,t)}function co(e){ao=!0,function(e){return!(e.metaKey||!Jr()&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key)}(e)&&(ro="keyboard",uo("keyboard",e))}function fo(e){ro="pointer","mousedown"!==e.type&&"pointerdown"!==e.type||(ao=!0,uo("pointer",e))}function po(e){eo(e)&&(ao=!0,ro="virtual")}function mo(e){e.target!==window&&e.target!==document&&(ao||so||(ro="virtual",uo("virtual",e)),ao=!1,so=!1)}function ho(){ao=!1,so=!0}function vo(e){if("undefined"==typeof window||io.get(no(e)))return;const t=no(e),n=to(e);let r=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){ao=!0,r.apply(this,arguments)},n.addEventListener("keydown",co,!0),n.addEventListener("keyup",co,!0),n.addEventListener("click",po,!0),t.addEventListener("focus",mo,!0),t.addEventListener("blur",ho,!1),"undefined"!=typeof PointerEvent?(n.addEventListener("pointerdown",fo,!0),n.addEventListener("pointermove",fo,!0),n.addEventListener("pointerup",fo,!0)):(n.addEventListener("mousedown",fo,!0),n.addEventListener("mousemove",fo,!0),n.addEventListener("mouseup",fo,!0)),t.addEventListener("beforeunload",(()=>{go(e)}),{once:!0}),io.set(t,{focus:r})}const go=(e,t)=>{const n=no(e),r=to(e);t&&r.removeEventListener("DOMContentLoaded",t),io.has(n)&&(n.HTMLElement.prototype.focus=io.get(n).focus,r.removeEventListener("keydown",co,!0),r.removeEventListener("keyup",co,!0),r.removeEventListener("click",po,!0),n.removeEventListener("focus",mo,!0),n.removeEventListener("blur",ho,!1),"undefined"!=typeof PointerEvent?(r.removeEventListener("pointerdown",fo,!0),r.removeEventListener("pointermove",fo,!0),r.removeEventListener("pointerup",fo,!0)):(r.removeEventListener("mousedown",fo,!0),r.removeEventListener("mousemove",fo,!0),r.removeEventListener("mouseup",fo,!0)),io.delete(n))};function bo(){return"pointer"!==ro}"undefined"!=typeof document&&function(e){const t=to(e);let n;"loading"!==t.readyState?vo(e):(n=()=>{vo(e)},t.addEventListener("DOMContentLoaded",n))}();const yo=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);const wo="undefined"!=typeof document?r.useLayoutEffect:()=>{};class xo{isDefaultPrevented(){return this.nativeEvent.defaultPrevented}preventDefault(){this.defaultPrevented=!0,this.nativeEvent.preventDefault()}stopPropagation(){this.nativeEvent.stopPropagation(),this.isPropagationStopped=()=>!0}isPropagationStopped(){return!1}persist(){}constructor(e,t){this.nativeEvent=t,this.target=t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget,this.bubbles=t.bubbles,this.cancelable=t.cancelable,this.defaultPrevented=t.defaultPrevented,this.eventPhase=t.eventPhase,this.isTrusted=t.isTrusted,this.timeStamp=t.timeStamp,this.type=e}}function Eo(e){let t=(0,r.useRef)({isFocused:!1,observer:null});wo((()=>{const e=t.current;return()=>{e.observer&&(e.observer.disconnect(),e.observer=null)}}),[]);let n=function(e){const t=(0,r.useRef)(null);return wo((()=>{t.current=e}),[e]),(0,r.useCallback)(((...e)=>{const n=t.current;return null==n?void 0:n(...e)}),[])}((t=>{null==e||e(t)}));return(0,r.useCallback)((e=>{if(e.target instanceof HTMLButtonElement||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement){t.current.isFocused=!0;let r=e.target,o=e=>{t.current.isFocused=!1,r.disabled&&n(new xo("blur",e)),t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)};r.addEventListener("focusout",o,{once:!0}),t.current.observer=new MutationObserver((()=>{if(t.current.isFocused&&r.disabled){var e;null===(e=t.current.observer)||void 0===e||e.disconnect();let n=r===document.activeElement?null:document.activeElement;r.dispatchEvent(new FocusEvent("blur",{relatedTarget:n})),r.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:n}))}})),t.current.observer.observe(r,{attributes:!0,attributeFilter:["disabled"]})}}),[n])}function ko(e={}){let{autoFocus:t=!1,isTextInput:n,within:o}=e,i=(0,r.useRef)({isFocused:!1,isFocusVisible:t||bo()}),[a,s]=(0,r.useState)(!1),[l,u]=(0,r.useState)((()=>i.current.isFocused&&i.current.isFocusVisible)),c=(0,r.useCallback)((()=>u(i.current.isFocused&&i.current.isFocusVisible)),[]),d=(0,r.useCallback)((e=>{i.current.isFocused=e,s(e),c()}),[c]);var f,p,m;f=e=>{i.current.isFocusVisible=e,c()},p=[],m={isTextInput:n},vo(),(0,r.useEffect)((()=>{let e=(e,t)=>{(function(e,t,n){var r;const o="undefined"!=typeof window?no(null==n?void 0:n.target).HTMLInputElement:HTMLInputElement,i="undefined"!=typeof window?no(null==n?void 0:n.target).HTMLTextAreaElement:HTMLTextAreaElement,a="undefined"!=typeof window?no(null==n?void 0:n.target).HTMLElement:HTMLElement,s="undefined"!=typeof window?no(null==n?void 0:n.target).KeyboardEvent:KeyboardEvent;return!((e=e||(null==n?void 0:n.target)instanceof o&&!yo.has(null==n||null===(r=n.target)||void 0===r?void 0:r.type)||(null==n?void 0:n.target)instanceof i||(null==n?void 0:n.target)instanceof a&&(null==n?void 0:n.target.isContentEditable))&&"keyboard"===t&&n instanceof s&&!lo[n.key])})(!!(null==m?void 0:m.isTextInput),e,t)&&f(bo())};return oo.add(e),()=>{oo.delete(e)}}),p);let{focusProps:h}=function(e){let{isDisabled:t,onFocus:n,onBlur:o,onFocusChange:i}=e;const a=(0,r.useCallback)((e=>{if(e.target===e.currentTarget)return o&&o(e),i&&i(!1),!0}),[o,i]),s=Eo(a),l=(0,r.useCallback)((e=>{const t=to(e.target);e.target===e.currentTarget&&t.activeElement===e.target&&(n&&n(e),i&&i(!0),s(e))}),[i,n,s]);return{focusProps:{onFocus:!t&&(n||i||o)?l:void 0,onBlur:t||!o&&!i?void 0:a}}}({isDisabled:o,onFocusChange:d}),{focusWithinProps:v}=function(e){let{isDisabled:t,onBlurWithin:n,onFocusWithin:o,onFocusWithinChange:i}=e,a=(0,r.useRef)({isFocusWithin:!1}),s=(0,r.useCallback)((e=>{a.current.isFocusWithin&&!e.currentTarget.contains(e.relatedTarget)&&(a.current.isFocusWithin=!1,n&&n(e),i&&i(!1))}),[n,i,a]),l=Eo(s),u=(0,r.useCallback)((e=>{a.current.isFocusWithin||document.activeElement!==e.target||(o&&o(e),i&&i(!0),a.current.isFocusWithin=!0,l(e))}),[o,i,l]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:u,onBlur:s}}}({isDisabled:!o,onFocusWithinChange:d});return{isFocused:a,isFocusVisible:l,focusProps:o?v:h}}let So=!1,Oo=0;function To(){So=!0,setTimeout((()=>{So=!1}),50)}function Co(e){"touch"===e.pointerType&&To()}function Fo(){if("undefined"!=typeof document)return"undefined"!=typeof PointerEvent?document.addEventListener("pointerup",Co):document.addEventListener("touchend",To),Oo++,()=>{Oo--,Oo>0||("undefined"!=typeof PointerEvent?document.removeEventListener("pointerup",Co):document.removeEventListener("touchend",To))}}function Ao(e){let{onHoverStart:t,onHoverChange:n,onHoverEnd:o,isDisabled:i}=e,[a,s]=(0,r.useState)(!1),l=(0,r.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,r.useEffect)(Fo,[]);let{hoverProps:u,triggerHoverEnd:c}=(0,r.useMemo)((()=>{let e=(e,r)=>{if(l.pointerType=r,i||"touch"===r||l.isHovered||!e.currentTarget.contains(e.target))return;l.isHovered=!0;let o=e.currentTarget;l.target=o,t&&t({type:"hoverstart",target:o,pointerType:r}),n&&n(!0),s(!0)},r=(e,t)=>{if(l.pointerType="",l.target=null,"touch"===t||!l.isHovered)return;l.isHovered=!1;let r=e.currentTarget;o&&o({type:"hoverend",target:r,pointerType:t}),n&&n(!1),s(!1)},a={};return"undefined"!=typeof PointerEvent?(a.onPointerEnter=t=>{So&&"mouse"===t.pointerType||e(t,t.pointerType)},a.onPointerLeave=e=>{!i&&e.currentTarget.contains(e.target)&&r(e,e.pointerType)}):(a.onTouchStart=()=>{l.ignoreEmulatedMouseEvents=!0},a.onMouseEnter=t=>{l.ignoreEmulatedMouseEvents||So||e(t,"mouse"),l.ignoreEmulatedMouseEvents=!1},a.onMouseLeave=e=>{!i&&e.currentTarget.contains(e.target)&&r(e,"mouse")}),{hoverProps:a,triggerHoverEnd:r}}),[t,n,o,i,l]);return(0,r.useEffect)((()=>{i&&c({currentTarget:l.target},l.pointerType)}),[i]),{hoverProps:u,isHovered:a}}var Ro=Object.defineProperty,Po=(e,t,n)=>(((e,t,n)=>{t in e?Ro(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let _o=new class{constructor(){Po(this,"current",this.detect()),Po(this,"handoffState","pending"),Po(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}};function Lo(e){return _o.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}function No(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}function Mo(){let e=[],t={addEventListener:(e,n,r,o)=>(e.addEventListener(n,r,o),t.add((()=>e.removeEventListener(n,r,o)))),requestAnimationFrame(...e){let n=requestAnimationFrame(...e);return t.add((()=>cancelAnimationFrame(n)))},nextFrame:(...e)=>t.requestAnimationFrame((()=>t.requestAnimationFrame(...e))),setTimeout(...e){let n=setTimeout(...e);return t.add((()=>clearTimeout(n)))},microTask(...e){let n={current:!0};return No((()=>{n.current&&e[0]()})),t.add((()=>{n.current=!1}))},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add((()=>{Object.assign(e.style,{[t]:r})}))},group(e){let t=Mo();return e(t),this.add((()=>t.dispose()))},add:t=>(e.includes(t)||e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function Io(){let[e]=(0,r.useState)(Mo);return(0,r.useEffect)((()=>()=>e.dispose()),[e]),e}let Do=(e,t)=>{_o.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)};function jo(e){let t=(0,r.useRef)(e);return Do((()=>{t.current=e}),[e]),t}let Vo=function(e){let t=jo(e);return r.useCallback(((...e)=>t.current(...e)),[t])};function zo(e,t=!1){let n=null===e?null:"current"in e?e.current:e,[o,i]=(0,r.useReducer)((()=>({})),{}),a=(0,r.useMemo)((()=>function(e){if(null===e)return{width:0,height:0};let{width:t,height:n}=e.getBoundingClientRect();return{width:t,height:n}}(n)),[n,o]);return Do((()=>{if(!n)return;let e=new ResizeObserver(i);return e.observe(n),()=>{e.disconnect()}}),[n]),t?{width:`${a.width}px`,height:`${a.height}px`}:a}let Ho=new Map,Bo=new Map;function Wo(e){var t;let n=null!=(t=Bo.get(e))?t:0;return Bo.set(e,n+1),0!==n||(Ho.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>function(e){var t;let n=null!=(t=Bo.get(e))?t:1;if(1===n?Bo.delete(e):Bo.set(e,n-1),1!==n)return;let r=Ho.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,Ho.delete(e))}(e)}function $o({allowed:e,disallowed:t}={},n=!0){Do((()=>{var r,o;if(!n)return;let i=Mo();for(let e of null!=(r=null==t?void 0:t())?r:[])e&&i.add(Wo(e));let a=null!=(o=null==e?void 0:e())?o:[];for(let e of a){if(!e)continue;let t=Lo(e);if(!t)continue;let n=e.parentElement;for(;n&&n!==t.body;){for(let e of n.children)a.some((t=>e.contains(t)))||i.add(Wo(e));n=n.parentElement}}return i.dispose}),[n,e,t])}function Uo(e,t,n=!0){let o=jo((e=>{let n=e.getBoundingClientRect();0===n.x&&0===n.y&&0===n.width&&0===n.height&&t()}));(0,r.useEffect)((()=>{if(!n)return;let t=null===e?null:e instanceof HTMLElement?e:e.current;if(!t)return;let r=Mo();if("undefined"!=typeof ResizeObserver){let e=new ResizeObserver((()=>o.current(t)));e.observe(t),r.add((()=>e.disconnect()))}if("undefined"!=typeof IntersectionObserver){let e=new IntersectionObserver((()=>o.current(t)));e.observe(t),r.add((()=>e.disconnect()))}return()=>r.dispose()}),[e,o,n])}function qo(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map((e=>`"${e}"`)).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,qo),r}let Go=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>`${e}:not([tabindex='-1'])`)).join(","),Ko=["[data-autofocus]"].map((e=>`${e}:not([tabindex='-1'])`)).join(",");var Yo,Xo,Qo=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(Qo||{}),Zo=((Xo=Zo||{})[Xo.Error=0]="Error",Xo[Xo.Overflow=1]="Overflow",Xo[Xo.Success=2]="Success",Xo[Xo.Underflow=3]="Underflow",Xo),Jo=((Yo=Jo||{})[Yo.Previous=-1]="Previous",Yo[Yo.Next=1]="Next",Yo);function ei(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(Go)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}var ti=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(ti||{});function ni(e,t=0){var n;return e!==(null==(n=Lo(e))?void 0:n.body)&&qo(t,{0:()=>e.matches(Go),1(){let t=e;for(;null!==t;){if(t.matches(Go))return!0;t=t.parentElement}return!1}})}function ri(e){let t=Lo(e);Mo().nextFrame((()=>{t&&!ni(t.activeElement,0)&&function(e){null==e||e.focus({preventScroll:!0})}(e)}))}var oi=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(oi||{});"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",(e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")}),!0),document.addEventListener("click",(e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")}),!0));let ii=["textarea","input"].join(",");function ai(e,t=(e=>e)){return e.slice().sort(((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let i=r.compareDocumentPosition(o);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}function si(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){let i=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,a=Array.isArray(e)?n?ai(e):e:64&t?function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(Ko)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}(e):ei(e);o.length>0&&a.length>1&&(a=a.filter((e=>!o.some((t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))))),r=null!=r?r:i.activeElement;let s,l=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,a.indexOf(r))-1;if(4&t)return Math.max(0,a.indexOf(r))+1;if(8&t)return a.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=32&t?{preventScroll:!0}:{},d=0,f=a.length;do{if(d>=f||d+f<=0)return 0;let e=u+d;if(16&t)e=(e+f)%f;else{if(e<0)return 3;if(e>=f)return 1}s=a[e],null==s||s.focus(c),d+=l}while(s!==i.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,ii))&&n}(s)&&s.select(),2}function li(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function ui(e,t,n){let o=jo(t);(0,r.useEffect)((()=>{function t(e){o.current(e)}return document.addEventListener(e,t,n),()=>document.removeEventListener(e,t,n)}),[e,n])}function ci(e,t,n=!0){let o=(0,r.useRef)(!1);function i(n,r){if(!o.current||n.defaultPrevented)return;let i=r(n);if(null===i||!i.getRootNode().contains(i)||!i.isConnected)return;let a=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e);for(let e of a){if(null===e)continue;let t=e instanceof HTMLElement?e:e.current;if(null!=t&&t.contains(i)||n.composed&&n.composedPath().includes(t))return}return!ni(i,ti.Loose)&&-1!==i.tabIndex&&n.preventDefault(),t(n,i)}(0,r.useEffect)((()=>{requestAnimationFrame((()=>{o.current=n}))}),[n]);let a=(0,r.useRef)(null);ui("pointerdown",(e=>{var t,n;o.current&&(a.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),!0),ui("mousedown",(e=>{var t,n;o.current&&(a.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),!0),ui("click",(e=>{li()||/Android/gi.test(window.navigator.userAgent)||a.current&&(i(e,(()=>a.current)),a.current=null)}),!0),ui("touchend",(e=>i(e,(()=>e.target instanceof HTMLElement?e.target:null))),!0),function(e,t,n){let o=jo((e=>i(e,(()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null))));(0,r.useEffect)((()=>{function t(e){o.current(e)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)}),[e,n])}("blur",0,!0)}function di(...e){return(0,r.useMemo)((()=>Lo(...e)),[...e])}function fi(e){var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";return"string"==typeof n&&"button"===n.toLowerCase()?"button":void 0}function pi(e,t){let[n,o]=(0,r.useState)((()=>fi(e)));return Do((()=>{o(fi(e))}),[e.type,e.as]),Do((()=>{n||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&o("button")}),[n,t]),n}function mi(){let e;return{before({doc:t}){var n;let r=t.documentElement,o=null!=(n=t.defaultView)?n:window;e=Math.max(0,o.innerWidth-r.clientWidth)},after({doc:t,d:n}){let r=t.documentElement,o=Math.max(0,r.clientWidth-r.offsetWidth),i=Math.max(0,e-o);n.style(r,"paddingRight",`${i}px`)}}}function hi(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let vi=function(e,t){let n=new Map,r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e,...o){let i=t[e].call(n,...o);i&&(n=i,r.forEach((e=>e())))}}}(0,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:Mo(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:hi(n)},o=[li()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap((e=>e())).some((t=>t.contains(e)))}t.microTask((()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=Mo();n.style(e.documentElement,"scrollBehavior","auto"),t.add((()=>t.microTask((()=>n.dispose()))))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,i=null;t.addEventListener(e,"click",(t=>{if(t.target instanceof HTMLElement)try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),a=e.querySelector(o);a&&!r(a)&&(i=a)}catch{}}),!0),t.addEventListener(e,"touchstart",(e=>{if(e.target instanceof HTMLElement)if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")})),t.addEventListener(e,"touchmove",(e=>{if(e.target instanceof HTMLElement){if("INPUT"===e.target.tagName)return;if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}}),{passive:!1}),t.add((()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;o!==t&&window.scrollTo(0,o),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)}))}))}}:{},mi(),{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];o.forEach((({before:e})=>null==e?void 0:e(r))),o.forEach((({after:e})=>null==e?void 0:e(r)))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});vi.subscribe((()=>{let e=vi.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&vi.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&vi.dispatch("TEARDOWN",n)}}));let gi=Symbol();function bi(...e){let t=(0,r.useRef)(e);(0,r.useEffect)((()=>{t.current=e}),[e]);let n=Vo((e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)}));return e.every((e=>null==e||(null==e?void 0:e[gi])))?void 0:n}let yi=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function wi(e){var t,n;let r=null!=(t=e.innerText)?t:"",o=e.cloneNode(!0);if(!(o instanceof HTMLElement))return r;let i=!1;for(let e of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))e.remove(),i=!0;let a=i?null!=(n=o.innerText)?n:"":r;return yi.test(a)&&(a=a.replace(yi,"")),a}function xi(e){return[e.screenX,e.screenY]}const Ei=Math.min,ki=Math.max,Si=Math.round,Oi=Math.floor,Ti=e=>({x:e,y:e}),Ci={left:"right",right:"left",bottom:"top",top:"bottom"},Fi={start:"end",end:"start"};function Ai(e,t,n){return ki(e,Ei(t,n))}function Ri(e,t){return"function"==typeof e?e(t):e}function Pi(e){return e.split("-")[0]}function _i(e){return e.split("-")[1]}function Li(e){return"x"===e?"y":"x"}function Ni(e){return"y"===e?"height":"width"}function Mi(e){return["top","bottom"].includes(Pi(e))?"y":"x"}function Ii(e){return Li(Mi(e))}function Di(e){return e.replace(/start|end/g,(e=>Fi[e]))}function ji(e){return e.replace(/left|right|bottom|top/g,(e=>Ci[e]))}function Vi(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function zi(e,t,n){let{reference:r,floating:o}=e;const i=Mi(t),a=Ii(t),s=Ni(a),l=Pi(t),u="y"===i,c=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[s]/2-o[s]/2;let p;switch(l){case"top":p={x:c,y:r.y-o.height};break;case"bottom":p={x:c,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-o.width,y:d};break;default:p={x:r.x,y:r.y}}switch(_i(t)){case"start":p[a]-=f*(n&&u?-1:1);break;case"end":p[a]+=f*(n&&u?-1:1)}return p}async function Hi(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:a,elements:s,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=Ri(t,e),m=function(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}(p),h=s[f?"floating"===d?"reference":"floating":d],v=Vi(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:l})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,b=await(null==i.getOffsetParent?void 0:i.getOffsetParent(s.floating)),y=await(null==i.isElement?void 0:i.isElement(b))&&await(null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},w=Vi(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:g,offsetParent:b,strategy:l}):g);return{top:(v.top-w.top+m.top)/y.y,bottom:(w.bottom-v.bottom+m.bottom)/y.y,left:(v.left-w.left+m.left)/y.x,right:(w.right-v.right+m.right)/y.x}}function Bi(e){return Ui(e)?(e.nodeName||"").toLowerCase():"#document"}function Wi(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function $i(e){var t;return null==(t=(Ui(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Ui(e){return e instanceof Node||e instanceof Wi(e).Node}function qi(e){return e instanceof Element||e instanceof Wi(e).Element}function Gi(e){return e instanceof HTMLElement||e instanceof Wi(e).HTMLElement}function Ki(e){return"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof Wi(e).ShadowRoot)}function Yi(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=ea(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Xi(e){return["table","td","th"].includes(Bi(e))}function Qi(e){const t=Zi(),n=ea(e);return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function Zi(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Ji(e){return["html","body","#document"].includes(Bi(e))}function ea(e){return Wi(e).getComputedStyle(e)}function ta(e){return qi(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function na(e){if("html"===Bi(e))return e;const t=e.assignedSlot||e.parentNode||Ki(e)&&e.host||$i(e);return Ki(t)?t.host:t}function ra(e){const t=na(e);return Ji(t)?e.ownerDocument?e.ownerDocument.body:e.body:Gi(t)&&Yi(t)?t:ra(t)}function oa(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=ra(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=Wi(o);return i?t.concat(a,a.visualViewport||[],Yi(o)?o:[],a.frameElement&&n?oa(a.frameElement):[]):t.concat(o,oa(o,[],n))}function ia(e){const t=ea(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Gi(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,s=Si(n)!==i||Si(r)!==a;return s&&(n=i,r=a),{width:n,height:r,$:s}}function aa(e){return qi(e)?e:e.contextElement}function sa(e){const t=aa(e);if(!Gi(t))return Ti(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ia(t);let a=(i?Si(n.width):n.width)/r,s=(i?Si(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}const la=Ti(0);function ua(e){const t=Wi(e);return Zi()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:la}function ca(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=aa(e);let a=Ti(1);t&&(r?qi(r)&&(a=sa(r)):a=sa(e));const s=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==Wi(e))&&t}(i,n,r)?ua(i):Ti(0);let l=(o.left+s.x)/a.x,u=(o.top+s.y)/a.y,c=o.width/a.x,d=o.height/a.y;if(i){const e=Wi(i),t=r&&qi(r)?Wi(r):r;let n=e,o=n.frameElement;for(;o&&r&&t!==n;){const e=sa(o),t=o.getBoundingClientRect(),r=ea(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;l*=e.x,u*=e.y,c*=e.x,d*=e.y,l+=i,u+=a,n=Wi(o),o=n.frameElement}}return Vi({width:c,height:d,x:l,y:u})}const da=[":popover-open",":modal"];function fa(e){return da.some((t=>{try{return e.matches(t)}catch(e){return!1}}))}function pa(e){return ca($i(e)).left+ta(e).scrollLeft}function ma(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=Wi(e),r=$i(e),o=n.visualViewport;let i=r.clientWidth,a=r.clientHeight,s=0,l=0;if(o){i=o.width,a=o.height;const e=Zi();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:i,height:a,x:s,y:l}}(e,n);else if("document"===t)r=function(e){const t=$i(e),n=ta(e),r=e.ownerDocument.body,o=ki(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=ki(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+pa(e);const s=-n.scrollTop;return"rtl"===ea(r).direction&&(a+=ki(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}}($i(e));else if(qi(t))r=function(e,t){const n=ca(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Gi(e)?sa(e):Ti(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{const n=ua(e);r={...t,x:t.x-n.x,y:t.y-n.y}}return Vi(r)}function ha(e,t){const n=na(e);return!(n===t||!qi(n)||Ji(n))&&("fixed"===ea(n).position||ha(n,t))}function va(e,t,n){const r=Gi(t),o=$i(t),i="fixed"===n,a=ca(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const l=Ti(0);if(r||!r&&!i)if(("body"!==Bi(t)||Yi(o))&&(s=ta(t)),r){const e=ca(t,!0,i,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&(l.x=pa(o));return{x:a.left+s.scrollLeft-l.x,y:a.top+s.scrollTop-l.y,width:a.width,height:a.height}}function ga(e){return"static"===ea(e).position}function ba(e,t){return Gi(e)&&"fixed"!==ea(e).position?t?t(e):e.offsetParent:null}function ya(e,t){const n=Wi(e);if(fa(e))return n;if(!Gi(e)){let t=na(e);for(;t&&!Ji(t);){if(qi(t)&&!ga(t))return t;t=na(t)}return n}let r=ba(e,t);for(;r&&Xi(r)&&ga(r);)r=ba(r,t);return r&&Ji(r)&&ga(r)&&!Qi(r)?n:r||function(e){let t=na(e);for(;Gi(t)&&!Ji(t);){if(Qi(t))return t;t=na(t)}return null}(e)||n}const wa={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i="fixed"===o,a=$i(r),s=!!t&&fa(t.floating);if(r===a||s&&i)return n;let l={scrollLeft:0,scrollTop:0},u=Ti(1);const c=Ti(0),d=Gi(r);if((d||!d&&!i)&&(("body"!==Bi(r)||Yi(a))&&(l=ta(r)),Gi(r))){const e=ca(r);u=sa(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x,y:n.y*u.y-l.scrollTop*u.y+c.y}},getDocumentElement:$i,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[..."clippingAncestors"===n?fa(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=oa(e,[],!1).filter((e=>qi(e)&&"body"!==Bi(e))),o=null;const i="fixed"===ea(e).position;let a=i?na(e):e;for(;qi(a)&&!Ji(a);){const t=ea(a),n=Qi(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||Yi(a)&&!n&&ha(e,a))?r=r.filter((e=>e!==a)):o=t,a=na(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],s=i.reduce(((e,n)=>{const r=ma(t,n,o);return e.top=ki(r.top,e.top),e.right=Ei(r.right,e.right),e.bottom=Ei(r.bottom,e.bottom),e.left=ki(r.left,e.left),e}),ma(t,a,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ya,getElementRects:async function(e){const t=this.getOffsetParent||ya,n=this.getDimensions,r=await n(e.floating);return{reference:va(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=ia(e);return{width:t,height:n}},getScale:sa,isElement:qi,isRTL:function(e){return"rtl"===ea(e).direction}};function xa(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:l=!1}=r,u=aa(e),c=o||i?[...u?oa(u):[],...oa(t)]:[];c.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)}));const d=u&&s?function(e,t){let n,r=null;const o=$i(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function a(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),i();const{left:u,top:c,width:d,height:f}=e.getBoundingClientRect();if(s||t(),!d||!f)return;const p={rootMargin:-Oi(c)+"px "+-Oi(o.clientWidth-(u+d))+"px "+-Oi(o.clientHeight-(c+f))+"px "+-Oi(u)+"px",threshold:ki(0,Ei(1,l))||1};let m=!0;function h(e){const t=e[0].intersectionRatio;if(t!==l){if(!m)return a();t?a(!1,t):n=setTimeout((()=>{a(!1,1e-7)}),1e3)}m=!1}try{r=new IntersectionObserver(h,{...p,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(h,p)}r.observe(e)}(!0),i}(u,n):null;let f,p=-1,m=null;a&&(m=new ResizeObserver((e=>{let[r]=e;r&&r.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame((()=>{var e;null==(e=m)||e.observe(t)}))),n()})),u&&!l&&m.observe(u),m.observe(t));let h=l?ca(e):null;return l&&function t(){const r=ca(e);!h||r.x===h.x&&r.y===h.y&&r.width===h.width&&r.height===h.height||n(),h=r,f=requestAnimationFrame(t)}(),n(),()=>{var e;c.forEach((e=>{o&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)})),null==d||d(),null==(e=m)||e.disconnect(),m=null,l&&cancelAnimationFrame(f)}}const Ea=Hi,ka=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:a,middlewareData:s}=t,l=await async function(e,t){const{placement:n,platform:r,elements:o}=e,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),a=Pi(n),s=_i(n),l="y"===Mi(n),u=["left","top"].includes(a)?-1:1,c=i&&l?-1:1,d=Ri(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...d};return s&&"number"==typeof m&&(p="end"===s?-1*m:m),l?{x:p*c,y:f*u}:{x:f*u,y:p*c}}(t,e);return a===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:a}}}}},Sa=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:a,initialPlacement:s,platform:l,elements:u}=t,{mainAxis:c=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:h=!0,...v}=Ri(e,t);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const g=Pi(o),b=Pi(s)===s,y=await(null==l.isRTL?void 0:l.isRTL(u.floating)),w=f||(b||!h?[ji(s)]:function(e){const t=ji(e);return[Di(e),t,Di(t)]}(s));f||"none"===m||w.push(...function(e,t,n,r){const o=_i(e);let i=function(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:a;default:return[]}}(Pi(e),"start"===n,r);return o&&(i=i.map((e=>e+"-"+o)),t&&(i=i.concat(i.map(Di)))),i}(s,h,m,y));const x=[s,...w],E=await Hi(t,v),k=[];let S=(null==(r=i.flip)?void 0:r.overflows)||[];if(c&&k.push(E[g]),d){const e=function(e,t,n){void 0===n&&(n=!1);const r=_i(e),o=Ii(e),i=Ni(o);let a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=ji(a)),[a,ji(a)]}(o,a,y);k.push(E[e[0]],E[e[1]])}if(S=[...S,{placement:o,overflows:k}],!k.every((e=>e<=0))){var O,T;const e=((null==(O=i.flip)?void 0:O.index)||0)+1,t=x[e];if(t)return{data:{index:e,overflows:S},reset:{placement:t}};let n=null==(T=S.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:T.placement;if(!n)switch(p){case"bestFit":{var C;const e=null==(C=S.map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:C[0];e&&(n=e);break}case"initialPlacement":n=s}if(o!==n)return{reset:{placement:n}}}return{}}}},Oa=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){const{placement:n,rects:r,platform:o,elements:i}=t,{apply:a=(()=>{}),...s}=Ri(e,t),l=await Hi(t,s),u=Pi(n),c=_i(n),d="y"===Mi(n),{width:f,height:p}=r.floating;let m,h;"top"===u||"bottom"===u?(m=u,h=c===(await(null==o.isRTL?void 0:o.isRTL(i.floating))?"start":"end")?"left":"right"):(h=u,m="end"===c?"top":"bottom");const v=p-l[m],g=f-l[h],b=!t.middlewareData.shift;let y=v,w=g;if(d){const e=f-l.left-l.right;w=c||b?Ei(g,e):e}else{const e=p-l.top-l.bottom;y=c||b?Ei(v,e):e}if(b&&!c){const e=ki(l.left,0),t=ki(l.right,0),n=ki(l.top,0),r=ki(l.bottom,0);d?w=f-2*(0!==e||0!==t?e+t:ki(l.left,l.right)):y=p-2*(0!==n||0!==r?n+r:ki(l.top,l.bottom))}await a({...t,availableWidth:w,availableHeight:y});const x=await o.getDimensions(i.floating);return f!==x.width||p!==x.height?{reset:{rects:!0}}:{}}}},Ta=(e,t,n)=>{const r=new Map,o={platform:wa,...n},i={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,s=i.filter(Boolean),l=await(null==a.isRTL?void 0:a.isRTL(t));let u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=zi(u,r,l),f=r,p={},m=0;for(let n=0;n<s.length;n++){const{name:i,fn:h}=s[n],{x:v,y:g,data:b,reset:y}=await h({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...b}},y&&m<=50&&(m++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(u=!0===y.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):y.rects),({x:c,y:d}=zi(u,f,l))),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}})(e,t,{...o,platform:i})};var Ca="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function Fa(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!=r--;)if(!Fa(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){const n=o[r];if(!("_owner"===n&&e.$$typeof||Fa(e[n],t[n])))return!1}return!0}return e!=e&&t!=t}function Aa(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Ra(e,t){const n=Aa(e);return Math.round(t*n)/n}function Pa(e){const t=r.useRef(e);return Ca((()=>{t.current=e})),t}const _a={...o},La=_a.useInsertionEffect||(e=>e());function Na(e){const t=r.useRef((()=>{}));return La((()=>{t.current=e})),r.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}var Ma="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;let Ia=!1,Da=0;const ja=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Da++,Va=_a.useId||function(){const[e,t]=r.useState((()=>Ia?ja():void 0));return Ma((()=>{null==e&&t(ja())}),[]),r.useEffect((()=>{Ia=!0}),[]),e};const za=r.createContext(null),Ha=r.createContext(null),Ba=()=>{var e;return(null==(e=r.useContext(za))?void 0:e.id)||null},Wa=()=>r.useContext(Ha);function $a(e){var t;void 0===e&&(e={});const{open:n=!1,onOpenChange:o,nodeId:i}=e,[a,s]=r.useState(null),[l,u]=r.useState(null),c=(null==(t=e.elements)?void 0:t.reference)||a;Ma((()=>{c&&(h.current=c)}),[c]);const d=function(e){void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:s}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(o);Fa(p,o)||m(o);const[h,v]=r.useState(null),[g,b]=r.useState(null),y=r.useCallback((e=>{e!==k.current&&(k.current=e,v(e))}),[]),w=r.useCallback((e=>{e!==S.current&&(S.current=e,b(e))}),[]),x=a||h,E=s||g,k=r.useRef(null),S=r.useRef(null),O=r.useRef(d),T=null!=u,C=Pa(u),F=Pa(i),A=r.useCallback((()=>{if(!k.current||!S.current)return;const e={placement:t,strategy:n,middleware:p};F.current&&(e.platform=F.current),Ta(k.current,S.current,e).then((e=>{const t={...e,isPositioned:!0};R.current&&!Fa(O.current,t)&&(O.current=t,Vr.flushSync((()=>{f(t)})))}))}),[p,t,n,F]);Ca((()=>{!1===c&&O.current.isPositioned&&(O.current.isPositioned=!1,f((e=>({...e,isPositioned:!1}))))}),[c]);const R=r.useRef(!1);Ca((()=>(R.current=!0,()=>{R.current=!1})),[]),Ca((()=>{if(x&&(k.current=x),E&&(S.current=E),x&&E){if(C.current)return C.current(x,E,A);A()}}),[x,E,A,C,T]);const P=r.useMemo((()=>({reference:k,floating:S,setReference:y,setFloating:w})),[y,w]),_=r.useMemo((()=>({reference:x,floating:E})),[x,E]),L=r.useMemo((()=>{const e={position:n,left:0,top:0};if(!_.floating)return e;const t=Ra(_.floating,d.x),r=Ra(_.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...Aa(_.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}}),[n,l,_.floating,d.x,d.y]);return r.useMemo((()=>({...d,update:A,refs:P,elements:_,floatingStyles:L})),[d,A,P,_,L])}({...e,elements:{...e.elements,...l&&{reference:l}}}),f=Wa(),p=null!=Ba(),m=Na(((e,t,n)=>{v.current.openEvent=e?t:void 0,g.emit("openchange",{open:e,event:t,reason:n,nested:p}),null==o||o(e,t,n)})),h=r.useRef(null),v=r.useRef({}),g=r.useState((()=>function(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter((e=>e!==n)))||[])}}}()))[0],b=Va(),y=r.useCallback((e=>{const t=qi(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;u(t),d.refs.setReference(t)}),[d.refs]),w=r.useCallback((e=>{(qi(e)||null===e)&&(h.current=e,s(e)),(qi(d.refs.reference.current)||null===d.refs.reference.current||null!==e&&!qi(e))&&d.refs.setReference(e)}),[d.refs]),x=r.useMemo((()=>({...d.refs,setReference:w,setPositionReference:y,domReference:h})),[d.refs,w,y]),E=r.useMemo((()=>({...d.elements,domReference:c})),[d.elements,c]),k=r.useMemo((()=>({...d,refs:x,elements:E,dataRef:v,nodeId:i,floatingId:b,events:g,open:n,onOpenChange:m})),[d,i,b,g,n,m,x,E]);return Ma((()=>{const e=null==f?void 0:f.nodesRef.current.find((e=>e.id===i));e&&(e.context=k)})),r.useMemo((()=>({...d,context:k,refs:x,elements:E})),[d,x,E,k])}const Ua="active",qa="selected";function Ga(e,t,n){const r=new Map,o="item"===n;let i=e;if(o&&e){const{[Ua]:t,[qa]:__,...n}=e;i=n}return{..."floating"===n&&{tabIndex:-1},...i,...t.map((t=>{const r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r})).concat(e).reduce(((e,t)=>t?(Object.entries(t).forEach((t=>{let[n,i]=t;var a;o&&[Ua,qa].includes(n)||(0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof i&&(null==(a=r.get(n))||a.push(i),e[n]=function(){for(var e,t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];return null==(e=r.get(n))?void 0:e.map((e=>e(...o))).find((e=>void 0!==e))})):e[n]=i)})),e):e),{})}}function Ka(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}let Ya=(0,r.createContext)({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});Ya.displayName="FloatingContext";let Xa=(0,r.createContext)(null);Xa.displayName="PlacementContext";function Qa({children:e,enabled:t=!0}){let[n,o]=(0,r.useState)(null),[i,a]=(0,r.useState)(0),s=(0,r.useRef)(null),[l,u]=(0,r.useState)(null);!function(e){Do((()=>{if(!e)return;let t=new MutationObserver((()=>{let t=e.style.maxHeight;parseFloat(t)!==parseInt(t)&&(e.style.maxHeight=`${Math.ceil(parseFloat(t))}px`)}));return t.observe(e,{attributes:!0,attributeFilter:["style"]}),()=>{t.disconnect()}}),[e])}(l);let c=t&&null!==n&&null!==l,{to:d="bottom",gap:f=0,offset:p=0,padding:m=0,inner:h}=function(e,t){var n,r,o;let i=Za(null!=(n=null==e?void 0:e.gap)?n:"var(--anchor-gap, 0)",t),a=Za(null!=(r=null==e?void 0:e.offset)?r:"var(--anchor-offset, 0)",t),s=Za(null!=(o=null==e?void 0:e.padding)?o:"var(--anchor-padding, 0)",t);return{...e,gap:i,offset:a,padding:s}}(n,l),[v,g="center"]=d.split(" ");Do((()=>{c&&a(0)}),[c]);let{refs:b,floatingStyles:y,context:w}=$a({open:c,placement:"selection"===v?"center"===g?"bottom":`bottom-${g}`:"center"===g?`${v}`:`${v}-${g}`,strategy:"absolute",transform:!1,middleware:[ka({mainAxis:"selection"===v?0:f,crossAxis:p}),(S={padding:m},void 0===S&&(S={}),{name:"shift",options:S,async fn(e){const{x:t,y:n,placement:r}=e,{mainAxis:o=!0,crossAxis:i=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=Ri(S,e),l={x:t,y:n},u=await Hi(e,s),c=Mi(Pi(r)),d=Li(c);let f=l[d],p=l[c];if(o){const e="y"===d?"bottom":"right";f=Ai(f+u["y"===d?"top":"left"],f,f-u[e])}if(i){const e="y"===c?"bottom":"right";p=Ai(p+u["y"===c?"top":"left"],p,p-u[e])}const m=a.fn({...e,[d]:f,[c]:p});return{...m,data:{x:m.x-t,y:m.y-n}}}}),"selection"!==v&&Sa({padding:m}),"selection"===v&&h?(k={...h,padding:m,overflowRef:s,offset:i,minItemsVisible:4,referenceOverflowThreshold:m,onFallbackChange(e){var t,n;if(!e)return;let r=w.elements.floating;if(!r)return;let o=parseFloat(getComputedStyle(r).scrollPaddingBottom)||0,i=Math.min(4,r.childElementCount),s=0,l=0;for(let e of null!=(n=null==(t=w.elements.floating)?void 0:t.childNodes)?n:[])if(e instanceof HTMLElement){let t=e.offsetTop,n=t+e.clientHeight+o,a=r.scrollTop,u=a+r.clientHeight;if(!(t>=a&&n<=u)){l=Math.max(0,Math.min(n,u)-Math.max(t,a)),s=e.clientHeight;break}i--}i>=1&&a((e=>{let t=s*i-l+o;return e>=t?e:t}))}},{name:"inner",options:k,async fn(e){const{listRef:t,overflowRef:n,onFallbackChange:r,offset:o=0,index:i=0,minItemsVisible:a=4,referenceOverflowThreshold:s=0,scrollRef:l,...u}=k,{rects:c,elements:{floating:d}}=e,f=t.current[i];if(!f)return{};const p={...e,...await ka(-f.offsetTop-d.clientTop-c.reference.height/2-f.offsetHeight/2-o).fn(e)},m=(null==l?void 0:l.current)||d,h=await Ea(Ka(p,m.scrollHeight),u),v=await Ea(p,{...u,elementContext:"reference"}),g=Math.max(0,h.top),b=p.y+g,y=Math.max(0,m.scrollHeight-g-Math.max(0,h.bottom));return m.style.maxHeight=y+"px",m.scrollTop=g,r&&(m.offsetHeight<f.offsetHeight*Math.min(a,t.current.length-1)-1||v.top>=-s||v.bottom>=-s?(0,Vr.flushSync)((()=>r(!0))):(0,Vr.flushSync)((()=>r(!1)))),n&&(n.current=await Ea(Ka({...p,y:b},m.offsetHeight),u)),{y:b}}}):null,Oa({padding:m,apply({availableWidth:e,availableHeight:t,elements:n}){Object.assign(n.floating.style,{overflow:"auto",maxWidth:`${e}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${t}px)`})}})].filter(Boolean),whileElementsMounted:xa}),[x=v,E=g]=w.placement.split("-");var k,S;"selection"===v&&(x="selection");let O=(0,r.useMemo)((()=>({anchor:[x,E].filter(Boolean).join(" ")})),[x,E]),T=function(e,t){const{open:n,elements:o}=e,{enabled:i=!0,overflowRef:a,scrollRef:s,onChange:l}=t,u=Na(l),c=r.useRef(!1),d=r.useRef(null),f=r.useRef(null);return r.useEffect((()=>{if(!i)return;function e(e){if(e.ctrlKey||!t||null==a.current)return;const n=e.deltaY,r=a.current.top>=-.5,o=a.current.bottom>=-.5,i=t.scrollHeight-t.clientHeight,s=n<0?-1:1,l=n<0?"max":"min";t.scrollHeight<=t.clientHeight||(!r&&n>0||!o&&n<0?(e.preventDefault(),(0,Vr.flushSync)((()=>{u((e=>e+Math[l](n,i*s)))}))):/firefox/i.test(function(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}())&&(t.scrollTop+=n))}const t=(null==s?void 0:s.current)||o.floating;return n&&t?(t.addEventListener("wheel",e),requestAnimationFrame((()=>{d.current=t.scrollTop,null!=a.current&&(f.current={...a.current})})),()=>{d.current=null,f.current=null,t.removeEventListener("wheel",e)}):void 0}),[i,n,o.floating,a,s,u]),r.useMemo((()=>i?{floating:{onKeyDown(){c.current=!0},onWheel(){c.current=!1},onPointerMove(){c.current=!1},onScroll(){const e=(null==s?void 0:s.current)||o.floating;if(a.current&&e&&c.current){if(null!==d.current){const t=e.scrollTop-d.current;(a.current.bottom<-.5&&t<-1||a.current.top<-.5&&t>1)&&(0,Vr.flushSync)((()=>u((e=>e+t))))}requestAnimationFrame((()=>{d.current=e.scrollTop}))}}}}:{}),[i,a,o.floating,s,u])}(w,{overflowRef:s,onChange:a}),{getReferenceProps:C,getFloatingProps:F}=function(e){void 0===e&&(e=[]);const t=e,n=r.useCallback((t=>Ga(t,e,"reference")),t),o=r.useCallback((t=>Ga(t,e,"floating")),t),i=r.useCallback((t=>Ga(t,e,"item")),e.map((e=>null==e?void 0:e.item)));return r.useMemo((()=>({getReferenceProps:n,getFloatingProps:o,getItemProps:i})),[n,o,i])}([T]),A=Vo((e=>{u(e),b.setFloating(e)}));return r.createElement(Xa.Provider,{value:o},r.createElement(Ya.Provider,{value:{setFloating:A,setReference:b.setReference,styles:y,getReferenceProps:C,getFloatingProps:F,slot:O}},e))}function Za(e,t,n=void 0){let o=Io(),i=Vo(((e,t)=>{if(null==e)return[n,null];if("number"==typeof e)return[e,null];if("string"==typeof e){if(!t)return[n,null];let r=es(e,t);return[r,n=>{let i=Ja(e);{let a=i.map((e=>window.getComputedStyle(t).getPropertyValue(e)));o.requestAnimationFrame((function s(){o.nextFrame(s);let l=!1;for(let[e,n]of i.entries()){let r=window.getComputedStyle(t).getPropertyValue(n);if(a[e]!==r){a[e]=r,l=!0;break}}if(!l)return;let u=es(e,t);r!==u&&(n(u),r=u)}))}return o.dispose}]}return[n,null]})),a=(0,r.useMemo)((()=>i(e,t)[0]),[e,t]),[s=a,l]=(0,r.useState)();return Do((()=>{let[n,r]=i(e,t);if(l(n),r)return r(l)}),[e,t]),s}function Ja(e){let t=/var\((.*)\)/.exec(e);if(t){let e=t[1].indexOf(",");if(-1===e)return[t[1]];let n=t[1].slice(0,e).trim(),r=t[1].slice(e+1).trim();return r?[n,...Ja(r)]:[n]}return[]}function es(e,t){let n=document.createElement("div");t.appendChild(n),n.style.setProperty("margin-top","0px","important"),n.style.setProperty("margin-top",e,"important");let r=parseFloat(window.getComputedStyle(n).marginTop)||0;return t.removeChild(n),r}let ts=(0,r.createContext)(null);ts.displayName="OpenClosedContext";var ns=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(ns||{});function rs(){return(0,r.useContext)(ts)}function os({value:e,children:t}){return r.createElement(ts.Provider,{value:e},t)}function is(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=""===(null==t?void 0:t.getAttribute("disabled"));return(!r||!function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r}var as=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(as||{});function ss(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),o=null!=r?r:-1;switch(e.focus){case 0:for(let e=0;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 1:-1===o&&(o=n.length);for(let e=o-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 2:for(let e=o+1;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 3:for(let e=n.length-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 4:for(let r=0;r<n.length;++r)if(t.resolveId(n[r],r,n)===e.id)return r;return r;case 5:return null;default:!function(e){throw new Error("Unexpected object: "+e)}(e)}}function ls(...e){return Array.from(new Set(e.flatMap((e=>"string"==typeof e?e.split(" "):[])))).filter(Boolean).join(" ")}var us,cs=((us=cs||{})[us.None=0]="None",us[us.RenderStrategy=1]="RenderStrategy",us[us.Static=2]="Static",us),ds=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(ds||{});function fs({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:i=!0,name:a,mergeRefs:s}){s=null!=s?s:ms;let l=hs(t,e);if(i)return ps(l,n,r,a,s);let u=null!=o?o:0;if(2&u){let{static:e=!1,...t}=l;if(e)return ps(t,n,r,a,s)}if(1&u){let{unmount:e=!0,...t}=l;return qo(e?0:1,{0:()=>null,1:()=>ps({...t,hidden:!0,style:{display:"none"}},n,r,a,s)})}return ps(l,n,r,a,s)}function ps(e,t={},n,o,i){let{as:a=n,children:s,refName:l="ref",...u}=ys(e,["unmount","static"]),c=void 0!==e.ref?{[l]:e.ref}:{},d="function"==typeof s?s(t):s;"className"in u&&u.className&&"function"==typeof u.className&&(u.className=u.className(t)),u["aria-labelledby"]&&u["aria-labelledby"]===u.id&&(u["aria-labelledby"]=void 0);let f={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)));if(e){f["data-headlessui-state"]=n.join(" ");for(let e of n)f[`data-${e}`]=""}}if(a===r.Fragment&&(Object.keys(bs(u)).length>0||Object.keys(bs(f)).length>0)){if((0,r.isValidElement)(d)&&!(Array.isArray(d)&&d.length>1)){let e=d.props,t=null==e?void 0:e.className,n="function"==typeof t?(...e)=>ls(t(...e),u.className):ls(t,u.className),o=n?{className:n}:{},a=hs(d.props,bs(ys(u,["ref"])));for(let e in f)e in a&&delete f[e];return(0,r.cloneElement)(d,Object.assign({},a,f,c,{ref:i(d.ref,c.ref)},o))}if(Object.keys(bs(u)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${o} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(bs(u)).concat(Object.keys(bs(f))).map((e=>`  - ${e}`)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>`  - ${e}`)).join("\n")].join("\n"))}return(0,r.createElement)(a,Object.assign({},ys(u,["ref"]),a!==r.Fragment&&c,a!==r.Fragment&&f),d)}function ms(...e){return e.every((e=>null==e))?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function hs(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])for(let e in n)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(n[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in n)Object.assign(t,{[e](t,...r){let o=n[e];for(let e of o){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;e(t,...r)}}});return t}function vs(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];for(let e in n)Object.assign(t,{[e](...t){let r=n[e];for(let e of r)null==e||e(...t)}});return t}function gs(e){var t;return Object.assign((0,r.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function bs(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function ys(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}let ws=(0,r.createContext)(void 0);function xs(){return(0,r.useContext)(ws)}function Es({value:e,children:t}){return r.createElement(ws.Provider,{value:e},t)}let ks=(0,r.createContext)(null);function Ss(){let e=(0,r.useContext)(ks);if(null===e){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,Ss),e}return e}function Os(){let[e,t]=(0,r.useState)([]);return[e.length>0?e.join(" "):void 0,(0,r.useMemo)((()=>function(e){let n=Vo((e=>(t((t=>[...t,e])),()=>t((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),o=(0,r.useMemo)((()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value})),[n,e.slot,e.name,e.props,e.value]);return r.createElement(ks.Provider,{value:o},e.children)}),[t])]}ks.displayName="DescriptionContext";let Ts=gs((function(e,t){let n=(0,r.useId)(),o=xs(),{id:i=`headlessui-description-${n}`,...a}=e,s=Ss(),l=bi(t);Do((()=>s.register(i)),[i,s.register]);let u=o||!1,c=(0,r.useMemo)((()=>({...s.slot,disabled:u})),[s.slot,u]);return fs({ourProps:{ref:l,...s.props,id:i},theirProps:a,slot:c,defaultTag:"p",name:s.name||"Description"})})),Cs=Object.assign(Ts,{});var Fs=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(Fs||{});let As=(0,r.createContext)(void 0);function Rs(){return(0,r.useContext)(As)}function Ps({id:e,children:t}){return r.createElement(As.Provider,{value:e},t)}let _s=(0,r.createContext)(null);function Ls(){let e=(0,r.useContext)(_s);if(null===e){let e=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,Ls),e}return e}function Ns(e){var t,n,o;let i=null!=(n=null==(t=(0,r.useContext)(_s))?void 0:t.value)?n:void 0;return(null!=(o=null==e?void 0:e.length)?o:0)>0?[i,...e].filter(Boolean).join(" "):i}function Ms({inherit:e=!1}={}){let t=Ns(),[n,o]=(0,r.useState)([]),i=e?[t,...n].filter(Boolean):n;return[i.length>0?i.join(" "):void 0,(0,r.useMemo)((()=>function(e){let t=Vo((e=>(o((t=>[...t,e])),()=>o((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),n=(0,r.useMemo)((()=>({register:t,slot:e.slot,name:e.name,props:e.props,value:e.value})),[t,e.slot,e.name,e.props,e.value]);return r.createElement(_s.Provider,{value:n},e.children)}),[o])]}_s.displayName="LabelContext";let Is=gs((function(e,t){var n;let o=(0,r.useId)(),i=Ls(),a=Rs(),s=xs(),{id:l=`headlessui-label-${o}`,htmlFor:u=(null!=a?a:null==(n=i.props)?void 0:n.htmlFor),passive:c=!1,...d}=e,f=bi(t);Do((()=>i.register(l)),[l,i.register]);let p=Vo((e=>{let t=e.currentTarget;if(t instanceof HTMLLabelElement&&e.preventDefault(),i.props&&"onClick"in i.props&&"function"==typeof i.props.onClick&&i.props.onClick(e),t instanceof HTMLLabelElement){let e=document.getElementById(t.htmlFor);if(e){let t=e.getAttribute("disabled");if("true"===t||""===t)return;let n=e.getAttribute("aria-disabled");if("true"===n||""===n)return;(e instanceof HTMLInputElement&&("radio"===e.type||"checkbox"===e.type)||"radio"===e.role||"checkbox"===e.role||"switch"===e.role)&&e.click(),e.focus({preventScroll:!0})}}})),m=s||!1,h=(0,r.useMemo)((()=>({...i.slot,disabled:m})),[i.slot,m]),v={ref:f,...i.props,id:l,htmlFor:u,onClick:p};return c&&("onClick"in v&&(delete v.htmlFor,delete v.onClick),"onClick"in d&&delete d.onClick),fs({ourProps:v,theirProps:d,slot:h,defaultTag:u?"label":"div",name:i.name||"Label"})})),Ds=Object.assign(Is,{});function js(){let e=function(){let e="undefined"==typeof document;return"useSyncExternalStore"in o&&(e=>e.useSyncExternalStore)(o)((()=>()=>{}),(()=>!1),(()=>!e))}(),[t,n]=r.useState(_o.isHandoffComplete);return t&&!1===_o.isHandoffComplete&&n(!1),r.useEffect((()=>{!0!==t&&n(!0)}),[t]),r.useEffect((()=>_o.handoff()),[]),!e&&t}let Vs=(0,r.createContext)(!1);let zs=r.Fragment,Hs=gs((function(e,t){let n=e,o=(0,r.useRef)(null),i=bi(function(e,t=!0){return Object.assign(e,{[gi]:t})}((e=>{o.current=e})),t),a=di(o),s=function(e){let t=(0,r.useContext)(Vs),n=(0,r.useContext)(Ws),o=di(e),[i,a]=(0,r.useState)((()=>{var e;if(!t&&null!==n)return null!=(e=n.current)?e:null;if(_o.isServer)return null;let r=null==o?void 0:o.getElementById("headlessui-portal-root");if(r)return r;if(null===o)return null;let i=o.createElement("div");return i.setAttribute("id","headlessui-portal-root"),o.body.appendChild(i)}));return(0,r.useEffect)((()=>{null!==i&&(null!=o&&o.body.contains(i)||null==o||o.body.appendChild(i))}),[i,o]),(0,r.useEffect)((()=>{t||null!==n&&a(n.current)}),[n,a,t]),i}(o),[l]=(0,r.useState)((()=>{var e;return _o.isServer?null:null!=(e=null==a?void 0:a.createElement("div"))?e:null})),u=(0,r.useContext)($s),c=js();return Do((()=>{!s||!l||s.contains(l)||(l.setAttribute("data-headlessui-portal",""),s.appendChild(l))}),[s,l]),Do((()=>{if(l&&u)return u.register(l)}),[u,l]),function(e){let t=Vo(e),n=(0,r.useRef)(!1);(0,r.useEffect)((()=>(n.current=!1,()=>{n.current=!0,No((()=>{n.current&&t()}))})),[t])}((()=>{var e;!s||!l||(l instanceof Node&&s.contains(l)&&s.removeChild(l),s.childNodes.length<=0&&(null==(e=s.parentElement)||e.removeChild(s)))})),c&&s&&l?(0,Vr.createPortal)(fs({ourProps:{ref:i},theirProps:n,slot:{},defaultTag:zs,name:"Portal"}),l):null})),Bs=r.Fragment,Ws=(0,r.createContext)(null),$s=(0,r.createContext)(null),Us=gs((function(e,t){let n=bi(t),{enabled:o=!0,...i}=e;return o?r.createElement(Hs,{...i,ref:n}):fs({ourProps:{ref:n},theirProps:i,slot:{},defaultTag:zs,name:"Portal"})})),qs=gs((function(e,t){let{target:n,...o}=e,i={ref:bi(t)};return r.createElement(Ws.Provider,{value:n},fs({ourProps:i,theirProps:o,defaultTag:Bs,name:"Popover.Group"}))})),Gs=Object.assign(Us,{Group:qs});var Ks=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Ks||{}),Ys=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(Ys||{}),Xs=(e=>(e[e.OpenMenu=0]="OpenMenu",e[e.CloseMenu=1]="CloseMenu",e[e.GoToItem=2]="GoToItem",e[e.Search=3]="Search",e[e.ClearSearch=4]="ClearSearch",e[e.RegisterItem=5]="RegisterItem",e[e.UnregisterItem=6]="UnregisterItem",e))(Xs||{});function Qs(e,t=(e=>e)){let n=null!==e.activeItemIndex?e.items[e.activeItemIndex]:null,r=ai(t(e.items.slice()),(e=>e.dataRef.current.domRef.current)),o=n?r.indexOf(n):null;return-1===o&&(o=null),{items:r,activeItemIndex:o}}let Zs={1:e=>1===e.menuState?e:{...e,activeItemIndex:null,menuState:1},0:e=>0===e.menuState?e:{...e,__demoMode:!1,menuState:0},2:(e,t)=>{var n,r,o,i,a;if(1===e.menuState)return e;let s={...e,searchQuery:"",activationTrigger:null!=(n=t.trigger)?n:1,__demoMode:!1};if(t.focus===as.Nothing)return{...s,activeItemIndex:null};if(t.focus===as.Specific)return{...s,activeItemIndex:e.items.findIndex((e=>e.id===t.id))};if(t.focus===as.Previous){let n=e.activeItemIndex;if(null!==n){let i=e.items[n].dataRef.current.domRef,a=ss(t,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});if(null!==a){let t=e.items[a].dataRef.current.domRef;if((null==(r=i.current)?void 0:r.previousElementSibling)===t.current||null===(null==(o=t.current)?void 0:o.previousElementSibling))return{...s,activeItemIndex:a}}}}else if(t.focus===as.Next){let n=e.activeItemIndex;if(null!==n){let r=e.items[n].dataRef.current.domRef,o=ss(t,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});if(null!==o){let t=e.items[o].dataRef.current.domRef;if((null==(i=r.current)?void 0:i.nextElementSibling)===t.current||null===(null==(a=t.current)?void 0:a.nextElementSibling))return{...s,activeItemIndex:o}}}}let l=Qs(e),u=ss(t,{resolveItems:()=>l.items,resolveActiveIndex:()=>l.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...s,...l,activeItemIndex:u}},3:(e,t)=>{let n=""!==e.searchQuery?0:1,r=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeItemIndex?e.items.slice(e.activeItemIndex+n).concat(e.items.slice(0,e.activeItemIndex+n)):e.items).find((e=>{var t;return(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))&&!e.dataRef.current.disabled})),i=o?e.items.indexOf(o):-1;return-1===i||i===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:i,activationTrigger:1}},4:e=>""===e.searchQuery?e:{...e,searchQuery:"",searchActiveItemIndex:null},5:(e,t)=>{let n=Qs(e,(e=>[...e,{id:t.id,dataRef:t.dataRef}]));return{...e,...n}},6:(e,t)=>{let n=Qs(e,(e=>{let n=e.findIndex((e=>e.id===t.id));return-1!==n&&e.splice(n,1),e}));return{...e,...n,activationTrigger:1}}},Js=(0,r.createContext)(null);function el(e){let t=(0,r.useContext)(Js);if(null===t){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,el),t}return t}function tl(e,t){return qo(t.type,Zs,e,t)}Js.displayName="MenuContext";let nl=r.Fragment,rl=cs.RenderStrategy|cs.Static,ol=r.Fragment,il=gs((function(e,t){let{__demoMode:n=!1,...o}=e,i=(0,r.useReducer)(tl,{__demoMode:n,menuState:n?0:1,buttonRef:(0,r.createRef)(),itemsRef:(0,r.createRef)(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:a,itemsRef:s,buttonRef:l},u]=i,c=bi(t);ci([l,s],((e,t)=>{var n;u({type:1}),ni(t,ti.Loose)||(e.preventDefault(),null==(n=l.current)||n.focus())}),0===a);let d=Vo((()=>{u({type:1})})),f=(0,r.useMemo)((()=>({open:0===a,close:d})),[a,d]),p={ref:c};return r.createElement(Qa,null,r.createElement(Js.Provider,{value:i},r.createElement(os,{value:qo(a,{0:ns.Open,1:ns.Closed})},fs({ourProps:p,theirProps:o,slot:f,defaultTag:nl,name:"Menu"}))))})),al=gs((function(e,t){var n;let o=(0,r.useId)(),{id:i=`headlessui-menu-button-${o}`,disabled:a=!1,autoFocus:s=!1,...l}=e,[u,c]=el("Menu.Button"),d=(0,r.useContext)(Ya).getReferenceProps,f=bi(u.buttonRef,t,(0,r.useContext)(Ya).setReference),p=Io(),m=Vo((e=>{switch(e.key){case Fs.Space:case Fs.Enter:case Fs.ArrowDown:e.preventDefault(),e.stopPropagation(),c({type:0}),p.nextFrame((()=>c({type:2,focus:as.First})));break;case Fs.ArrowUp:e.preventDefault(),e.stopPropagation(),c({type:0}),p.nextFrame((()=>c({type:2,focus:as.Last})))}})),h=Vo((e=>{e.key===Fs.Space&&e.preventDefault()})),v=Vo((e=>{if(is(e.currentTarget))return e.preventDefault();a||(0===u.menuState?(c({type:1}),p.nextFrame((()=>{var e;return null==(e=u.buttonRef.current)?void 0:e.focus({preventScroll:!0})}))):(e.preventDefault(),c({type:0})))})),{isFocusVisible:g,focusProps:b}=ko({autoFocus:s}),{isHovered:y,hoverProps:w}=Ao({isDisabled:a}),{pressed:x,pressProps:E}=function({disabled:e=!1}={}){let t=(0,r.useRef)(null),[n,o]=(0,r.useState)(!1),i=Io(),a=Vo((()=>{t.current=null,o(!1),i.dispose()})),s=Vo((e=>{if(i.dispose(),null===t.current){t.current=e.currentTarget,o(!0);{let n=Lo(e.currentTarget);i.addEventListener(n,"pointerup",a,!1),i.addEventListener(n,"pointermove",(e=>{if(t.current){let n=function(e){let t=e.width/2,n=e.height/2;return{top:e.clientY-n,right:e.clientX+t,bottom:e.clientY+n,left:e.clientX-t}}(e);o(function(e,t){return!(!e||!t||e.right<t.left||e.left>t.right||e.bottom<t.top||e.top>t.bottom)}(n,t.current.getBoundingClientRect()))}}),!1),i.addEventListener(n,"pointercancel",a,!1)}}}));return{pressed:n,pressProps:e?{}:{onPointerDown:s,onPointerUp:a,onClick:a}}}({disabled:a}),k=(0,r.useMemo)((()=>({open:0===u.menuState,active:x||0===u.menuState,disabled:a,hover:y,focus:g,autofocus:s})),[u,y,g,x,a,s]);return fs({ourProps:vs(d(),{ref:f,id:i,type:pi(e,u.buttonRef),"aria-haspopup":"menu","aria-controls":null==(n=u.itemsRef.current)?void 0:n.id,"aria-expanded":0===u.menuState,disabled:a||void 0,autoFocus:s,onKeyDown:m,onKeyUp:h,onClick:v},b,w,E),theirProps:l,slot:k,defaultTag:"button",name:"Menu.Button"})})),sl=gs((function(e,t){var n,o;let i=(0,r.useId)(),{id:a=`headlessui-menu-items-${i}`,anchor:s,portal:l=!1,modal:u=!0,...c}=e,d=function(e){return(0,r.useMemo)((()=>e?"string"==typeof e?{to:e}:e:null),[e])}(s),[f,p]=el("Menu.Items"),[m,h]=function(e=null){!1===e&&(e=null),"string"==typeof e&&(e={to:e});let t=(0,r.useContext)(Xa),n=(0,r.useMemo)((()=>e),[JSON.stringify(e,"undefined"!=typeof HTMLElement?(e,t)=>t instanceof HTMLElement?t.outerHTML:t:void 0)]);Do((()=>{null==t||t(null!=n?n:null)}),[t,n]);let o=(0,r.useContext)(Ya);return(0,r.useMemo)((()=>[o.setFloating,e?o.styles:{}]),[o.setFloating,e,o.styles])}(d),v=function(){let{getFloatingProps:e,slot:t}=(0,r.useContext)(Ya);return(0,r.useCallback)(((...n)=>Object.assign({},e(...n),{"data-anchor":t.anchor})),[e,t])}(),g=bi(f.itemsRef,t,d?m:null),b=di(f.itemsRef);d&&(l=!0);let y=Io(),w=rs(),x=null!==w?(w&ns.Open)===ns.Open:0===f.menuState;Uo(f.buttonRef,(()=>p({type:1})),x),function(e,t,n=(()=>[document.body])){!function(e,t,n=(()=>({containers:[]}))){let o=function(e){return(0,r.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot)}(vi),i=e?o.get(e):void 0,a=!!i&&i.count>0;Do((()=>{if(e&&t)return vi.dispatch("PUSH",e,n),()=>vi.dispatch("POP",e,n)}),[t,e])}(e,t,(e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}}))}(b,!f.__demoMode&&u&&0===f.menuState),$o({allowed:Vo((()=>[f.buttonRef.current,f.itemsRef.current]))},!f.__demoMode&&u&&0===f.menuState);let E=!function(e,t=!0){let n=(0,r.useRef)({left:0,top:0});if(Do((()=>{let t=e.current;if(!t)return;let r=t.getBoundingClientRect();r&&(n.current=r)}),[t]),null==e.current||!t||e.current===document.activeElement)return!1;let o=e.current.getBoundingClientRect();return o.top!==n.current.top||o.left!==n.current.left}(f.buttonRef,0!==f.menuState)&&x;(0,r.useEffect)((()=>{let e=f.itemsRef.current;e&&0===f.menuState&&e!==(null==b?void 0:b.activeElement)&&e.focus({preventScroll:!0})}),[f.menuState,f.itemsRef,b,f.itemsRef.current]),function({container:e,accept:t,walk:n,enabled:o=!0}){let i=(0,r.useRef)(t),a=(0,r.useRef)(n);(0,r.useEffect)((()=>{i.current=t,a.current=n}),[t,n]),Do((()=>{if(!e||!o)return;let t=Lo(e);if(!t)return;let n=i.current,r=a.current,s=Object.assign((e=>n(e)),{acceptNode:n}),l=t.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,s,!1);for(;l.nextNode();)r(l.currentNode)}),[e,o,i,a])}({container:f.itemsRef.current,enabled:0===f.menuState,accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let k=Vo((e=>{var t,n;switch(y.dispose(),e.key){case Fs.Space:if(""!==f.searchQuery)return e.preventDefault(),e.stopPropagation(),p({type:3,value:e.key});case Fs.Enter:if(e.preventDefault(),e.stopPropagation(),p({type:1}),null!==f.activeItemIndex){let{dataRef:e}=f.items[f.activeItemIndex];null==(n=null==(t=e.current)?void 0:t.domRef.current)||n.click()}ri(f.buttonRef.current);break;case Fs.ArrowDown:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:as.Next});case Fs.ArrowUp:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:as.Previous});case Fs.Home:case Fs.PageUp:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:as.First});case Fs.End:case Fs.PageDown:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:as.Last});case Fs.Escape:e.preventDefault(),e.stopPropagation(),p({type:1}),Mo().nextFrame((()=>{var e;return null==(e=f.buttonRef.current)?void 0:e.focus({preventScroll:!0})}));break;case Fs.Tab:e.preventDefault(),e.stopPropagation(),p({type:1}),Mo().microTask((()=>{!function(e,t){si(ei(),t,{relativeTo:e})}(f.buttonRef.current,e.shiftKey?Qo.Previous:Qo.Next)}));break;default:1===e.key.length&&(p({type:3,value:e.key}),y.setTimeout((()=>p({type:4})),350))}})),S=Vo((e=>{e.key===Fs.Space&&e.preventDefault()})),O=(0,r.useMemo)((()=>({open:0===f.menuState})),[f]),T=vs(d?v():{},{"aria-activedescendant":null===f.activeItemIndex||null==(n=f.items[f.activeItemIndex])?void 0:n.id,"aria-labelledby":null==(o=f.buttonRef.current)?void 0:o.id,id:a,onKeyDown:k,onKeyUp:S,role:"menu",tabIndex:0,ref:g,style:{...h,"--button-width":zo(f.buttonRef,!0).width}});return r.createElement(Gs,{enabled:!!l&&(e.static||x)},fs({ourProps:T,theirProps:c,slot:O,defaultTag:"div",features:rl,visible:E,name:"Menu.Items"}))})),ll=gs((function(e,t){let n=(0,r.useId)(),{id:o=`headlessui-menu-item-${n}`,disabled:i=!1,...a}=e,[s,l]=el("Menu.Item"),u=null!==s.activeItemIndex&&s.items[s.activeItemIndex].id===o,c=(0,r.useRef)(null),d=bi(t,c);Do((()=>{if(s.__demoMode||0!==s.menuState||!u||0===s.activationTrigger)return;let e=Mo();return e.requestAnimationFrame((()=>{var e,t;null==(t=null==(e=c.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})),e.dispose}),[s.__demoMode,c,u,s.menuState,s.activationTrigger,s.activeItemIndex]);let f=function(e){let t=(0,r.useRef)(""),n=(0,r.useRef)("");return Vo((()=>{let r=e.current;if(!r)return"";let o=r.innerText;if(t.current===o)return n.current;let i=function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let e=n.split(" ").map((e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():wi(t).trim()}return null})).filter(Boolean);if(e.length>0)return e.join(", ")}return wi(e).trim()}(r).trim().toLowerCase();return t.current=o,n.current=i,i}))}(c),p=(0,r.useRef)({disabled:i,domRef:c,get textValue(){return f()}});Do((()=>{p.current.disabled=i}),[p,i]),Do((()=>(l({type:5,id:o,dataRef:p}),()=>l({type:6,id:o}))),[p,o]);let m=Vo((()=>{l({type:1})})),h=Vo((e=>{if(i)return e.preventDefault();l({type:1}),ri(s.buttonRef.current)})),v=Vo((()=>{if(i)return l({type:2,focus:as.Nothing});l({type:2,focus:as.Specific,id:o})})),g=function(){let e=(0,r.useRef)([-1,-1]);return{wasMoved(t){let n=xi(t);return(e.current[0]!==n[0]||e.current[1]!==n[1])&&(e.current=n,!0)},update(t){e.current=xi(t)}}}(),b=Vo((e=>{g.update(e),!i&&(u||l({type:2,focus:as.Specific,id:o,trigger:0}))})),y=Vo((e=>{g.wasMoved(e)&&(i||u||l({type:2,focus:as.Specific,id:o,trigger:0}))})),w=Vo((e=>{g.wasMoved(e)&&(i||u&&l({type:2,focus:as.Nothing}))})),[x,E]=Ms(),[k,S]=Os(),O=(0,r.useMemo)((()=>({active:u,focus:u,disabled:i,close:m})),[u,i,m]);return r.createElement(E,null,r.createElement(S,null,fs({ourProps:{id:o,ref:d,role:"menuitem",tabIndex:!0===i?void 0:-1,"aria-disabled":!0===i||void 0,"aria-labelledby":x,"aria-describedby":k,disabled:void 0,onClick:h,onFocus:v,onPointerEnter:b,onMouseEnter:b,onPointerMove:y,onMouseMove:y,onPointerLeave:w,onMouseLeave:w},theirProps:a,slot:O,defaultTag:ol,name:"Menu.Item"})))})),ul=gs((function(e,t){let[n,o]=Ms();return r.createElement(o,null,fs({ourProps:{ref:t,"aria-labelledby":n,role:"group"},theirProps:e,slot:{},defaultTag:"div",name:"Menu.Section"}))})),cl=gs((function(e,t){let n=(0,r.useId)(),{id:o=`headlessui-menu-heading-${n}`,...i}=e,a=Ls();return Do((()=>a.register(o)),[o,a.register]),fs({ourProps:{id:o,ref:t,role:"presentation",...a.props},theirProps:i,slot:{},defaultTag:"header",name:"Menu.Heading"})})),dl=gs((function(e,t){return fs({ourProps:{ref:t,role:"separator"},theirProps:e,slot:{},defaultTag:"div",name:"Menu.Separator"})})),fl=Object.assign(il,{Button:al,Items:sl,Item:ll,Section:ul,Heading:cl,Separator:dl});function pl(){let e=(0,r.useRef)(!1);return Do((()=>(e.current=!0,()=>{e.current=!1})),[]),e}function ml(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}function hl(e,...t){e&&t.length>0&&e.classList.add(...t)}function vl(e,...t){e&&t.length>0&&e.classList.remove(...t)}function gl({container:e,direction:t,classes:n,onStart:o,onStop:i}){let a=pl(),s=Io(),l=(0,r.useRef)(!1);Do((()=>{if("idle"===t||!a.current)return;o.current(t);let r=e.current;return r?s.add(function(e,{direction:t,done:n,classes:r,inFlight:o}){let i=Mo(),a=void 0!==n?ml(n):()=>{};"enter"===t&&(e.removeAttribute("hidden"),e.style.display="");let s=qo(t,{enter:()=>r.enter,leave:()=>r.leave}),l=qo(t,{enter:()=>r.enterTo,leave:()=>r.leaveTo}),u=qo(t,{enter:()=>r.enterFrom,leave:()=>r.leaveFrom});return function(e,{inFlight:t,prepare:n}){if(null!=t&&t.current)return void n();let r=e.style.transition;e.style.transition="none",n(),e.offsetHeight,e.style.transition=r}(e,{prepare(){vl(e,...r.base,...r.enter,...r.enterTo,...r.enterFrom,...r.leave,...r.leaveFrom,...r.leaveTo,...r.entered),hl(e,...r.base,...s,...u)},inFlight:o}),o&&(o.current=!0),i.nextFrame((()=>{i.add(function(e,t){let n=ml(t),r=Mo();if(!e)return r.dispose;let{transitionDuration:o,transitionDelay:i}=getComputedStyle(e),[a,s]=[o,i].map((e=>{let[t=0]=e.split(",").filter(Boolean).map((e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e))).sort(((e,t)=>t-e));return t})),l=a+s;if(0!==l){let t=r.group((r=>{let o=r.setTimeout((()=>{n(),r.dispose()}),l);r.addEventListener(e,"transitionrun",(i=>{i.target===i.currentTarget&&(o(),r.addEventListener(e,"transitioncancel",(e=>{e.target===e.currentTarget&&(n(),t())})))}))}));r.addEventListener(e,"transitionend",(e=>{e.target===e.currentTarget&&(n(),r.dispose())}))}else n();return r.dispose}(e,(()=>(vl(e,...r.base,...s),hl(e,...r.base,...r.entered,...l),o&&(o.current=!1),a())))),vl(e,...r.base,...s,...u),hl(e,...r.base,...s,...l)})),i.dispose}(r,{direction:t,classes:n.current,inFlight:l,done(){i.current(t)}})):i.current(t),s.dispose}),[t])}function bl(e=""){return e.split(/\s+/).filter((e=>e.length>1))}function yl(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:Ol)!==r.Fragment||1===r.Children.count(e.children)}let wl=(0,r.createContext)(null);wl.displayName="TransitionContext";var xl=(e=>(e.Visible="visible",e.Hidden="hidden",e))(xl||{});let El=(0,r.createContext)(null);function kl(e){return"children"in e?kl(e.children):e.current.filter((({el:e})=>null!==e.current)).filter((({state:e})=>"visible"===e)).length>0}function Sl(e,t){let n=jo(e),o=(0,r.useRef)([]),i=pl(),a=Io(),s=Vo(((e,t=ds.Hidden)=>{let r=o.current.findIndex((({el:t})=>t===e));-1!==r&&(qo(t,{[ds.Unmount](){o.current.splice(r,1)},[ds.Hidden](){o.current[r].state="hidden"}}),a.microTask((()=>{var e;!kl(o)&&i.current&&(null==(e=n.current)||e.call(n))})))})),l=Vo((e=>{let t=o.current.find((({el:t})=>t===e));return t?"visible"!==t.state&&(t.state="visible"):o.current.push({el:e,state:"visible"}),()=>s(e,ds.Unmount)})),u=(0,r.useRef)([]),c=(0,r.useRef)(Promise.resolve()),d=(0,r.useRef)({enter:[],leave:[],idle:[]}),f=Vo(((e,n,r)=>{u.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter((([t])=>t!==e))),null==t||t.chains.current[n].push([e,new Promise((e=>{u.current.push(e)}))]),null==t||t.chains.current[n].push([e,new Promise((e=>{Promise.all(d.current[n].map((([e,t])=>t))).then((()=>e()))}))]),"enter"===n?c.current=c.current.then((()=>null==t?void 0:t.wait.current)).then((()=>r(n))):r(n)})),p=Vo(((e,t,n)=>{Promise.all(d.current[t].splice(0).map((([e,t])=>t))).then((()=>{var e;null==(e=u.current.shift())||e()})).then((()=>n(t)))}));return(0,r.useMemo)((()=>({children:o,register:l,unregister:s,onStart:f,onStop:p,wait:c,chains:d})),[l,s,o,f,p,d,c])}El.displayName="NestingContext";let Ol=r.Fragment,Tl=cs.RenderStrategy,Cl=gs((function(e,t){let{show:n,appear:o=!1,unmount:i=!0,...a}=e,s=(0,r.useRef)(null),l=bi(...yl(e)?[s,t]:null===t?[]:[t]);js();let u=rs();if(void 0===n&&null!==u&&(n=(u&ns.Open)===ns.Open),void 0===n)throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[c,d]=(0,r.useState)(n?"visible":"hidden"),f=Sl((()=>{n||d("hidden")})),[p,m]=(0,r.useState)(!0),h=(0,r.useRef)([n]);Do((()=>{!1!==p&&h.current[h.current.length-1]!==n&&(h.current.push(n),m(!1))}),[h,n]);let v=(0,r.useMemo)((()=>({show:n,appear:o,initial:p})),[n,o,p]);Uo(s,(()=>d("hidden"))),Do((()=>{n?d("visible"):!kl(f)&&null!==s.current&&d("hidden")}),[n,f]);let g={unmount:i},b=Vo((()=>{var t;p&&m(!1),null==(t=e.beforeEnter)||t.call(e)})),y=Vo((()=>{var t;p&&m(!1),null==(t=e.beforeLeave)||t.call(e)}));return r.createElement(El.Provider,{value:f},r.createElement(wl.Provider,{value:v},fs({ourProps:{...g,as:r.Fragment,children:r.createElement(Fl,{ref:l,...g,...a,beforeEnter:b,beforeLeave:y})},theirProps:{},defaultTag:r.Fragment,features:Tl,visible:"visible"===c,name:"Transition"})))})),Fl=gs((function(e,t){var n,o,i;let{beforeEnter:a,afterEnter:s,beforeLeave:l,afterLeave:u,enter:c,enterFrom:d,enterTo:f,entered:p,leave:m,leaveFrom:h,leaveTo:v,...g}=e,b=(0,r.useRef)(null),y=yl(e),w=bi(...y?[b,t]:null===t?[]:[t]),x=null==(n=g.unmount)||n?ds.Unmount:ds.Hidden,{show:E,appear:k,initial:S}=function(){let e=(0,r.useContext)(wl);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[O,T]=(0,r.useState)(E?"visible":"hidden"),C=function(){let e=(0,r.useContext)(El);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:F,unregister:A}=C;Do((()=>F(b)),[F,b]),Do((()=>{if(x===ds.Hidden&&b.current)return E&&"visible"!==O?void T("visible"):qo(O,{hidden:()=>A(b),visible:()=>F(b)})}),[O,b,F,A,E,x]);let R=jo({base:bl(g.className),enter:bl(c),enterFrom:bl(d),enterTo:bl(f),entered:bl(p),leave:bl(m),leaveFrom:bl(h),leaveTo:bl(v)}),P=jo({beforeEnter:a,afterEnter:s,beforeLeave:l,afterLeave:u}),_=js();Do((()=>{if(y&&_&&"visible"===O&&null===b.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}),[b,O,_,y]);let L=k&&E&&S,N=L?"enter":!_||S&&!k?"idle":E?"enter":"leave",M=function(e=0){let[t,n]=(0,r.useState)(e),o=pl(),i=(0,r.useCallback)((e=>{o.current&&n((t=>t|e))}),[t,o]),a=(0,r.useCallback)((e=>!!(t&e)),[t]),s=(0,r.useCallback)((e=>{o.current&&n((t=>t&~e))}),[n,o]),l=(0,r.useCallback)((e=>{o.current&&n((t=>t^e))}),[n]);return{flags:t,addFlag:i,hasFlag:a,removeFlag:s,toggleFlag:l}}(0),I=Vo((e=>qo(e,{enter:()=>{var e,t;M.addFlag(ns.Opening),null==(t=(e=P.current).beforeEnter)||t.call(e)},leave:()=>{var e,t;M.addFlag(ns.Closing),null==(t=(e=P.current).beforeLeave)||t.call(e)},idle:()=>{}}))),D=Vo((e=>qo(e,{enter:()=>{var e,t;M.removeFlag(ns.Opening),null==(t=(e=P.current).afterEnter)||t.call(e)},leave:()=>{var e,t;M.removeFlag(ns.Closing),null==(t=(e=P.current).afterLeave)||t.call(e)},idle:()=>{}}))),j=(0,r.useRef)(!1),V=Sl((()=>{j.current||(T("hidden"),A(b))}),C);gl({container:b,classes:R,direction:N,onStart:jo((e=>{j.current=!0,V.onStart(b,e,I)})),onStop:jo((e=>{j.current=!1,V.onStop(b,e,D),"leave"===e&&!kl(V)&&(T("hidden"),A(b))}))});let z=g,H={ref:w};return L?z={...z,className:ls(g.className,...R.current.enter,...R.current.enterFrom)}:j.current?(z.className=ls(g.className,null==(o=b.current)?void 0:o.className),""===z.className&&delete z.className):(z.className=ls(g.className,null==(i=b.current)?void 0:i.className,...qo(N,{enter:[...R.current.enterTo,...R.current.entered],leave:R.current.leaveTo,idle:[]})),""===z.className&&delete z.className),r.createElement(El.Provider,{value:V},r.createElement(os,{value:qo(O,{visible:ns.Open,hidden:ns.Closed})|M.flags},fs({ourProps:H,theirProps:z,defaultTag:Ol,features:Tl,visible:"visible"===O,name:"Transition.Child"})))})),Al=gs((function(e,t){let n=null!==(0,r.useContext)(wl),o=null!==rs();return r.createElement(r.Fragment,null,!n&&o?r.createElement(Cl,{ref:t,...e}):r.createElement(Fl,{ref:t,...e}))})),Rl=Object.assign(Cl,{Child:Al,Root:Cl});const Pl=({placement:e="right",width:t="w-48",contentClassName:n="py-1 bg-white",trigger:o,offset:i=[0,0],children:s,disabled:l=!1,mainClassName:u=""})=>{let c="bottom-end";switch(e){case"left":c="bottom-start";break;case"right":default:c="bottom-end";break;case"top-start":c="top-start"}const[d,f]=(e=>{const t=(0,a.useRef)(null),n=(0,a.useRef)(null),r=(0,a.useRef)((()=>{})),o=(0,a.useCallback)((()=>{t.current&&n.current&&(r.current&&r.current(),r.current=Xn(t.current,n.current,e).destroy)}),[t,n,r,e]);return(0,a.useMemo)((()=>[e=>{t.current=e,o()},e=>{n.current=e,o()}]),[t,n,o])})({placement:c,strategy:"fixed",modifiers:[{name:"offset",options:{offset:i}}]});switch(t?.toString()){case"48":t="w-48";break;case"60":t="w-60";break;case"72.5":t="w-[18.25rem]";break;case"80":t="w-80";break;default:t=t||"w-48"}return(0,r.createElement)(fl,{as:"div",className:yt("relative",u)},(({open:e})=>(0,r.createElement)(a.Fragment,null,(0,r.createElement)(al,{ref:d,as:a.Fragment,disabled:l},o),(0,r.createElement)("div",{ref:f,className:"z-50"},(0,r.createElement)(Rl,{show:e,as:a.Fragment,enter:"transition ease-out duration-200",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95"},(0,r.createElement)("div",{className:yt("my-2 rounded-md shadow-lg",t)},(0,r.createElement)(sl,{className:yt("rounded-md focus:outline-none ring-1 ring-black ring-opacity-5",n)},s)))))))};Pl.Item=ll;const _l=Pl,Ll=(0,a.forwardRef)((({variant:e="",isSmall:t=!1,hasSuffixIcon:n=!1,hasPrefixIcon:o=!1,type:i="button",className:a,onClick:s,children:l,disabled:u=!1,id:c="",...d},f)=>{const p=t?"small":"base",m={base:{default:"px-6 py-3",hasPrefixIcon:"pl-4 pr-6 py-3",hasSuffixIcon:"pl-6 pr-4 py-3"},medium:{default:"px-4 py-3 h-11",hasPrefixIcon:"pl-4 pr-6 py-3",hasSuffixIcon:"pl-6 pr-4 py-3"},small:{default:"px-5 py-2 h-[2.625rem]",hasPrefixIcon:"pl-3 pr-5 py-2 h-[2.625rem]",hasSuffixIcon:"pl-5 pr-3 py-2 h-[2.625rem]"}};return(0,r.createElement)("button",{type:i,className:yt("group flex items-center justify-center gap-2 rounded-md focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 transition duration-150 ease-in-out cursor-pointer border-0",{primary:"text-white bg-accent focus-visible:ring-accent border border-solid border-accent",white:"text-accent bg-white border border-solid border-accent focus-visible:ring-accent",dark:"text-white border border-white bg-transparent border-solid",link:"underline border-0 bg-transparent",blank:"bg-transparent border-transparent",gray:"bg-transparent border border-solid border-zip-dark-theme-border text-zip-dark-theme-heading","gray-selected":"bg-zip-dark-theme-border text-white",other:"","gradient-border":"bg-transparent text-zip-app-heading zw-base-bold gradient-border-cover gradient-border-cover-button",gradient:"bg-gradient-to-r from-gradient-color-1 via-46.88 via-gradient-color-2 to-gradient-color-3 text-white zw-base-bold","border-secondary":"text-app-secondary bg-app-light-background border border-app-secondary shadow-sm"}[e],!o&&!n&&m[p].default,o&&m[p].hasPrefixIcon,n&&m[p].hasSuffixIcon,{base:"text-base font-medium",small:"text-sm font-medium"}[p],{base:"rounded-md",small:"rounded"}[p],u&&"cursor-not-allowed opacity-70",a),onClick:e=>{s&&"function"==typeof s&&s(e)},ref:f,disabled:u,...c&&{id:c},...d},l)})),Nl=r.forwardRef((function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))})),Ml=r.forwardRef((function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))})),Il=[{name:"red",label:(0,Ft.__)("Red","zipwp-images"),hex:"#F90F0F"},{name:"orange",label:(0,Ft.__)("Orange","zipwp-images"),hex:"#FD6713"},{name:"yellow",label:(0,Ft.__)("Yellow","zipwp-images"),hex:"#FFE03B"},{name:"green",label:(0,Ft.__)("Green","zipwp-images"),hex:"#4CAF50"},{name:"blue",label:(0,Ft.__)("Blue","zipwp-images"),hex:"#2191F3"},{name:"pink",label:(0,Ft.__)("Pink","zipwp-images"),hex:"#FE4483"},{name:"brown",label:(0,Ft.__)("Brown","zipwp-images"),hex:"#795548"},{name:"black",label:(0,Ft.__)("Black","zipwp-images"),hex:"#000000"},{name:"gray",label:(0,Ft.__)("Gray","zipwp-images"),hex:"#9E9E9E"},{name:"white",label:(0,Ft.__)("White","zipwp-images"),hex:"#FFFFFF"},{name:"turquoise",label:(0,Ft.__)("Turquoise","zipwp-images"),hex:"#29D5C4"},{name:"lilac",label:(0,Ft.__)("Lilac","zipwp-images"),hex:"#CBABFF"},{name:"violet",label:(0,Ft.__)("Violet","zipwp-images"),hex:"#9027FA"},{name:"grayscale",label:(0,Ft.__)("Grayscale","zipwp-images"),hex:"",component:(0,r.createElement)("div",{className:"size-6 rounded-full overflow-hidden"},(0,r.createElement)("div",{className:"w-1/2 h-full bg-[#9E9E9E]"}),(0,r.createElement)("div",{className:"w-1/2 h-full bg-white"}))},{name:"transparent",label:(0,Ft.__)("Transparent","zipwp-images"),hex:"",component:(0,r.createElement)((({className:e=""})=>(0,r.createElement)("svg",{className:e,width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("g",{clipPath:"url(#clip0_33003_155235)"},(0,r.createElement)("rect",{width:"6",height:"6",transform:"translate(9 9)",fill:"#9E9E9E"}),(0,r.createElement)("rect",{width:"6",height:"6",transform:"translate(9 21)",fill:"#9E9E9E"}),(0,r.createElement)("rect",{width:"6",height:"6",transform:"translate(9 -3)",fill:"#9E9E9E"}),(0,r.createElement)("rect",{width:"6",height:"6",transform:"translate(21 9)",fill:"#9E9E9E"}),(0,r.createElement)("rect",{width:"6",height:"6",transform:"translate(-3 9)",fill:"#9E9E9E"}),(0,r.createElement)("rect",{width:"6",height:"6",transform:"translate(15 15)",fill:"#9E9E9E"}),(0,r.createElement)("rect",{width:"6",height:"6",transform:"translate(15 3)",fill:"#9E9E9E"}),(0,r.createElement)("rect",{width:"6",height:"6",transform:"translate(3 15)",fill:"#9E9E9E"}),(0,r.createElement)("rect",{width:"6",height:"6",transform:"translate(3 3)",fill:"#9E9E9E"})),(0,r.createElement)("path",{d:"M0.5 12C0.5 5.64873 5.64873 0.5 12 0.5C18.3513 0.5 23.5 5.64873 23.5 12C23.5 18.3513 18.3513 23.5 12 23.5C5.64873 23.5 0.5 18.3513 0.5 12Z",stroke:"black",strokeOpacity:"0.08"}),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"clip0_33003_155235"},(0,r.createElement)("path",{d:"M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z",fill:"white"}))))),{className:"size-6 rounded-full"})}],Dl=({value:e,onChange:t})=>{const n=e=>()=>{"function"==typeof t&&t(e)};return(0,r.createElement)(_l,{contentClassName:"p-3 bg-white space-y-4",trigger:(0,r.createElement)("div",{className:"flex items-center justify-between gap-2 min-w-[72px] pl-4 py-2 pr-2 cursor-pointer rounded border border-solid border-border-primary"},e?(0,r.createElement)("span",{className:"flex place-items-center size-6 rounded-full border border-solid border-black/10 [&>*]:shrink-0",...!!e?.hex&&{style:{backgroundColor:e.hex}}},!!e?.component&&e.component):(0,r.createElement)("span",{className:"text-sm font-normal text-body-text leading-[150%]"},(0,Ft.__)("Color","zipwp-images")),(0,r.createElement)(Nl,{className:"w-4 h-4 text-app-inactive-icon"}))},(0,r.createElement)("div",{className:"grid grid-cols-5 gap-2 auto-rows-auto"},Il.map((t=>(0,r.createElement)(Zr,{key:t.name,content:t.label},(0,r.createElement)(_l.Item,{as:"div",className:"relative flex place-items-center size-6 rounded-full cursor-pointer border border-solid border-black/10 [&>*]:shrink-0",onClick:n(t),...!!t?.hex&&{style:{backgroundColor:t.hex}}},!!t?.component&&t.component,e?.name===t?.name&&(0,r.createElement)("div",{className:"flex justify-center items-center absolute inset-0 pointer-events-none"},(0,r.createElement)("div",{className:"rounded-full size-4 bg-white flex justify-center items-center"},(0,r.createElement)(Ml,{className:"w-3 h-3 text-body-text"})))))))),(0,r.createElement)(_l.Item,{as:Ll,variant:"blank",className:yt("w-fit py-1 px-2.5 text-xs font-semibold text-body-text border border-solid border-border-primary rounded",!e&&"opacity-50 cursor-not-allowed"),onClick:n(null),disabled:!e},(0,Ft.__)("Clear","zipwp-images")))},jl={all:{value:"all",label:(0,Ft.__)("All Orientation","zipwp-images")},horizontal:{value:"landscape",label:(0,Ft.__)("Landscape","zipwp-images")},vertical:{value:"portrait",label:(0,Ft.__)("Portrait","zipwp-images")},square:{value:"square",label:(0,Ft.__)("Square","zipwp-images")}},Vl=({value:e,onChange:t})=>(0,r.createElement)(_l,{contentClassName:"p-1 bg-white",trigger:(0,r.createElement)("div",{className:"flex items-center justify-between gap-2 min-w-[100px] pl-4 py-2.5 pr-2 cursor-pointer rounded border border-solid border-border-primary"},(0,r.createElement)("span",{className:"text-sm font-normal text-body-text leading-[150%] text-nowrap"},e.label),(0,r.createElement)(Nl,{className:"w-4 h-4 text-app-inactive-icon"}))},Object.values(jl).map(((n,o)=>{return(0,r.createElement)(_l.Item,{as:"div",key:o},(0,r.createElement)("div",{"aria-hidden":"true",className:"flex items-center justify-between gap-2 px-2 py-1.5 text-sm font-normal leading-5 text-zip-body-text hover:bg-background-secondary transition duration-150 ease-in-out rounded bg-white border-none cursor-pointer",onClick:(i=n,()=>{"function"==typeof t&&t(i)})},(0,r.createElement)("span",null,n.label),e.value===n.value&&(0,r.createElement)(Ml,{className:"w-[14px] h-[14px] text-zip-body-text"})));var i}))),zl=({className:e="w-5 h-5 text-white"})=>(0,r.createElement)("svg",{className:yt("animate-spin",e),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},(0,r.createElement)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.createElement)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})),Hl="idle",Bl="pending",Wl=["pexels","pixabay"],$l=(e=20)=>{const t=["aspect-[1/1]","aspect-[1/2]","aspect-[2/1]","aspect-[2/2]","aspect-[3/3]","aspect-[4/3]","aspect-[3/4]"];let n=0;return Array.from({length:e}).map(((e,o)=>(n=n===t.length?0:n,(0,r.createElement)("div",{key:`skeleton-${o}`,className:yt("relative overflow-hidden rounded-lg","bg-slate-300 rounded-lg relative animate-pulse",t[n++])}))))},Ul=()=>{var e;const{dispatch:t}=h(),[n,o]=(0,a.useState)(jl.all),[i,s]=(0,a.useState)(null),[l,u]=(0,a.useState)([]),[c,d]=(0,a.useState)(Hl),[f,p]=(0,a.useState)(1),[v,g]=(0,a.useState)(null),b=(0,a.useRef)(!0),y=(0,a.useRef)(""),w=(0,a.useRef)(new Set),x=(0,a.useRef)([]),{register:E,watch:k,setFocus:S,reset:O}=function(e={}){const t=r.useRef(),n=r.useRef(),[o,i]=r.useState({isDirty:!1,isValidating:!1,isLoading:J(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:J(e.defaultValues)?void 0:e.defaultValues});t.current||(t.current={...Ce(e),formState:o});const a=t.current.control;return a._options=e,function(e){const t=r.useRef(e);t.current=e,r.useEffect((()=>{const n=!e.disabled&&t.current.subject&&t.current.subject.subscribe({next:t.current.next});return()=>{n&&n.unsubscribe()}}),[e.disabled])}({subject:a._subjects.state,next:e=>{((e,t,n,r)=>{n(e);const{name:o,...i}=e;return H(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find((e=>t[e]===(!r||j)))})(e,a._proxyFormState,a._updateFormState,!0)&&i({...a._formState})}}),r.useEffect((()=>a._disableForm(e.disabled)),[a,e.disabled]),r.useEffect((()=>{if(a._proxyFormState.isDirty){const e=a._getDirty();e!==o.isDirty&&a._subjects.state.next({isDirty:e})}}),[a,o.isDirty]),r.useEffect((()=>{e.values&&!he(e.values,n.current)?(a._reset(e.values,a._options.resetOptions),n.current=e.values,i((e=>({...e})))):a._resetDefaultValues()}),[e.values,a]),r.useEffect((()=>{e.errors&&a._setErrors(e.errors)}),[e.errors,a]),r.useEffect((()=>{a._state.mount||(a._updateValid(),a._state.mount=!0),a._state.watch&&(a._state.watch=!1,a._subjects.state.next({...a._formState})),a._removeUnmounted()})),r.useEffect((()=>{e.shouldUnregister&&a._subjects.values.next({values:a._getWatch()})}),[e.shouldUnregister,a]),t.current.formState=((e,t,n,r=!0)=>{const o={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(o,i,{get:()=>{const o=i;return t._proxyFormState[o]!==j&&(t._proxyFormState[o]=!r||j),n&&(n[o]=!0),e[o]}});return o})(o,a),t.current}({defaultValues:{keyword:""}}),T=k("keyword"),C=async(e="",t=n.value,r=i,o="pexels",a=f)=>{try{y.current=e;let n=r;if("pexels"===o&&r&&("transparent"===r.name||"grayscale"===r.name))return 0;"pexels"===o&&r&&"lilac"===r.name&&(n=Il.find((e=>"violet"===e.name))),"pixabay"===o&&r&&"violet"===r.name&&(n=Il.find((e=>"lilac"===e.name)));const i=new AbortController;x.current.push(i);const s=await Rt()({path:"zipwp-images/v1/images",data:{keywords:e,orientation:t,per_page:20..toString(),page:a.toString(),engine:o,...!!n&&{color:n.name}},method:"POST",headers:{"X-WP-Nonce":zipwpImages.rest_api_nonce},signal:i.signal}),l=s.success&&s?.data?s.data?.data:[];return u((e=>[...e,...l])),l.length<20&&w.current.add(o),l?.length}catch(e){throw e}},F=()=>{w.current?.clear()},A=async(e="",t=n.value,r=i,o=f)=>{const a=[];x.current.length&&(x.current.forEach((e=>e.abort())),x.current=[]);try{d(Bl);for(const n of Wl){if(w.current.has(n))continue;const i=await C(e,t,r,n,o);a.push(i)}w.current.size===Wl.length&&(b.current=!1),d("success")}catch(e){if("AbortError"===e.name)return;d("failure"),console.error(e)}},R=((e,t)=>{const[n,r]=(0,a.useState)(e);return(0,a.useEffect)((()=>{const n=setTimeout((()=>{r(e)}),t);return()=>{clearTimeout(n)}}),[e,t]),n})(T,400);(0,a.useEffect)((()=>{F(),p(1),u([]),b.current=!0,A(R,n.value,i,1)}),[R]);const P=e=>async()=>{v||(g(e),await Et(e),g(null))};(0,a.useEffect)((()=>{S("keyword")}),[]);const _=c===Bl?[...l,...$l()]:l,L=null!==i||"all"!==n?.value,N=null!==(e=zipwpImages?.saved_images)&&void 0!==e?e:[];return(0,r.createElement)("div",{className:"flex flex-col justify-start items-center gap-5 h-full"},(0,r.createElement)("div",{className:"w-full h-10 flex items-center justify-between gap-3"},(0,r.createElement)("div",{className:"w-full max-w-[800px]"},(0,r.createElement)(kt,{className:"w-full",name:"keyword",placeholder:"Search images",register:E,prefixIcon:T?(0,r.createElement)("button",{type:"button",className:"inline-flex items-start justify-center pl-3 bg-transparent p-0 m-0 border-0 outline-none focus:outline-none cursor-pointer",onClick:()=>{F(),O({keyword:""}),u([]),p(1),A("",n.value,i,1)}},(0,r.createElement)(Ot,{className:"w-4 h-4 text-zip-app-inactive-icon"})):(0,r.createElement)("div",{className:"inline-flex items-start justify-center pl-3 bg-transparent p-0 m-0 border-0"},(0,r.createElement)(St,{className:"w-4 h-4 text-zip-app-inactive-icon"})),autoComplete:"off"})),(0,r.createElement)("div",{className:"flex items-center justify-end gap-3"},L&&(0,r.createElement)("span",{className:"text-sm font-normal text-body-text leading-[150%] cursor-pointer text-nowrap",onClick:()=>{s(null),o(jl.all),u([]),p(1),A(T,jl?.all?.value,null,1)},"aria-hidden":"true"},(0,Ft.__)("Clear filter","zipwp-images")),(0,r.createElement)(Dl,{value:i,onChange:e=>{F(),s(e),p(1),u([]),A(T,n.value,e,1)}}),(0,r.createElement)(Vl,{value:n,onChange:e=>{F(),o(e),p(1),u([]),A(T,e.value,i,1)}}))),(0,r.createElement)("div",{className:"w-full max-h-full overflow-y-auto",onScroll:e=>{if(c===Bl)return;const{target:{scrollHeight:t,scrollTop:r,clientHeight:o}}=e;r+o>=t-250&&b.current&&(p((e=>e+1)),A(T,n.value,i,f+1))}},!!_.length&&(0,r.createElement)(Dt,{className:"gap-6 [&>div]:gap-6 pb-5",columns:{default:1,220:1,767:2,1024:2,1280:4,1920:5}},_.map((e=>{return e?.optimized_url?(0,r.createElement)("div",{key:e.id,className:yt("flex relative bg-white rounded-lg shadow-md group/overlay",N.includes(e.id)&&"before:absolute before:top-2 before:left-2 before:content-['Imported'] before:bg-black/70 before:px-2 before:py-1 before:rounded before:text-white before:text-xs before:font-semibold")},(0,r.createElement)("img",{src:e.optimized_url,alt:e.alt,className:"w-full h-fit min-h-[250px] object-cover"}),(0,r.createElement)("div",{className:"absolute inset-0 opacity-0 group-hover/overlay:opacity-100 bg-black/50 transition-all duration-300 ease-in-out flex items-center justify-center gap-4"},(0,r.createElement)(Zr,{content:(0,Ft.__)("Full preview","zipwp-images")},(0,r.createElement)("button",{className:"flex items-center justify-center p-3 m-0 bg-transparent border border-solid border-white/25 hover:border-white rounded-full outline-none focus:outline-none cursor-pointer transition-colors duration-150 ease-in-out",onClick:(n=e,()=>{t({type:m,payload:n})})},(0,r.createElement)(Tt,{className:"w-6 h-6 text-white"}))),(0,r.createElement)(Zr,{content:(0,Ft.__)("Insert Image","zipwp-images")},(0,r.createElement)("button",{className:"flex items-center justify-center p-3 m-0 bg-transparent border border-solid border-white/25 hover:border-white rounded-full outline-none focus:outline-none cursor-pointer transition-colors duration-150 ease-in-out",onClick:P(e)},v?.id===e.id?(0,r.createElement)(zl,{className:"w-6 h-6 text-white"}):(0,r.createElement)(Ct,{className:"w-6 h-6 text-white"}))),(0,r.createElement)("div",{className:"absolute bottom-4 left-4"},(0,r.createElement)("span",{className:"text-sm text-white"},e.description?.split(" ").slice(0,3).join(" "),"..."),(0,r.createElement)("br",null),(0,r.createElement)("a",{className:"!text-white/90 text-xs !no-underline",href:e.author_url,target:"_blank",rel:"noreferrer"},"by ",e.author_name," ","via"," ",(0,r.createElement)("span",{className:"capitalize"},e.engine))))):e;var n}))),c!==Hl&&c!==Bl&&!l.length&&(0,r.createElement)("div",{className:"grid grid-cols-1 gap-6 mt-24 h-auto text-center"},(0,r.createElement)("p",{className:"p-0 m-0 text-base text-zip-body-text"},(0,Ft.sprintf)(
// translators: %s: search keyword
// translators: %s: search keyword
(0,Ft.__)("Sorry, we couldn't find anything for “%s”.","zipwp-images"),y.current),(0,r.createElement)("br",null),(0,Ft.__)("Try to refine your search.","zipwp-images"))),c!==Bl&&!b.current&&!!l.length&&(0,r.createElement)("div",{className:"text-center text-sm text-border-primary mt-5 pb-5"},(0,Ft.__)("End of the search results.","zipwp-images"))))},ql=r.forwardRef((function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))})),Gl=r.forwardRef((function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5 8.25 12l7.5-7.5"}))}));function Kl(e,t){return null!==e&&null!==t&&"object"==typeof e&&"object"==typeof t&&"id"in e&&"id"in t?e.id===t.id:e===t}function Yl(e={},t=null,n=[]){for(let[r,o]of Object.entries(e))Ql(n,Xl(t,r),o);return n}function Xl(e,t){return e?e+"["+t+"]":t}function Ql(e,t,n){if(Array.isArray(n))for(let[r,o]of n.entries())Ql(e,Xl(t,r.toString()),o);else n instanceof Date?e.push([t,n.toISOString()]):"boolean"==typeof n?e.push([t,n?"1":"0"]):"string"==typeof n?e.push([t,n]):"number"==typeof n?e.push([t,`${n}`]):null==n?e.push([t,""]):Yl(n,t,e)}var Zl=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(Zl||{});let Jl=gs((function(e,t){var n;let{features:r=1,...o}=e;return fs({ourProps:{ref:t,"aria-hidden":!(2&~r)||(null!=(n=o["aria-hidden"])?n:void 0),hidden:!(4&~r)||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...!(4&~r)&&!!(2&~r)&&{display:"none"}}},theirProps:o,slot:{},defaultTag:"div",name:"Hidden"})})),eu=(0,r.createContext)(null);function tu(e){let[t,n]=(0,r.useState)(null);return r.createElement(eu.Provider,{value:{target:t}},e.children,r.createElement(Jl,{features:Zl.Hidden,ref:n}))}function nu({children:e}){let t=(0,r.useContext)(eu);if(!t)return r.createElement(r.Fragment,null,e);let{target:n}=t;return n?(0,Vr.createPortal)(r.createElement(r.Fragment,null,e),n):null}function ru({data:e,form:t,disabled:n,onReset:o,overrides:i}){let[a,s]=(0,r.useState)(null),l=Io();return(0,r.useEffect)((()=>{if(o&&a)return l.addEventListener(a,"reset",o)}),[a,t,o]),r.createElement(nu,null,r.createElement(ou,{setForm:s,formId:t}),Yl(e).map((([e,o])=>r.createElement(Jl,{features:Zl.Hidden,...bs({key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:t,disabled:n,name:e,value:o,...i})}))))}function ou({setForm:e,formId:t}){return(0,r.useEffect)((()=>{if(t){let n=document.getElementById(t);n&&e(n)}}),[e,t]),t?null:r.createElement(Jl,{features:Zl.Hidden,as:"input",type:"hidden",hidden:!0,readOnly:!0,ref:t=>{if(!t)return;let n=t.closest("form");n&&e(n)}})}var iu=(e=>(e[e.RegisterOption=0]="RegisterOption",e[e.UnregisterOption=1]="UnregisterOption",e))(iu||{});let au={0(e,t){let n=[...e.options,{id:t.id,element:t.element,propsRef:t.propsRef}];return{...e,options:ai(n,(e=>e.element.current))}},1(e,t){let n=e.options.slice(),r=e.options.findIndex((e=>e.id===t.id));return-1===r?e:(n.splice(r,1),{...e,options:n})}},su=(0,r.createContext)(null);function lu(e){let t=(0,r.useContext)(su);if(null===t){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,lu),t}return t}su.displayName="RadioGroupDataContext";let uu=(0,r.createContext)(null);function cu(e){let t=(0,r.useContext)(uu);if(null===t){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,cu),t}return t}function du(e,t){return qo(t.type,au,e,t)}uu.displayName="RadioGroupActionsContext";let fu=gs((function(e,t){let n=(0,r.useId)(),o=xs(),{id:i=`headlessui-radiogroup-${n}`,value:a,defaultValue:s,form:l,name:u,onChange:c,by:d,disabled:f=o||!1,...p}=e,m=function(e=Kl){return(0,r.useCallback)(((t,n)=>{if("string"==typeof e){let r=e;return(null==t?void 0:t[r])===(null==n?void 0:n[r])}return e(t,n)}),[e])}(d),[h,v]=(0,r.useReducer)(du,{options:[]}),g=h.options,[b,y]=Ms(),[w,x]=Os(),E=(0,r.useRef)(null),k=bi(E,t),[S,O]=function(e,t,n){let[o,i]=(0,r.useState)(n),a=void 0!==e,s=(0,r.useRef)(a),l=(0,r.useRef)(!1),u=(0,r.useRef)(!1);return!a||s.current||l.current?!a&&s.current&&!u.current&&(u.current=!0,s.current=a,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")):(l.current=!0,s.current=a,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")),[a?e:o,Vo((e=>(a||i(e),null==t?void 0:t(e))))]}(a,c,s),T=(0,r.useMemo)((()=>g.find((e=>!e.propsRef.current.disabled))),[g]),C=(0,r.useMemo)((()=>g.some((e=>m(e.propsRef.current.value,S)))),[g,S]),F=Vo((e=>{var t;if(f||m(e,S))return!1;let n=null==(t=g.find((t=>m(t.propsRef.current.value,e))))?void 0:t.propsRef.current;return!(null!=n&&n.disabled||(null==O||O(e),0))})),A=Vo((e=>{let t=E.current;if(!t)return;let n=Lo(t),r=g.filter((e=>!1===e.propsRef.current.disabled)).map((e=>e.element.current));switch(e.key){case Fs.Enter:!function(e){var t,n;let r=null!=(t=null==e?void 0:e.form)?t:e.closest("form");if(r){for(let t of r.elements)if(t!==e&&("INPUT"===t.tagName&&"submit"===t.type||"BUTTON"===t.tagName&&"submit"===t.type||"INPUT"===t.nodeName&&"image"===t.type))return void t.click();null==(n=r.requestSubmit)||n.call(r)}}(e.currentTarget);break;case Fs.ArrowLeft:case Fs.ArrowUp:if(e.preventDefault(),e.stopPropagation(),si(r,Qo.Previous|Qo.WrapAround)===Zo.Success){let e=g.find((e=>e.element.current===(null==n?void 0:n.activeElement)));e&&F(e.propsRef.current.value)}break;case Fs.ArrowRight:case Fs.ArrowDown:if(e.preventDefault(),e.stopPropagation(),si(r,Qo.Next|Qo.WrapAround)===Zo.Success){let e=g.find((e=>e.element.current===(null==n?void 0:n.activeElement)));e&&F(e.propsRef.current.value)}break;case Fs.Space:{e.preventDefault(),e.stopPropagation();let t=g.find((e=>e.element.current===(null==n?void 0:n.activeElement)));t&&F(t.propsRef.current.value)}}})),R=Vo((e=>(v({type:0,...e}),()=>v({type:1,id:e.id})))),P=(0,r.useMemo)((()=>({value:S,firstOption:T,containsCheckedOption:C,disabled:f,compare:m,...h})),[S,T,C,f,m,h]),_=(0,r.useMemo)((()=>({registerOption:R,change:F})),[R,F]),L={ref:k,id:i,role:"radiogroup","aria-labelledby":b,"aria-describedby":w,onKeyDown:A},N=(0,r.useMemo)((()=>({value:S})),[S]),M=(0,r.useCallback)((()=>F(s)),[F]);return r.createElement(x,{name:"RadioGroup.Description"},r.createElement(y,{name:"RadioGroup.Label"},r.createElement(uu.Provider,{value:_},r.createElement(su.Provider,{value:P},null!=u&&r.createElement(ru,{disabled:f,data:{[u]:S||"on"},overrides:{type:"radio",checked:null!=S},form:l,onReset:M}),fs({ourProps:L,theirProps:p,slot:N,defaultTag:"div",name:"RadioGroup"})))))})),pu=gs((function(e,t){var n;let o=lu("RadioGroup.Option"),i=cu("RadioGroup.Option"),a=(0,r.useId)(),{id:s=`headlessui-radiogroup-option-${a}`,value:l,disabled:u=o.disabled||!1,autoFocus:c=!1,...d}=e,f=(0,r.useRef)(null),p=bi(f,t),[m,h]=Ms(),[v,g]=Os(),b=jo({value:l,disabled:u});Do((()=>i.registerOption({id:s,element:f,propsRef:b})),[s,i,f,b]);let y=Vo((e=>{var t;if(is(e.currentTarget))return e.preventDefault();i.change(l)&&(null==(t=f.current)||t.focus())})),w=(null==(n=o.firstOption)?void 0:n.id)===s,{isFocusVisible:x,focusProps:E}=ko({autoFocus:c}),{isHovered:k,hoverProps:S}=Ao({isDisabled:u}),O=o.compare(o.value,l),T=vs({ref:p,id:s,role:"radio","aria-checked":O?"true":"false","aria-labelledby":m,"aria-describedby":v,"aria-disabled":!!u||void 0,tabIndex:u?-1:O||!o.containsCheckedOption&&w?0:-1,onClick:u?void 0:y,autoFocus:c},E,S),C=(0,r.useMemo)((()=>({checked:O,disabled:u,active:x,hover:k,focus:x,autofocus:c})),[O,u,k,x,c]);return r.createElement(g,{name:"RadioGroup.Description"},r.createElement(h,{name:"RadioGroup.Label"},fs({ourProps:T,theirProps:d,slot:C,defaultTag:"div",name:"RadioGroup.Option"})))})),mu=gs((function(e,t){var n;let o=lu("Radio"),i=cu("Radio"),a=(0,r.useId)(),s=Rs(),l=xs(),{id:u=s||`headlessui-radio-${a}`,value:c,disabled:d=o.disabled||l||!1,autoFocus:f=!1,...p}=e,m=(0,r.useRef)(null),h=bi(m,t),v=Ns(),g=function(){var e,t;return null!=(t=null==(e=(0,r.useContext)(ks))?void 0:e.value)?t:void 0}(),b=jo({value:c,disabled:d});Do((()=>i.registerOption({id:u,element:m,propsRef:b})),[u,i,m,b]);let y=Vo((e=>{var t;if(is(e.currentTarget))return e.preventDefault();i.change(c)&&(null==(t=m.current)||t.focus())})),{isFocusVisible:w,focusProps:x}=ko({autoFocus:f}),{isHovered:E,hoverProps:k}=Ao({isDisabled:d}),S=(null==(n=o.firstOption)?void 0:n.id)===u,O=o.compare(o.value,c);return fs({ourProps:vs({ref:h,id:u,role:"radio","aria-checked":O?"true":"false","aria-labelledby":v,"aria-describedby":g,"aria-disabled":!!d||void 0,tabIndex:d?-1:O||!o.containsCheckedOption&&S?0:-1,autoFocus:f,onClick:d?void 0:y},x,k),theirProps:p,slot:(0,r.useMemo)((()=>({checked:O,disabled:d,hover:E,focus:w,autofocus:f})),[O,d,E,w,f]),defaultTag:"span",name:"Radio"})})),hu=Ds,vu=Cs,gu=Object.assign(fu,{Option:pu,Radio:mu,Label:hu,Description:vu}),bu=gs((function(e,t){let n=`headlessui-control-${(0,r.useId)()}`,[o,i]=Ms(),[a,s]=Os(),l=xs(),{disabled:u=l||!1,...c}=e,d=(0,r.useMemo)((()=>({disabled:u})),[u]);return r.createElement(Es,{value:u},r.createElement(i,{value:o},r.createElement(s,{value:a},r.createElement(Ps,{id:n},fs({ourProps:{ref:t,disabled:u||void 0,"aria-disabled":u||void 0},theirProps:{...c,children:r.createElement(tu,null,"function"==typeof c.children?c.children(d):c.children)},slot:d,defaultTag:"div",name:"Field"})))))}));function yu({value:e,onChange:t,by:n,children:o}){return(0,r.createElement)(gu,{value:e,onChange:t,...!!n&&{by:n}},o)}yu.Button=({value:e,children:t,disabled:n=!1})=>(0,r.createElement)(bu,{className:"flex items-center gap-2",disabled:n},(0,r.createElement)(mu,{as:a.Fragment,value:e},(({checked:e,disabled:t})=>(0,r.createElement)("span",{className:yt("group flex size-[14px] items-center justify-center rounded-full border border-solid border-zip-body-text bg-white",t&&"opacity-70 cursor-not-allowed")},e&&(0,r.createElement)("span",{className:"size-2 rounded-full bg-zip-body-text"})))),(0,r.createElement)(Ds,{as:a.Fragment},(({disabled:e})=>(0,r.createElement)("label",{className:yt(!!e&&"opacity-70")},t))));const wu=yu,xu=()=>{var e;const{imagePreview:t,dispatch:n}=h(),o=(0,a.useMemo)((()=>t?t.sizes:[]),[t]),[i,s]=(0,a.useState)(null!==(e=o[0])&&void 0!==e?e:null),[l,u]=(0,a.useState)(!1),c=()=>{n({type:m,payload:null})};return(0,a.useEffect)((()=>{t&&s(o[0])}),[t]),!!t&&(0,r.createElement)("div",{className:"absolute inset-0 grid grid-cols-[1fr_380px] grid-rows-1 bg-white"},(0,r.createElement)("div",{className:"flex items-center justify-center p-6"},(0,r.createElement)("img",{className:"w-full h-full max-w-full max-h-full object-contain pointer-events-none",src:t.url,alt:"Preview",draggable:"false"})),(0,r.createElement)("div",{className:"flex flex-col justify-start items-start p-6 bg-wp-background border-l border-r-0 border-y-0 border-wp-border border-solid"},(0,r.createElement)("div",{className:"!space-y-2"},(0,r.createElement)("p",{className:"m-0 text-base font-semibold text-zip-app-heading"},(0,Ft.__)("Image Details","zipwp-images")),!!t?.description&&(0,r.createElement)("p",{className:""},t.description),(0,r.createElement)("a",{href:t.author_url,target:"_blank",rel:"noreferrer"},"by ",t.author_name," via"," ",(0,r.createElement)("span",{className:"capitalize"},t.engine)),(0,r.createElement)("p",null,(0,Ft.__)("Orientation","zipwp-images"),":"," ",(0,r.createElement)("span",{className:"capitalize"},t.orientation))),(0,r.createElement)("hr",{className:"w-full border-t border-b-0 border-solid border-border-tertiary my-6"}),(0,r.createElement)("div",{className:"space-y-2 w-full"},(0,r.createElement)("p",{className:"m-0 text-base font-semibold text-zip-app-heading"},(0,Ft.__)("Choose a size:","zipwp-images")),(0,r.createElement)(wu,{value:i,onChange:s},o.map((e=>(0,r.createElement)(wu.Button,{key:e.id,value:e},(0,r.createElement)("span",{className:"text-zip-body-text text-sm capitalize"},e.id)," ",!!e.width&&(0,r.createElement)("span",{className:"text-zip-app-inactive-icon text-sm"},"W: ",e.width)," ",!!e.height&&(0,r.createElement)("span",{className:"text-zip-app-inactive-icon text-sm"},"H: ",e.height))))),(0,r.createElement)(Ll,{className:"w-full !mt-6 shadow-sm",variant:"primary",onClick:(d={id:t.id,name:xt(i?.url),url:i?.url},async()=>{l||(u(!0),await Et(d),u(!1),setTimeout(c,1e3))}),isSmall:!0},l?(0,r.createElement)(zl,{className:"w-4 h-4 text-white"}):(0,r.createElement)(ql,{className:"w-4 h-4"}),(0,r.createElement)("span",{className:yt(l&&"sr-only")},"Insert Image")),(0,r.createElement)(Ll,{variant:"blank",className:"w-full !mt-5 text-zip-body-text font-semibold text-sm border border-solid border-border-tertiary bg-white",isSmall:!0,onClick:c},(0,r.createElement)(Gl,{className:"w-4 h-4 text-zip-app-inactive-icon"}),(0,r.createElement)("span",null,(0,Ft.__)("Back to All Images","zipwp-images"))))));var d},Eu=()=>(0,r.createElement)("div",{className:"px-5 pt-5 h-[calc(100%_-_1.25rem)]"},(0,r.createElement)(Ul,null),(0,r.createElement)(xu,null)),ku=wp.media.view.Frame.extend({tagName:"div",className:"attachments-browser ast-attachments-browser",images:[],object:[],initialize(){_.defaults(this.options,{filters:!1,search:!0,date:!0,display:!1,sidebar:!0,AttachmentView:wp.media.view.Attachment.Library}),this.createContent()},createContent(){const e=function(e){if(!e)return;const{el:t}=e;return t}(this);wt.set(this),setTimeout((()=>{var t;(t=e)&&(0,a.createRoot)(t).render((0,r.createElement)(Eu,null))}),10)}});!function(e){const t={init(){if(void 0!==wp&&wp.media){const t=wp.media.view.MediaFrame.Post,n=wp.media.view.MediaFrame.Select;wp.media.view.ZipwpImagesAttachmentsBrowser=ku;const r={browseRouter(e){n.prototype.browseRouter.apply(this,arguments),e.set({zipwpimages:{text:zipwpImages.title,priority:70}})},bindHandlers(){zipwpImages.is_customize_preview||t.prototype.bindHandlers.apply(this,arguments),n.prototype.bindHandlers.apply(this,arguments),this.on("content:create:zipwpimages",this.zipwpimages,this)},zipwpimages(t){const n=this.state(),r=new wp.media.view.ZipwpImagesAttachmentsBrowser({controller:this,model:n,AttachmentView:n.get("AttachmentView")});t.view=r,wp.media.view.ZipwpImagesAttachmentsBrowser.object=r,setTimeout((function(){e(document).trigger("ast-image__set-scope")}),100)}};zipwpImages.is_customize_preview||(wp.media.view.MediaFrame.Post=t.extend(r)),wp.media.view.MediaFrame.Select=n.extend(r)}}};e((function(){t.init(),zipwpImages.is_bb_active&&zipwpImages.is_bb_editor&&void 0!==FLBuilder&&null!==FLBuilder._singlePhotoSelector&&FLBuilder._singlePhotoSelector.on("open",(function(){t.init()}))}))}(jQuery)},20:(e,t,n)=>{var r=n(609),o=Symbol.for("react.element"),i=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,n){var r,l={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,r)&&!s.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:o,type:e,key:u,ref:c,props:l,_owner:a.current}}},848:(e,t,n)=>{e.exports=n(20)},63:(e,t,n)=>{var r=n(609),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,s=r.useLayoutEffect,l=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return s((function(){o.value=n,o.getSnapshot=t,u(o)&&c({inst:o})}),[e,n,t]),a((function(){return u(o)&&c({inst:o}),e((function(){u(o)&&c({inst:o})}))}),[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},940:(e,t,n)=>{var r=n(609),o=n(888),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,s=r.useRef,l=r.useEffect,u=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=s(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=u((function(){function e(e){if(!l){if(l=!0,a=e,e=r(e),void 0!==o&&f.hasValue){var t=f.value;if(o(t,e))return s=t}return s=e}if(t=s,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?t:(a=e,s=n)}var a,s,l=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]}),[t,n,r,o]);var p=a(e,d[0],d[1]);return l((function(){f.hasValue=!0,f.value=p}),[p]),c(p),p}},888:(e,t,n)=>{e.exports=n(63)},242:(e,t,n)=>{e.exports=n(940)},609:e=>{e.exports=window.React}},o={};function i(e){var t=o[e];if(void 0!==t)return t.exports;var n=o[e]={exports:{}};return r[e](n,n.exports,i),n.exports}i.m=r,e=[],i.O=(t,n,r,o)=>{if(!n){var a=1/0;for(c=0;c<e.length;c++){for(var[n,r,o]=e[c],s=!0,l=0;l<n.length;l++)(!1&o||a>=o)&&Object.keys(i.O).every((e=>i.O[e](n[l])))?n.splice(l--,1):(s=!1,o<a&&(a=o));if(s){e.splice(c--,1);var u=r();void 0!==u&&(t=u)}}return t}o=o||0;for(var c=e.length;c>0&&e[c-1][2]>o;c--)e[c]=e[c-1];e[c]=[n,r,o]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var o=Object.create(null);i.r(o);var a={};t=t||[null,n({}),n([]),n(n)];for(var s=2&r&&e;"object"==typeof s&&!~t.indexOf(s);s=n(s))Object.getOwnPropertyNames(s).forEach((t=>a[t]=()=>e[t]));return a.default=()=>e,i.d(o,a),o},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e={792:0,85:0};i.O.j=t=>0===e[t];var t=(t,n)=>{var r,o,[a,s,l]=n,u=0;if(a.some((t=>0!==e[t]))){for(r in s)i.o(s,r)&&(i.m[r]=s[r]);if(l)var c=l(i)}for(t&&t(n);u<a.length;u++)o=a[u],i.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return i.O(c)},n=globalThis.webpackChunkzipwp_images=globalThis.webpackChunkzipwp_images||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var a=i.O(void 0,[85],(()=>i(472)));a=i.O(a)})();