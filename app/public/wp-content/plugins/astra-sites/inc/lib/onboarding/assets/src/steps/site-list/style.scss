.step-site-list,
.starter-templates-ai-steps {
	--stc-toggle-dropdown-popup-item-cursor: pointer;
}

.st-other-filters {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #d1d5db;
	padding-bottom: 8px;
}

.st-category-filter {
	flex: 1;
}

.stc-category-list-item {
	border-bottom: 2px solid transparent;
	padding: 0 !important;
	margin: 0 28px 0 0;
	margin-bottom: -26px;
	margin-top: -20px;
	font-size: 14px;
	color: var( --st-color-placeholder );
	font-weight: var( --stc-font-weight-bold );

	&.active {
		border-color: var( --stc-color-heading );
	}
}

.st-type-and-order-filters {
	--stc-toggle-dropdown-popup-item-padding: 5px 17px;
	--stc-toggle-dropdown-popup-padding: 8px 0px;
	display: flex;
}

.st-site-type-filter {
	margin-right: 30px;
}

.st-sites-grid {
	min-height: 100vh;
	margin-top: 65px;
	position: relative;
	.stc-grid-item-title {
		font-size: var( --st-font-size-xs );
		line-height: var( --st-line-height-xs );
		color: var( --st-color-heading );
		padding: 0;
		margin: 0;
	}
}

.stc-site-order-filter,
.stc-site-type-filter {
	width: 110px;

	.stc-toggle-dropdown-popup {
		top: 120%;

		&-item {
			padding: 9px 17px;
			font-size: var( --stc-font-size-xs );
			color: var( --stc-color-body );

			&:first-child:hover,
			&.active:first-child,
			&:last-child:hover,
			&.active:last-child {
				border-radius: 0 0 var( --stc-border-radius-4 ) var( --stc-border-radius-4 );
			}
		}
	}
}

.stc-site-type-filter {
	margin-right: 15px;
}

.st-related-site-message,
.st-suggested-site-message,
.st-sites-found-message {
	text-align: left;
	font-size: var( --st-font-size-xs );
	color: var( --st-color-heading );
}

.step-site-list {
	.step-actions p {
		font-weight: 500;
		font-size: 22px;
		color: #3b3f5c;
	}

	#ist-bashcanvas {
		display: none;
	}
}

.st-sites-found-message {
	position: absolute;
	top: -40px;
}
.site-loading {
	.site-list-screen-wrap {
		opacity: 0;
	}
}

.site-loaded {
	.site-list-loading-skeleton {
		display: none;
		transition: opacity 1s ease-out;
		opacity: 0;
	}
	.site-list-screen-wrap {
		animation: 300ms ease 0s normal forwards 1 fadein;
	}

	@keyframes fadein {
		0% {
			opacity: 0.7;
		}
		100% {
			opacity: 1;
		}
	}
}

.cta-strip-right {
	display: flex;
	justify-content: center;
	align-items: center;
	align-content: center;
	grid-gap: 40px;
}

.stc-extra-text.stc-logo {
	align-items: flex-start;
}
.stc-extra-text::after {
	background: linear-gradient( 90deg, #b809a7 0%, #e90b76 46.88%, #fc8536 100% );
	-webkit-background-clip: text;
	background-clip: text;
	color: transparent;
	font-size: 12px;
	font-weight: 500;
	line-height: 12px;
	text-align: center;
	content: attr( data-extratext );
	text-align: left;
	margin-left: 8px;
}

@media screen and ( min-width: 768px ) {
	.st-other-filters {
		padding-left: 22px;
	}
}
