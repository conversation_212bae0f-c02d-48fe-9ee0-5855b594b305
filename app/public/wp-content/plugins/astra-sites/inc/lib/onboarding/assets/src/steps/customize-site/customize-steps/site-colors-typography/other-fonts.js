export const FONTS = [
	{
		'body-font-family': "'Manrope', sans-serif",
		'body-font-family-slug': 'manrope',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Plus Jakarta Sans', sans-serif",
		'headings-font-family-slug': 'plus-jakarta-sans',
		'headings-font-weight': 600,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'DM Sans', sans-serif",
		'body-font-family-slug': 'dm-sans',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Lexend', sans-serif",
		'headings-font-family-slug': 'lexend',
		'headings-font-weight': 600,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Inter', sans-serif",
		'body-font-family-slug': 'inter',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Work Sans', sans-serif",
		'headings-font-family-slug': 'work-sans',
		'headings-font-weight': 600,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Poppins', sans-serif",
		'body-font-family-slug': 'poppins',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Inter', sans-serif",
		'headings-font-family-slug': 'inter',
		'headings-font-weight': 600,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Lora', serif",
		'body-font-family-slug': 'lora',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Nunito Sans', sans-serif",
		'headings-font-family-slug': 'nunito-sans',
		'headings-font-weight': 600,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Inter', sans-serif",
		'body-font-family-slug': 'inter',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Manrope', sans-serif",
		'headings-font-family-slug': 'manrope',
		'headings-font-weight': 600,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'DM Sans', sans-serif",
		'body-font-family-slug': 'dm-sans',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Outfit', sans-serif",
		'headings-font-family-slug': 'outfit',
		'headings-font-weight': 600,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Inter', sans-serif",
		'body-font-family-slug': 'inter',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'IBM Plex Serif', serif",
		'headings-font-family-slug': 'ibm-plex-serif',
		'headings-font-weight': 600,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Plus Jakarta Sans', sans-serif",
		'body-font-family-slug': 'plus-jakarta-sans',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Crimson Text', serif",
		'headings-font-family-slug': 'crimson-text',
		'headings-font-weight': 600,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'IBM Plex Sans', sans-serif",
		'body-font-family-slug': 'ibm-plex-sans',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'EB Garamond', serif",
		'headings-font-family-slug': 'eb-garamond',
		'headings-font-weight': 600,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
];
