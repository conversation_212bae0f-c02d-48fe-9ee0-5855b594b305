body {
	/* Color */
	--st-color-accent: #2563eb;
	--st-color-accent-hover: #1d4ed8;
	--st-color-heading: #1f2937;
	--st-color-body: #4b5563;
	--st-color-light-gray: #e5e7eb;
	--st-color-placeholder: #6b7280;
	--st-color-white: #ffffff;
	--st-color-favorite: #e91e63;
	--st-background-secondary: #f7f7f9;
	--st-background-primary: #ffffff;
	--st-background-light: #f9fafb;
	--st-tooltip-background: #020617;

	/* Font sizes */
	--st-font-size-xxl: 30px;
	--st-font-size-xl: 24px;
	--st-font-size-l: 20px;
	--st-font-size-m: 18px;
	--st-font-size-s: 16px;
	--st-font-size-xs: 14px;

	/*  Font Weight */
	--st-font-weight-extra-bold: 600;
	--st-font-weight-bold: 500;
	--st-font-weight-normal: 400;

	/*  Font Line Height */
	--st-font-line-height-xl: 36px;
	--st-font-line-height-l: 32px;
	--st-font-line-height-m: 28px;
	--st-font-line-height-s: 24px;
	--st-font-line-height-xs: 20px;

	/* Border */
	--st-border-color: #d1d5db;
	--st-border-radius-6: 6px;
	--st-border-radius-4: 4px;
	--st-border-radius-3: 3px;
	--st-border-radius-2: 2px;
	--st-border-radius-1: 1px;
}

.zw-xxs-medium {
	font-size: 0.625rem;
	font-weight: 500;
	line-height: 0.75rem;
}
.zw-xs-normal {
	font-size: 0.75rem;
	font-weight: 400;
	line-height: 1rem;
}
.zw-xs-medium {
	font-size: 0.75rem;
	font-weight: 500;
	line-height: 1rem;
}
.zw-xs-semibold {
	font-size: 0.75rem;
	font-weight: 600;
	line-height: 1rem;
}
.zw-sm-normal {
	font-size: 0.875rem;
	font-weight: 400;
	line-height: 1.25rem;
}
.zw-sm-medium {
	font-size: 0.875rem;
	font-weight: 500;
	line-height: 1.25rem;
}
.zw-sm-semibold {
	font-size: 0.875rem;
	font-weight: 600;
	line-height: 1.25rem;
}
.zw-base-normal {
	font-size: 1rem;
	font-weight: 400;
	line-height: 1.5rem;
}
.zw-base-medium {
	font-size: 1rem;
	font-weight: 500;
	line-height: 1.5rem;
}
.zw-base-semibold {
	font-size: 1rem;
	font-weight: 600;
	line-height: 1.5rem;
}
.zw-base-bold {
	font-size: 1rem;
	font-weight: 700;
	line-height: 1.5rem;
}

.zw-h1 {
	font-size: 1.875rem;
	font-weight: 700;
	line-height: 2.25rem;
	color: var( --app-heading );
}
.zw-h2 {
	font-size: 1.5rem;
	font-weight: 600;
	line-height: 2rem;
	color: var( --app-heading );
}
.zw-h3 {
	font-size: 1.25rem;
	font-weight: 600;
	line-height: 1.25rem;
	color: var( --app-heading );
}
.zw-h4 {
	font-size: 1.125rem;
	font-weight: 700;
	line-height: 1.75rem;
	color: var( --app-heading );
}
.zw-h5 {
	font-size: 1rem;
	font-weight: 600;
	line-height: 1rem;
	color: var( --app-heading );
}
.zw-h6 {
	font-size: 0.875rem;
	font-weight: 600;
	line-height: 0.875rem;
	color: var( --app-heading );
}
