.step-header {
	--stc-toggle-dropdown-border-color: transparent;
	--stc-toggle-dropdown-popup-item-margin: 0 0 10px 0;
	--stc-toggle-dropdown-padding: 10px 10px 10px 10px;
	--stc-toggle-dropdown-selected-padding: 27px 20px 27px 20px;
	--stc-toggle-dropdown-popup-top: 100%;
	--stc-toggle-dropdown-popup-left: 10px;
	--stc-toggle-dropdown-icon-height: 7px;

	position: relative;

	.row {
		padding: 0;
		position: relative;
		pointer-events: none;
		z-index: 1;

		.st-header-left,
		.st-header-right {
			pointer-events: auto;
		}

		#st-whats-new {
			pointer-events: auto;
			margin: 0 20px 5px 0;

			.whats-new-rss-trigger-button {
				padding: 0;

				svg path {
					fill: var( --st-color-placeholder );
				}
			}
		}
	}

	.st-exit-to-dashboard {
		padding: 26px 26px;
		svg {
			margin: 0;
		}
	}

	.st-page-builder-filter {
		border-left: 1px solid var( --st-border-color );
		border-right: 1px solid var( --st-border-color );
		.st-page-builder-toggle {
			.stc-toggle-dropdown-selected {
				width: fit-content;
				min-width: 228px;
				.stc-logo-image {
					border-radius: unset;
				}
			}
			.stc-toggle-dropdown-popup {
				z-index: 999999;
				--stc-toggle-dropdown-popup-top: 103%;
				border-radius: 0 0 var( --stc-border-radius-4 ) var( --stc-border-radius-4 );
				width: 100%;
				min-width: 228px;
				left: 0;

				.stc-logo-image {
					border-radius: unset;
				}
			}
		}
		+ .col.exit-link {
			border-left-width: 0;
		}
	}
}

@media ( min-width: 768px ) and ( max-width: 820px ) {
	/* CSS rules for screens between 768px and 820px wide */
	.step-header {
		.st-page-builder-filter {
			.st-page-builder-toggle {
				.stc-toggle-dropdown-selected {
					min-width: 0;
					.stc-logo-text {
						display: none;
					}
				}
				.stc-toggle-dropdown-popup {
					min-width: 0;
					.stc-logo-text {
						display: none;
					}
				}
			}
		}
	}
}
