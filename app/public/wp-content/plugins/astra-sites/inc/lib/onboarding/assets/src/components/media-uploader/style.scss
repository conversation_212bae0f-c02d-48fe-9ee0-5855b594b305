.ist-selected-image {
	position: relative;
	margin-top: 20px;
	display: flex;
	margin-bottom: 2em;
	width: 100%;
	text-align: center;
	cursor: pointer;
	min-height: 100px;
	border: 1px dashed var( --st-color-accent );
	border-radius: 2px;
	padding: 20px;

	img {
		max-width: 100%;
		vertical-align: middle;
		width: 80%;
	}

	&:hover .ist-change-logo {
		opacity: 1;
		visibility: visible;
	}
}

.ist-remove-logo {
	cursor: pointer;
	position: absolute;
	top: -10px;
	right: -10px;
	background: #ffffff;
	box-shadow: 2px 0px 4px rgb( 0 0 0 / 15% );
	border-radius: 50%;
	height: 20px;
	width: 20px;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1;
}

.ist-change-logo {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	text-align: center;
	background: #fff;
	padding: 10px 0;
	color: #646464;
	border: 1px dashed var( --st-color-accent );
	transition: 0.3s all ease-in-out;
	margin: 0;
	opacity: 0;
	visibility: hidden;
	cursor: pointer;
	border-width: 1px 0 0 0;
	border-radius: 0;
}

.ist-logo-preview-wrap {
	align-self: center;
	margin: 0 auto;
}

.ist-logo-wrapper {
	position: relative;
}

.astra-sites-ai-logo-wrap {
	display: flex;
	align-items: center;
	justify-content: space-between;
	.control-reset {
		cursor: pointer;
		&.disabled {
			cursor: default;
			svg path {
				fill: var( --st-border-color );
			}
		}
	}
}

/*.step-customizer {
	.step-content {
		button {
			width: 100%;
			margin-top: 30px;
			margin-bottom: 15px;
			&.disabled-btn {
				background-color: var( --st-color-placeholder );
			}
		}
	}
	.step-actions {
		.ist-link:nth-child( 2 ) {
			color: var( --st-color-accent );
			svg {
				fill: var( --st-color-accent );
			}
		}
	}
}*/
