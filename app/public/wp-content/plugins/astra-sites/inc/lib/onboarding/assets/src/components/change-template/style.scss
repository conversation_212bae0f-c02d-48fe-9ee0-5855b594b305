.change-template-wrap {
	padding: 4% 8%;
	background: var( --st-background-secondary );
	display: flex;
	align-items: center;
	align-content: center;
	justify-content: space-between;
	border-bottom: 1px solid var( --st-border-color );

	.template-name h5 {
		color: var( --st-color-body );
		margin-bottom: 5px;
	}

	.stc-grid-item-badge {
		position: unset;
	}

	.label {
		color: var( --st-color-placeholder );
	}
	.change-btn-wrap {
		cursor: pointer;
	}
	.change-btn {
		background: transparent;
		border: 1px solid #626262;
		border-radius: var( --st-border-radius-3 );
		padding: 6px;
		font-weight: var( --st-font-weight-normal );
		font-size: var( --st-font-size-xs );
		line-height: var( --st-font-line-height-l );
		display: flex;
		align-content: center;
		justify-content: center;
		align-items: center;
		&:hover {
			border-color: var( --st-color-heading );
			svg path {
				fill: var( --st-color-heading );
			}
		}
	}
}
