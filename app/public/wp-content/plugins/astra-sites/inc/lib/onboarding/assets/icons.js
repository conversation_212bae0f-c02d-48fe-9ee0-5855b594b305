const ICONS = {
	dashboard: (
		<svg
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M6 18L18 6M6 6L18 18"
				stroke="#6B7280"
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
		</svg>
	),
	tada: (
		<svg
			width="40"
			height="41"
			viewBox="0 0 40 41"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M29.5749 28.3335L3.52519 39.6683C1.82715 40.3441 0.139647 38.6711 0.800662 36.9674L12.1842 10.5077"
				fill="#FFB636"
			/>
			<path
				d="M0.79938 36.9688L1.70407 34.866C1.78165 34.7477 1.85899 34.6292 1.93665 34.511C6.43641 27.656 11.0021 20.8429 15.7238 14.1385L21.0179 19.5651C17.0234 22.3075 4.05305 35.7111 1.33625 39.2664C0.744848 38.7225 0.457426 37.8502 0.79938 36.9688Z"
				fill="#FFD469"
			/>
			<path
				d="M23.78 16.5201C28.6123 21.3524 31.2554 26.5442 29.6834 28.1162C28.1115 29.6882 22.9198 27.0451 18.0873 22.2128C13.2549 17.3804 10.6119 12.1887 12.1839 10.6167C13.7559 9.04464 18.9476 11.6877 23.78 16.5201Z"
				fill="#A06C33"
			/>
			<path
				d="M12.1563 26.1402C12.1563 26.6099 11.7756 26.9906 11.306 26.9906C10.8363 26.9906 10.4556 26.6099 10.4556 26.1402C10.4556 25.6705 10.8363 25.2898 11.306 25.2898C11.7756 25.2898 12.1563 25.6705 12.1563 26.1402Z"
				fill="#F7F9AA"
			/>
			<path
				d="M39.2181 17.2028C39.2181 17.8768 38.6717 18.4232 37.9976 18.4232C37.3236 18.4232 36.7772 17.8768 36.7772 17.2028C36.7772 16.5287 37.3236 15.9823 37.9976 15.9823C38.6717 15.9823 39.2181 16.5287 39.2181 17.2028ZM31.0679 33.4545C30.3938 33.4545 29.8474 34.0009 29.8474 34.675C29.8474 35.349 30.3938 35.8954 31.0679 35.8954C31.7419 35.8954 32.2884 35.349 32.2884 34.675C32.2884 34.0009 31.7419 33.4545 31.0679 33.4545Z"
				fill="#FFB636"
			/>
			<path
				d="M27.8093 6.76157C27.7046 7.46978 27.2471 8.09532 26.5539 8.47782C26.0753 8.74189 25.5125 8.87915 24.9283 8.87915C24.7422 8.87906 24.5564 8.86533 24.3723 8.83806C23.9592 8.77696 23.5457 8.83931 23.2379 9.00907C23.1 9.08517 22.8629 9.25165 22.8244 9.51204C22.786 9.77243 22.9647 10.0004 23.0746 10.1131C23.3135 10.3581 23.6782 10.5335 24.0785 10.5991C24.0895 10.6002 24.1004 10.6006 24.1114 10.6023C25.8004 10.852 27.0068 12.1898 26.8006 13.5845C26.6959 14.2928 26.2383 14.9184 25.545 15.3009C25.0664 15.5649 24.5037 15.7022 23.9196 15.7022C23.7335 15.7021 23.5476 15.6884 23.3635 15.6611C22.9505 15.5999 22.5368 15.6623 22.2291 15.8321C22.0911 15.9082 21.8541 16.0747 21.8156 16.3351C21.7539 16.753 22.267 17.3047 23.1023 17.4281C23.5568 17.4953 23.8707 17.9182 23.8035 18.3727C23.7425 18.786 23.3873 19.0831 22.9817 19.0831C22.9411 19.0831 22.9002 19.0801 22.8589 19.074C21.17 18.8243 19.9636 17.4864 20.1698 16.0917C20.2745 15.3834 20.7321 14.7579 21.4253 14.3754C22.0546 14.0282 22.8294 13.9002 23.6069 14.0152C24.02 14.0762 24.4335 14.0139 24.7413 13.8442C24.8793 13.7681 25.1163 13.6016 25.1548 13.3412C25.2155 12.9301 24.7191 12.3903 23.9077 12.2554C23.8944 12.2541 23.881 12.253 23.8677 12.251C23.0903 12.136 22.3856 11.7895 21.8837 11.275C21.3308 10.7083 21.0738 9.97704 21.1785 9.26868C21.2832 8.56048 21.7408 7.93493 22.434 7.55243C23.0633 7.20525 23.8381 7.07728 24.6156 7.1922C25.0285 7.25314 25.4422 7.19095 25.75 7.02118C25.8879 6.94509 26.125 6.7786 26.1635 6.51821C26.2019 6.25782 26.0232 6.02985 25.9132 5.91712C25.6678 5.66556 25.29 5.48618 24.8768 5.42517C24.4223 5.35798 24.1083 4.93509 24.1755 4.48056C24.2426 4.02603 24.6657 3.71243 25.1201 3.77923C25.8975 3.89423 26.6022 4.24079 27.1041 4.75525C27.6571 5.32204 27.9141 6.05329 27.8093 6.76157Z"
				fill="#BEA4FF"
			/>
			<path
				d="M6.38487 13.0244C6.38487 13.7882 5.76565 14.4075 5.00182 14.4075C4.23799 14.4075 3.61877 13.7882 3.61877 13.0244C3.61877 12.2606 4.23799 11.6414 5.00182 11.6414C5.76565 11.6414 6.38487 12.2605 6.38487 13.0244ZM31.1071 11.4265C30.2246 11.4265 29.5093 12.1419 29.5093 13.0243C29.5093 13.9067 30.2247 14.6221 31.1071 14.6221C31.9896 14.6221 32.7049 13.9067 32.7049 13.0243C32.7049 12.1419 31.9896 11.4265 31.1071 11.4265ZM16.8378 31.4783C15.9553 31.4783 15.24 32.1937 15.24 33.0761C15.24 33.9585 15.9554 34.6739 16.8378 34.6739C17.7203 34.6739 18.4357 33.9585 18.4357 33.0761C18.4357 32.1937 17.7203 31.4783 16.8378 31.4783ZM38.0334 24.1622C38.275 23.6943 38.0916 23.1191 37.6237 22.8775C37.4371 22.7812 37.2145 22.6534 36.9567 22.5055C34.6916 21.206 29.9786 18.5022 24.4292 22.9828C24.0194 23.3135 23.9555 23.9139 24.2863 24.3236C24.617 24.7334 25.2173 24.7975 25.6271 24.4665C30.1611 20.8059 33.821 22.9052 36.0077 24.1596C36.2735 24.3121 36.5246 24.4562 36.7485 24.5718C36.8598 24.6293 36.9813 24.6643 37.1061 24.6748C37.2309 24.6853 37.3565 24.6712 37.4758 24.6331C37.5951 24.5951 37.7058 24.5339 37.8014 24.4531C37.8971 24.3723 37.9759 24.2735 38.0334 24.1622Z"
				fill="#FF6E83"
			/>
			<path
				d="M33.972 5.45348C33.972 5.92317 33.5912 6.30387 33.1216 6.30387C32.6519 6.30387 32.2712 5.92317 32.2712 5.45348C32.2712 4.98379 32.6519 4.60309 33.1216 4.60309C33.5913 4.60301 33.972 4.98379 33.972 5.45348ZM3.61968 1.47832C3.14999 1.47832 2.76929 1.85903 2.76929 2.32871C2.76929 2.7984 3.14999 3.17911 3.61968 3.17911C4.08937 3.17911 4.47015 2.7984 4.47015 2.32871C4.47015 1.85903 4.08937 1.47832 3.61968 1.47832ZM16.9541 12.5765C17.0503 12.3879 17.1689 12.1732 17.2944 11.9458C17.9315 10.7918 18.8038 9.21153 18.8563 7.3709C18.9194 5.15965 17.7677 3.152 15.4335 1.40372C15.0283 1.1002 14.4539 1.18278 14.1506 1.58786C13.8472 1.99301 13.9296 2.56739 14.3348 2.87075C16.1902 4.26043 17.0699 5.71536 17.0242 7.31864C16.9844 8.71489 16.2666 10.0152 15.6898 11.06C15.5569 11.3008 15.4313 11.5282 15.3216 11.7433C15.0916 12.1942 15.2705 12.7461 15.7213 12.9762C15.85 13.0422 15.9926 13.0766 16.1372 13.0766C16.3062 13.0767 16.472 13.0301 16.6162 12.9418C16.7604 12.8535 16.8773 12.7271 16.9541 12.5765Z"
				fill="#59CAFC"
			/>
		</svg>
	),
	tooltip: (
		<svg
			width="18"
			height="18"
			viewBox="0 0 14 14"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M7 0.875C3.61758 0.875 0.875 3.61758 0.875 7C0.875 10.3824 3.61758 13.125 7 13.125C10.3824 13.125 13.125 10.3824 13.125 7C13.125 3.61758 10.3824 0.875 7 0.875ZM7 12.0859C4.1918 12.0859 1.91406 9.8082 1.91406 7C1.91406 4.1918 4.1918 1.91406 7 1.91406C9.8082 1.91406 12.0859 4.1918 12.0859 7C12.0859 9.8082 9.8082 12.0859 7 12.0859Z"
				fill="#3B3F5C"
			/>
			<path
				d="M8.52578 4.32988C8.11562 3.97031 7.57422 3.77344 7 3.77344C6.42578 3.77344 5.88437 3.97168 5.47422 4.32988C5.04766 4.70313 4.8125 5.20488 4.8125 5.74219V5.84609C4.8125 5.90625 4.86172 5.95547 4.92187 5.95547H5.57812C5.63828 5.95547 5.6875 5.90625 5.6875 5.84609V5.74219C5.6875 5.13926 6.27676 4.64844 7 4.64844C7.72324 4.64844 8.3125 5.13926 8.3125 5.74219C8.3125 6.16738 8.01172 6.55703 7.54551 6.73613C7.25566 6.84688 7.00957 7.04102 6.8332 7.29531C6.6541 7.55508 6.56113 7.8668 6.56113 8.18262V8.47656C6.56113 8.53672 6.61035 8.58594 6.67051 8.58594H7.32676C7.38691 8.58594 7.43613 8.53672 7.43613 8.47656V8.16621C7.43684 8.03349 7.47753 7.90405 7.55288 7.7948C7.62824 7.68554 7.73478 7.60152 7.85859 7.55371C8.66523 7.24336 9.18613 6.53242 9.18613 5.74219C9.1875 5.20488 8.95234 4.70313 8.52578 4.32988ZM6.45312 10.0078C6.45312 10.1529 6.51074 10.292 6.6133 10.3945C6.71586 10.4971 6.85496 10.5547 7 10.5547C7.14504 10.5547 7.28414 10.4971 7.3867 10.3945C7.48926 10.292 7.54687 10.1529 7.54687 10.0078C7.54687 9.86277 7.48926 9.72367 7.3867 9.62111C7.28414 9.51855 7.14504 9.46094 7 9.46094C6.85496 9.46094 6.71586 9.51855 6.6133 9.62111C6.51074 9.72367 6.45312 9.86277 6.45312 10.0078Z"
				fill="#3B3F5C"
			/>
		</svg>
	),
	youtube: (
		<svg
			width="70"
			height="50"
			viewBox="0 0 70 50"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			{ ' ' }
			<path
				d="M68.454 7.67676C68.0525 6.19296 67.2693 4.84024 66.1824 3.75328C65.0955 2.66632 63.7428 1.88306 62.259 1.48148C56.8269 0 34.9645 0 34.9645 0C34.9645 0 13.101 0.0448436 7.66888 1.52633C6.18507 1.92793 4.83236 2.71123 3.74545 3.79824C2.65853 4.88525 1.87535 6.23803 1.47388 7.72187C-0.169205 17.3737 -0.806588 32.0808 1.519 41.3465C1.92051 42.8303 2.70371 44.183 3.79062 45.27C4.87753 46.3569 6.23022 47.1402 7.714 47.5418C13.1461 49.0232 35.0091 49.0232 35.0091 49.0232C35.0091 49.0232 56.8718 49.0232 62.3036 47.5418C63.7874 47.1402 65.1402 46.357 66.2271 45.27C67.3141 44.1831 68.0973 42.8303 68.4989 41.3465C70.2319 31.681 70.766 16.9832 68.454 7.67703V7.67676Z"
				fill="#FF0000"
			/>{ ' ' }
			<path
				d="M26 35.0098L44.1368 24.5049L26.0003 14L26 35.0098Z"
				fill="white"
			/>{ ' ' }
		</svg>
	),
	clickToPlay: (
		<svg
			width="301"
			height="210"
			viewBox="0 0 301 210"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			xmlnsXlink="http://www.w3.org/1999/xlink"
		>
			{ ' ' }
			<rect width="301" height="210" fill="url(#pattern0)" />{ ' ' }
			<defs>
				{ ' ' }
				<pattern
					id="pattern0"
					patternContentUnits="objectBoundingBox"
					width="1"
					height="1"
				>
					{ ' ' }
					<use
						xlinkHref="#image0_114_1409"
						transform="translate(-0.00133934) scale(0.00182637 0.0026178)"
					/>{ ' ' }
				</pattern>{ ' ' }
				<image
					id="image0_114_1409"
					width="549"
					height="382"
					xlinkHref="data:image/png;base64,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"
				/>{ ' ' }
			</defs>{ ' ' }
		</svg>
	),
	twitter: (
		<svg
			width="14"
			height="10"
			viewBox="0 0 14 10"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M14 1.18378C13.4848 1.38455 12.9313 1.52024 12.3503 1.58129C12.9434 1.26885 13.3988 0.774108 13.6132 0.184623C13.0494 0.478685 12.4326 0.685871 11.7893 0.797228C11.2654 0.306615 10.5189 0 9.69276 0C8.10649 0 6.82041 1.13033 6.82041 2.52444C6.82041 2.72233 6.84584 2.91498 6.89478 3.0998C4.50767 2.99448 2.39127 1.98946 0.974586 0.462109C0.727398 0.83496 0.585758 1.26866 0.585758 1.73125C0.585758 2.60712 1.09288 3.37978 1.86353 3.83252C1.40741 3.81996 0.961324 3.71168 0.562516 3.51672C0.562352 3.5273 0.562352 3.53787 0.562352 3.54849C0.562352 4.77164 1.55241 5.79199 2.86634 6.02391C2.44338 6.12501 1.99972 6.13981 1.56926 6.06717C1.93473 7.07012 2.99551 7.79996 4.25234 7.82039C3.26933 8.49745 2.03082 8.90106 0.68518 8.90106C0.453305 8.90106 0.224711 8.88909 0 8.86578C1.2711 9.58206 2.78086 10 4.40289 10C9.68609 10 12.5751 6.15321 12.5751 2.81721C12.5751 2.70772 12.5723 2.59885 12.5668 2.4906C13.1291 2.13332 13.6144 1.69079 14 1.18378Z"
				fill="#59CAFC"
			/>
		</svg>
	),
	angleLeft: (
		<svg
			width="12"
			height="18"
			viewBox="0 0 12 18"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M11.115 15.885L4.245 9L11.115 2.115L9 0L0 9L9 18L11.115 15.885Z"
				fill="black"
			/>
		</svg>
	),
	angleRight: (
		<svg
			width="12"
			height="18"
			viewBox="0 0 12 18"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M0.000232837 15.885L6.87023 9L0.000232837 2.115L2.11523 0L11.1152 9L2.11523 18L0.000232837 15.885Z"
				fill="black"
			/>
		</svg>
	),
	sync: (
		<svg
			width="22"
			height="22"
			viewBox="0 0 22 22"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M4.5835 7.33332L8.25016 11H5.50016C5.50016 14.0342 7.966 16.5 11.0002 16.5C11.926 16.5 12.806 16.2708 13.5668 15.8583L14.9052 17.1967C13.7777 17.9117 12.4393 18.3333 11.0002 18.3333C6.9485 18.3333 3.66683 15.0517 3.66683 11H0.916832L4.5835 7.33332ZM16.5002 11C16.5002 7.96582 14.0343 5.49999 11.0002 5.49999C10.0743 5.49999 9.19433 5.72916 8.4335 6.14166L7.09516 4.80332C8.22266 4.08832 9.561 3.66666 11.0002 3.66666C15.0518 3.66666 18.3335 6.94832 18.3335 11H21.0835L17.4168 14.6667L13.7502 11H16.5002Z"
				fill="#6B7280"
			/>
		</svg>
	),
	angleUP: (
		<svg
			width="10"
			height="6"
			viewBox="0 0 10 6"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M4.47171 0.204743L0.220381 4.1544C-0.0734605 4.4274 -0.0734605 4.86883 0.220381 5.13892L0.926852 5.79526C1.22069 6.06825 1.69584 6.06825 1.98656 5.79526L5 2.99564L8.01344 5.79526C8.30728 6.06825 8.78243 6.06825 9.07315 5.79526L9.77962 5.13892C10.0735 4.86592 10.0735 4.42449 9.77962 4.1544L5.52829 0.204743C5.2407 -0.0682478 4.76555 -0.0682478 4.47171 0.204743Z"
				fill="#3B3F5C"
			/>
		</svg>
	),
	angleDown: (
		<svg
			width="10"
			height="6"
			viewBox="0 0 10 6"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			{ ' ' }
			<path
				d="M4.47171 5.79526L0.220381 1.8456C-0.0734605 1.5726 -0.0734605 1.13117 0.220381 0.861084L0.926852 0.204743C1.22069 -0.0682478 1.69584 -0.0682478 1.98656 0.204743L5 3.00436L8.01344 0.204743C8.30728 -0.0682478 8.78243 -0.0682478 9.07315 0.204743L9.77962 0.861084C10.0735 1.13408 10.0735 1.57551 9.77962 1.8456L5.52829 5.79526C5.2407 6.06825 4.76555 6.06825 4.47171 5.79526Z"
				fill="#3B3F5C"
			/>{ ' ' }
		</svg>
	),
	arrowRight: (
		<svg
			width="14"
			height="9"
			viewBox="0 0 14 9"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			{ ' ' }
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M-0.000488281 4.50001C-0.000488281 4.3674 0.0521901 4.24022 0.145958 4.14645C0.239726 4.05268 0.366903 4.00001 0.499512 4.00001H12.2925L9.14551 0.854006C9.05162 0.760119 8.99888 0.632782 8.99888 0.500006C8.99888 0.36723 9.05162 0.239893 9.14551 0.146006C9.2394 0.0521192 9.36674 -0.000625607 9.49951 -0.00062561C9.63229 -0.000625613 9.75963 0.0521192 9.85351 0.146006L13.8535 4.14601C13.9001 4.19245 13.937 4.24763 13.9622 4.30837C13.9874 4.36912 14.0004 4.43424 14.0004 4.50001C14.0004 4.56577 13.9874 4.63089 13.9622 4.69164C13.937 4.75238 13.9001 4.80756 13.8535 4.85401L9.85351 8.85401C9.75963 8.94789 9.63229 9.00064 9.49951 9.00064C9.36674 9.00064 9.2394 8.94789 9.14551 8.85401C9.05162 8.76012 8.99888 8.63278 8.99888 8.50001C8.99888 8.36723 9.05162 8.23989 9.14551 8.14601L12.2925 5.00001H0.499512C0.366903 5.00001 0.239726 4.94733 0.145958 4.85356C0.0521901 4.75979 -0.000488281 4.63261 -0.000488281 4.50001V4.50001Z"
				fill="white"
			/>{ ' ' }
		</svg>
	),
	questionMarkNoFill: (
		<svg
			width="18"
			height="18"
			viewBox="0 0 24 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			{ ' ' }
			<path
				d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
				stroke="#09090B"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
			<path
				d="M9.08984 9C9.32495 8.33167 9.789 7.7681 10.3998 7.40913C11.0106 7.05016 11.7287 6.91894 12.427 7.03871C13.1253 7.15848 13.7587 7.52152 14.2149 8.06353C14.6712 8.60553 14.9209 9.29152 14.9198 10C14.9198 12 11.9198 13 11.9198 13"
				stroke="#09090B"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
			<path
				d="M12 17H12.01"
				stroke="#09090B"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>{ ' ' }
		</svg>
	),
	questionMark: (
		<svg
			width="16"
			height="16"
			viewBox="0 0 16 16"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			{ ' ' }
			<path
				d="M8 0C3.58214 0 0 3.58214 0 8C0 12.4179 3.58214 16 8 16C12.4179 16 16 12.4179 16 8C16 3.58214 12.4179 0 8 0ZM8 12.6429C7.60536 12.6429 7.28571 12.3232 7.28571 11.9286C7.28571 11.5339 7.60536 11.2143 8 11.2143C8.39464 11.2143 8.71429 11.5339 8.71429 11.9286C8.71429 12.3232 8.39464 12.6429 8 12.6429ZM9.12321 8.72322C8.9615 8.78566 8.82235 8.8954 8.72392 9.0381C8.62549 9.18081 8.57235 9.34986 8.57143 9.52321V9.92857C8.57143 10.0071 8.50714 10.0714 8.42857 10.0714H7.57143C7.49286 10.0714 7.42857 10.0071 7.42857 9.92857V9.54464C7.42857 9.13214 7.54821 8.725 7.78393 8.38571C8.01429 8.05357 8.33571 7.8 8.71429 7.65536C9.32143 7.42143 9.71429 6.9125 9.71429 6.35714C9.71429 5.56964 8.94464 4.92857 8 4.92857C7.05536 4.92857 6.28571 5.56964 6.28571 6.35714V6.49286C6.28571 6.57143 6.22143 6.63571 6.14286 6.63571H5.28571C5.20714 6.63571 5.14286 6.57143 5.14286 6.49286V6.35714C5.14286 5.65536 5.45 5 6.00714 4.5125C6.54286 4.04286 7.25 3.78571 8 3.78571C8.75 3.78571 9.45714 4.04464 9.99286 4.5125C10.55 5 10.8571 5.65536 10.8571 6.35714C10.8571 7.38929 10.1768 8.31786 9.12321 8.72322Z"
				fill="#3B3F5C"
			/>{ ' ' }
		</svg>
	),
	favorite: (
		<svg
			width="17"
			height="16"
			viewBox="0 0 17 16"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path d="M8.435 15.4782L7.21193 14.3648C2.8679 10.4257 0 7.82768 0 4.63925C0 2.04127 2.04127 0 4.63925 0C6.10694 0 7.51559 0.683235 8.435 1.76292C9.35442 0.683235 10.7631 0 12.2308 0C14.8287 0 16.87 2.04127 16.87 4.63925C16.87 7.82768 14.0021 10.4257 9.65808 14.3732L8.435 15.4782Z" />
		</svg>
	),
	arrowRightBold: (
		<svg
			width="51"
			height="44"
			viewBox="0 0 51 44"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M0 22C0 21.1712 0.329241 20.3763 0.915292 19.7903C1.50134 19.2042 2.2962 18.875 3.125 18.875H39.3313L25.9125 5.46248C25.3257 4.87569 24.9961 4.07983 24.9961 3.24998C24.9961 2.42013 25.3257 1.62427 25.9125 1.03748C26.4993 0.450689 27.2952 0.121033 28.125 0.121033C28.9548 0.121033 29.7507 0.450689 30.3375 1.03748L49.0875 19.7875C49.3785 20.0778 49.6094 20.4226 49.767 20.8023C49.9245 21.1819 50.0056 21.5889 50.0056 22C50.0056 22.411 49.9245 22.818 49.767 23.1977C49.6094 23.5773 49.3785 23.9222 49.0875 24.2125L30.3375 42.9625C29.7507 43.5493 28.9548 43.8789 28.125 43.8789C27.2952 43.8789 26.4993 43.5493 25.9125 42.9625C25.3257 42.3757 24.9961 41.5798 24.9961 40.75C24.9961 39.9201 25.3257 39.1243 25.9125 38.5375L39.3313 25.125H3.125C2.2962 25.125 1.50134 24.7957 0.915292 24.2097C0.329241 23.6236 0 22.8288 0 22Z"
				fill="black"
			/>
		</svg>
	),
	spinner: (
		<svg
			width="16px"
			height="16px"
			viewBox="0 0 1024 1024"
			className="icon"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				fill="black"
				d="M512 64a32 32 0 0132 32v192a32 32 0 01-64 0V96a32 32 0 0132-32zm0 640a32 32 0 0132 32v192a32 32 0 11-64 0V736a32 32 0 0132-32zm448-192a32 32 0 01-32 32H736a32 32 0 110-64h192a32 32 0 0132 32zm-640 0a32 32 0 01-32 32H96a32 32 0 010-64h192a32 32 0 0132 32zM195.2 195.2a32 32 0 0145.248 0L376.32 331.008a32 32 0 01-45.248 45.248L195.2 240.448a32 32 0 010-45.248zm452.544 452.544a32 32 0 0145.248 0L828.8 783.552a32 32 0 01-45.248 45.248L647.744 692.992a32 32 0 010-45.248zM828.8 195.264a32 32 0 010 45.184L692.992 376.32a32 32 0 01-45.248-45.248l135.808-135.808a32 32 0 0145.248 0zm-452.544 452.48a32 32 0 010 45.248L240.448 828.8a32 32 0 01-45.248-45.248l135.808-135.808a32 32 0 0145.248 0z"
			/>
		</svg>
	),
	cross: (
		<svg
			width="12"
			height="12"
			viewBox="0 0 14 14"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M14 1.41L12.59 0L7 5.59L1.41 0L0 1.41L5.59 7L0 12.59L1.41 14L7 8.41L12.59 14L14 12.59L8.41 7L14 1.41Z"
				fill="#6B7280"
			/>
		</svg>
	),
	reset: (
		<svg
			width="12"
			height="12"
			viewBox="0 0 12 12"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M1.76219 1.7625C2.84969 0.675 4.34219 0 5.99969 0C9.31469 0 11.9922 2.685 11.9922 6C11.9922 9.315 9.31469 12 5.99969 12C3.20219 12 0.869689 10.0875 0.202188 7.5H1.76219C2.37719 9.2475 4.04219 10.5 5.99969 10.5C8.48219 10.5 10.4997 8.4825 10.4997 6C10.4997 3.5175 8.48219 1.5 5.99969 1.5C4.75469 1.5 3.64469 2.0175 2.83469 2.835L5.24969 5.25H-0.000311852V0L1.76219 1.7625Z"
				fill="#6B7280"
			/>
		</svg>
	),
	remove: (
		<svg
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M6 18L18 6M6 6L18 18"
				stroke="#6B7280"
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
		</svg>
	),
	premiumIcon: (
		<svg
			width="18"
			height="18"
			viewBox="0 0 18 18"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<g clipPath="url(#clip0_26119_59306)">
				<path
					d="M18 9C18 4.02944 13.9706 0 9 0C4.02944 0 0 4.02944 0 9C0 13.9706 4.02944 18 9 18C13.9706 18 18 13.9706 18 9Z"
					fill="url(#paint0_linear_26119_59306)"
				/>
				<path
					d="M13.7502 5.84406C13.652 5.75804 13.5327 5.70272 13.406 5.6844C13.2793 5.66609 13.1503 5.68552 13.0336 5.74048L10.8624 6.75372L9.60092 4.36699C9.54062 4.25556 9.45327 4.1629 9.34774 4.09843C9.24222 4.03396 9.1223 4 9.0002 4C8.8781 4 8.75819 4.03396 8.65266 4.09843C8.54714 4.1629 8.45978 4.25556 8.39949 4.36699L7.13798 6.75372L4.96682 5.74048C4.8499 5.6856 4.72068 5.66614 4.59377 5.68431C4.46686 5.70247 4.34733 5.75753 4.24867 5.84326C4.15002 5.929 4.07619 6.04198 4.03552 6.16945C3.99486 6.29692 3.98898 6.43379 4.01855 6.56458L5.10842 11.4416C5.12926 11.536 5.16815 11.625 5.22273 11.7032C5.27731 11.7814 5.34644 11.8471 5.42594 11.8964C5.53358 11.964 5.65664 11.9998 5.78208 12C5.84306 11.9999 6 12 6 12C6 12 9.96618 12 12 12C12.1992 12 12.4081 11.9955 12.5702 11.8964C12.6502 11.8477 12.7197 11.7822 12.7743 11.7039C12.829 11.6256 12.8676 11.5363 12.8877 11.4416L13.9819 6.56458C14.0111 6.43375 14.0049 6.29695 13.964 6.16963C13.923 6.0423 13.849 5.92953 13.7502 5.84406Z"
					fill="white"
				/>
				<path
					d="M12.5 13H5.5C5.22386 13 5 13.2239 5 13.5C5 13.7761 5.22386 14 5.5 14H12.5C12.7761 14 13 13.7761 13 13.5C13 13.2239 12.7761 13 12.5 13Z"
					fill="white"
				/>
			</g>
			<defs>
				<linearGradient
					id="paint0_linear_26119_59306"
					x1="8.175"
					y1="1.18801e-05"
					x2="22.2753"
					y2="0.536807"
					gradientUnits="userSpaceOnUse"
				>
					<stop stopColor="#492CDD" />
					<stop offset="1" stopColor="#AD38E2" />
				</linearGradient>
				<clipPath id="clip0_26119_59306">
					<rect width="18" height="18" fill="white" />
				</clipPath>
			</defs>
		</svg>
	),
};

export default ICONS;
