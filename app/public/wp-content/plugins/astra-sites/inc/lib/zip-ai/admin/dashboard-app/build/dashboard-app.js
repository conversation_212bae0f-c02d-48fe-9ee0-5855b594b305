!function(){var e,t,n={679:function(e,t,n){"use strict";var r=n(864),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},l={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},i={};function s(e){return r.isMemo(e)?l:i[e.$$typeof]||o}i[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},i[r.Memo]=l;var c=Object.defineProperty,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,m=Object.prototype;e.exports=function e(t,n,r){if("string"!=typeof n){if(m){var o=p(n);o&&o!==m&&e(t,o,r)}var l=u(n);d&&(l=l.concat(d(n)));for(var i=s(t),C=s(n),h=0;h<l.length;++h){var v=l[h];if(!(a[v]||r&&r[v]||C&&C[v]||i&&i[v])){var g=f(n,v);try{c(t,v,g)}catch(e){}}}}return t}},826:function(e){e.exports=Array.isArray||function(e){return"[object Array]"==Object.prototype.toString.call(e)}},779:function(e,t,n){var r=n(826);e.exports=function e(t,n,o){return r(n)||(o=n||o,n=[]),o=o||{},t instanceof RegExp?function(e,t){var n=e.source.match(/\((?!\?)/g);if(n)for(var r=0;r<n.length;r++)t.push({name:r,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return u(e,t)}(t,n):r(t)?function(t,n,r){for(var o=[],a=0;a<t.length;a++)o.push(e(t[a],n,r).source);return u(new RegExp("(?:"+o.join("|")+")",d(r)),n)}(t,n,o):function(e,t,n){return f(a(e,n),t,n)}(t,n,o)},e.exports.parse=a,e.exports.compile=function(e,t){return i(a(e,t),t)},e.exports.tokensToFunction=i,e.exports.tokensToRegExp=f;var o=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function a(e,t){for(var n,r=[],a=0,l=0,i="",u=t&&t.delimiter||"/";null!=(n=o.exec(e));){var d=n[0],f=n[1],p=n.index;if(i+=e.slice(l,p),l=p+d.length,f)i+=f[1];else{var m=e[l],C=n[2],h=n[3],v=n[4],g=n[5],y=n[6],E=n[7];i&&(r.push(i),i="");var w=null!=C&&null!=m&&m!==C,b="+"===y||"*"===y,x="?"===y||"*"===y,L=n[2]||u,_=v||g;r.push({name:h||a++,prefix:C||"",delimiter:L,optional:x,repeat:b,partial:w,asterisk:!!E,pattern:_?c(_):E?".*":"[^"+s(L)+"]+?"})}}return l<e.length&&(i+=e.substr(l)),i&&r.push(i),r}function l(e){return encodeURI(e).replace(/[\/?#]/g,(function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()}))}function i(e,t){for(var n=new Array(e.length),o=0;o<e.length;o++)"object"==typeof e[o]&&(n[o]=new RegExp("^(?:"+e[o].pattern+")$",d(t)));return function(t,o){for(var a="",i=t||{},s=(o||{}).pretty?l:encodeURIComponent,c=0;c<e.length;c++){var u=e[c];if("string"!=typeof u){var d,f=i[u.name];if(null==f){if(u.optional){u.partial&&(a+=u.prefix);continue}throw new TypeError('Expected "'+u.name+'" to be defined')}if(r(f)){if(!u.repeat)throw new TypeError('Expected "'+u.name+'" to not repeat, but received `'+JSON.stringify(f)+"`");if(0===f.length){if(u.optional)continue;throw new TypeError('Expected "'+u.name+'" to not be empty')}for(var p=0;p<f.length;p++){if(d=s(f[p]),!n[c].test(d))throw new TypeError('Expected all "'+u.name+'" to match "'+u.pattern+'", but received `'+JSON.stringify(d)+"`");a+=(0===p?u.prefix:u.delimiter)+d}}else{if(d=u.asterisk?encodeURI(f).replace(/[?#]/g,(function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})):s(f),!n[c].test(d))throw new TypeError('Expected "'+u.name+'" to match "'+u.pattern+'", but received "'+d+'"');a+=u.prefix+d}}else a+=u}return a}}function s(e){return e.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function c(e){return e.replace(/([=!:$\/()])/g,"\\$1")}function u(e,t){return e.keys=t,e}function d(e){return e&&e.sensitive?"":"i"}function f(e,t,n){r(t)||(n=t||n,t=[]);for(var o=(n=n||{}).strict,a=!1!==n.end,l="",i=0;i<e.length;i++){var c=e[i];if("string"==typeof c)l+=s(c);else{var f=s(c.prefix),p="(?:"+c.pattern+")";t.push(c),c.repeat&&(p+="(?:"+f+p+")*"),l+=p=c.optional?c.partial?f+"("+p+")?":"(?:"+f+"("+p+"))?":f+"("+p+")"}}var m=s(n.delimiter||"/"),C=l.slice(-m.length)===m;return o||(l=(C?l.slice(0,-m.length):l)+"(?:"+m+"(?=$))?"),l+=a?"$":o&&C?"":"(?="+m+"|$)",u(new RegExp("^"+l,d(n)),t)}},703:function(e,t,n){"use strict";var r=n(414);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,a,l){if(l!==r){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return n.PropTypes=n,n}},697:function(e,t,n){e.exports=n(703)()},414:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},921:function(e,t){"use strict";var n="function"==typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,a=n?Symbol.for("react.fragment"):60107,l=n?Symbol.for("react.strict_mode"):60108,i=n?Symbol.for("react.profiler"):60114,s=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,m=n?Symbol.for("react.suspense_list"):60120,C=n?Symbol.for("react.memo"):60115,h=n?Symbol.for("react.lazy"):60116,v=n?Symbol.for("react.block"):60121,g=n?Symbol.for("react.fundamental"):60117,y=n?Symbol.for("react.responder"):60118,E=n?Symbol.for("react.scope"):60119;function w(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case u:case d:case a:case i:case l:case p:return e;default:switch(e=e&&e.$$typeof){case c:case f:case h:case C:case s:return e;default:return t}}case o:return t}}}function b(e){return w(e)===d}t.AsyncMode=u,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=s,t.Element=r,t.ForwardRef=f,t.Fragment=a,t.Lazy=h,t.Memo=C,t.Portal=o,t.Profiler=i,t.StrictMode=l,t.Suspense=p,t.isAsyncMode=function(e){return b(e)||w(e)===u},t.isConcurrentMode=b,t.isContextConsumer=function(e){return w(e)===c},t.isContextProvider=function(e){return w(e)===s},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return w(e)===f},t.isFragment=function(e){return w(e)===a},t.isLazy=function(e){return w(e)===h},t.isMemo=function(e){return w(e)===C},t.isPortal=function(e){return w(e)===o},t.isProfiler=function(e){return w(e)===i},t.isStrictMode=function(e){return w(e)===l},t.isSuspense=function(e){return w(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===a||e===d||e===i||e===l||e===p||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===C||e.$$typeof===s||e.$$typeof===c||e.$$typeof===f||e.$$typeof===g||e.$$typeof===y||e.$$typeof===E||e.$$typeof===v)},t.typeOf=w},864:function(e,t,n){"use strict";e.exports=n(921)}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var a=r[e]={exports:{}};return n[e](a,a.exports,o),a.exports}o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,{a:t}),t},t=Object.getPrototypeOf?function(e){return Object.getPrototypeOf(e)}:function(e){return e.__proto__},o.t=function(n,r){if(1&r&&(n=this(n)),8&r)return n;if("object"==typeof n&&n){if(4&r&&n.__esModule)return n;if(16&r&&"function"==typeof n.then)return n}var a=Object.create(null);o.r(a);var l={};e=e||[null,t({}),t([]),t(t)];for(var i=2&r&&n;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((function(e){l[e]=function(){return n[e]}}));return l.default=function(){return n},o.d(a,l),a},o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){"use strict";var e=window.wp.element,t=window.wp.i18n;const n=32,r=24,a=16,l=2,i="currentColor",s="none",c="#fff",u={stroke:i,strokeWidth:1.4,fill:s,strokeLinecap:"round",strokeLinejoin:"round"},d={fill:i,stroke:s},f=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,e.createElement)("svg",{width:r,height:r,viewBox:"0 0 29 28",fill:s,...t},(0,e.createElement)("g",{clipPath:"url(#clip_spec_ai_wordpress_logo)"},(0,e.createElement)("path",{d:"M28.2 14C28.2 6.272 21.9279 0 14.2 0C6.47195 0 0.199951 6.272 0.199951 14C0.199951 21.728 6.47195 28 14.2 28C21.9279 28 28.2 21.728 28.2 14ZM14.2 1.414C21.158 1.414 26.7859 7.042 26.7859 14C26.7859 20.958 21.158 26.586 14.2 26.586C7.24195 26.586 1.61395 20.958 1.61395 14C1.61395 7.042 7.24195 1.414 14.2 1.414ZM11.414 20.748L7.14395 9.254C7.82995 9.212 8.61395 9.142 8.61395 9.142C9.21595 9.072 9.14595 7.728 8.52995 7.756C8.52995 7.756 6.72395 7.896 5.54795 7.896C5.33795 7.896 5.08595 7.896 4.81995 7.882C6.83595 4.844 10.28 2.842 14.2 2.842C17.1259 2.842 19.786 3.948 21.774 5.768C20.934 5.656 19.744 6.258 19.744 7.756C19.744 8.68 20.276 9.464 20.85 10.388C21.284 11.144 21.55 12.096 21.55 13.482C21.55 15.358 19.7719 19.754 19.7719 19.754L15.978 9.254C16.65 9.212 17.028 9.03 17.028 9.03C17.63 8.96 17.56 7.49 16.958 7.518C16.958 7.518 15.138 7.672 13.962 7.672C12.87 7.672 11.008 7.518 11.008 7.518C10.406 7.49 10.336 9.002 10.938 9.03L12.114 9.142L13.682 13.398L11.414 20.748ZM19.842 23.758L23.496 14C23.496 14 24.434 11.634 24.042 8.666C24.924 10.262 25.358 12.054 25.358 14C25.358 18.144 23.174 21.812 19.842 23.758ZM3.95195 9.478L9.29995 24.15C5.56195 22.33 3.04195 18.438 3.04195 14C3.04195 12.376 3.32195 10.878 3.95195 9.478ZM14.382 15.82L17.588 24.57C16.538 24.948 15.39 25.158 14.2 25.158C13.192 25.158 12.226 25.004 11.316 24.738L14.382 15.82Z",...d,...n})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"clip_spec_ai_wordpress_logo"},(0,e.createElement)("rect",{width:28,height:28,fill:c,transform:"translate(0.199951)"}))))},p=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,e.createElement)("svg",{width:n,height:n,viewBox:"0 0 30 30",fill:s,...t},(0,e.createElement)("path",{d:"M5 0C2.23858 0 0 2.23858 0 5V25C0 27.7614 2.23858 30 5 30H25C27.7614 30 30 27.7614 30 25V5C30 2.23858 27.7614 0 25 0H5ZM26.1432 10.7265C26.1459 10.7262 26.1487 10.7259 26.1514 10.7256L26.1349 10.7269C26.1377 10.7267 26.1405 10.7266 26.1432 10.7265ZM26.1432 10.7265C21.9125 11.174 19.2481 11.1414 18.4509 11.1007C18.3687 11.1071 18.3417 10.9723 18.4048 10.9331C20.7044 9.21451 21.5524 7.98503 21.7865 7.59037C21.8319 7.53551 21.776 7.45429 21.7102 7.45939C20.6151 7.42458 13.7358 7.29097 9.63266 7.95161C6.00268 8.5242 3.84948 11.2753 3.86235 14.851C3.87523 18.4266 6.09129 21.3863 9.29387 22.1304C17.4168 23.9986 22.9334 18.0944 23.8439 17.0311C23.9057 16.975 23.8485 16.8768 23.7649 16.8661L19.5924 16.8476C19.4937 16.8553 19.4654 16.7035 19.5615 16.6618C24.5276 14.3256 25.9654 11.5273 26.2442 10.8553C26.2709 10.7871 26.2208 10.7249 26.1432 10.7265Z",...d,fillRule:"evenodd",clipRule:"evenodd",...r}))},m=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,e.createElement)("svg",{width:r,height:r,viewBox:"0 0 29 28",fill:s,...t},(0,e.createElement)("path",{d:"M22.9111 2.80005C24.2857 2.80005 25.4 3.8991 25.4 5.25484L25.4 9.4453C25.4 10.801 24.2857 11.9001 22.9111 11.9001H19.1778C17.8032 11.9001 16.6889 10.801 16.6889 9.4453L16.6889 5.25484C16.6889 3.8991 17.8032 2.80005 19.1778 2.80005L22.9111 2.80005Z",...u,strokeWidth:l,...n}),(0,e.createElement)("path",{d:"M5.48889 2.80005C4.11431 2.80005 3 3.8991 3 5.25484L3.00001 9.4453C3.00001 10.801 4.11432 11.9001 5.4889 11.9001H9.22223C10.5968 11.9001 11.7111 10.801 11.7111 9.4453L11.7111 5.25484C11.7111 3.8991 10.5968 2.80005 9.22222 2.80005L5.48889 2.80005Z",...u,strokeWidth:l,...n}),(0,e.createElement)("path",{d:"M22.9111 16.1001C24.2857 16.1001 25.4 17.1991 25.4 18.5549V22.7453C25.4 24.101 24.2857 25.2001 22.9111 25.2001H19.1778C17.8032 25.2001 16.6889 24.101 16.6889 22.7453L16.6889 18.5549C16.6889 17.1991 17.8032 16.1001 19.1778 16.1001H22.9111Z",...u,strokeWidth:l,...n}),(0,e.createElement)("path",{d:"M5.4889 16.1001C4.11432 16.1001 3.00001 17.1991 3.00001 18.5549L3.00001 22.7453C3.00001 24.101 4.11433 25.2001 5.4889 25.2001H9.22223C10.5968 25.2001 11.7111 24.101 11.7111 22.7453L11.7111 18.5549C11.7111 17.1991 10.5968 16.1001 9.22223 16.1001H5.4889Z",...u,strokeWidth:l,...n}))},C=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,e.createElement)("svg",{width:r,height:r,viewBox:"0 0 28 28",fill:s,...t},(0,e.createElement)("path",{d:"M11.1998 17.5L7.6998 14L11.1998 10.5M16.7998 10.5L20.2998 14L16.7998 17.5M5.5998 25.2C4.05341 25.2 2.7998 23.9464 2.7998 22.4V5.60005C2.7998 4.05365 4.05341 2.80005 5.5998 2.80005H22.3998C23.9462 2.80005 25.1998 4.05365 25.1998 5.60005V22.4C25.1998 23.9464 23.9462 25.2 22.3998 25.2H5.5998Z",...u,strokeWidth:l,...n}))},h=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,e.createElement)("svg",{width:r,height:r,viewBox:"0 0 29 28",fill:s,...t},(0,e.createElement)("path",{d:"M2.2002 4.36921C2.2002 3.06073 3.27471 2 4.6002 2H23.8002C25.1257 2 26.2002 3.06073 26.2002 4.36921V23.6308C26.2002 24.9393 25.1257 26 23.8002 26H4.6002C3.27471 26 2.2002 24.9393 2.2002 23.6308V4.36921Z",...u,strokeWidth:1.7,...n}),(0,e.createElement)("path",{d:"M5.02918 6.12988C5.02918 5.47564 5.56643 4.94528 6.22918 4.94528H22.1714C22.8341 4.94528 23.3714 5.47564 23.3714 6.12988V9.13253C23.3714 9.78677 22.8341 10.3171 22.1714 10.3171H6.22918C5.56643 10.3171 5.02918 9.78677 5.02918 9.13253V6.12988Z",...u,strokeWidth:1.7,...n}),(0,e.createElement)("path",{d:"M9.75183 12.9683C10.4146 12.9683 10.9518 13.4986 10.9518 14.1529L10.9518 22.0893C10.9518 22.7435 10.4146 23.2739 9.75183 23.2739H6.2291C5.56636 23.2739 5.0291 22.7435 5.0291 22.0893L5.0291 14.1529C5.0291 13.4986 5.56636 12.9683 6.2291 12.9683H9.75183Z",...u,strokeWidth:1.7,...n}),(0,e.createElement)("path",{d:"M22.1714 12.9683C22.8341 12.9683 23.3714 13.4986 23.3714 14.1529V22.0893C23.3714 22.7435 22.8341 23.2739 22.1714 23.2739H14.6509C13.9881 23.2739 13.4509 22.7435 13.4509 22.0893V14.1529C13.4509 13.4986 13.9881 12.9683 14.6509 12.9683H22.1714Z",...u,strokeWidth:1.7,...n}))},v=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,e.createElement)("svg",{width:r,height:r,viewBox:"0 0 29 28",fill:s,...t},(0,e.createElement)("path",{d:"M3.7 9.10005H24.7M21.2 14H7.2M15.6 19.6H7.2M7.2 25.2H21.2C23.5196 25.2 25.4 23.3196 25.4 21V7.00005C25.4 4.68045 23.5196 2.80005 21.2 2.80005H7.2C4.8804 2.80005 3 4.68045 3 7.00005V21C3 23.3196 4.8804 25.2 7.2 25.2Z",...u,strokeWidth:l,...n}))},g=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,e.createElement)("svg",{width:r,height:r,viewBox:"0 0 28 28",fill:s,...t},(0,e.createElement)("g",{clipPath:"url(#clip_spec_ai_wand)"},(0,e.createElement)("path",{d:"M15.4712 1.71924C15.4712 0.917724 14.8013 0.23584 13.9998 0.23584C13.1982 0.23584 12.5164 0.917724 12.5164 1.71924V5.36791C12.5164 6.16943 13.1982 6.83935 13.9998 6.83935C14.8013 6.83935 15.4712 6.16943 15.4712 5.36791V1.71924ZM19.1558 6.83935C18.6054 7.40161 18.6054 8.35865 19.1798 8.93286C19.73 9.48316 20.6989 9.48316 21.2612 8.90893L23.7614 6.40869C24.3237 5.8584 24.3118 4.88941 23.7376 4.31518C23.1873 3.75293 22.2183 3.77685 21.656 4.33911L19.1558 6.83935ZM6.72632 8.90893C7.28858 9.48316 8.25757 9.48316 8.80786 8.93286C9.38208 8.35865 9.38208 7.40161 8.8318 6.83935L6.34351 4.33911C5.78125 3.77685 4.81226 3.75293 4.26197 4.31518C3.68774 4.87744 3.68774 5.84643 4.23805 6.39672L6.72632 8.90893ZM23.283 26.1235C24.0007 26.8532 25.2209 26.8413 25.9267 26.1235C26.6327 25.3937 26.6327 24.2095 25.9267 23.4797L14.574 12.0432C13.8562 11.3254 12.636 11.3254 11.9302 12.0432C11.2124 12.773 11.2244 13.9573 11.9302 14.675L23.283 26.1235ZM1.63013 12.5935C0.828614 12.5935 0.158691 13.2634 0.158691 14.0649C0.158691 14.8665 0.828614 15.5364 1.63013 15.5364H5.27881C6.08032 15.5364 6.76221 14.8665 6.76221 14.0649C6.76221 13.2634 6.08032 12.5935 5.27881 12.5935H1.63013ZM26.3574 15.5364C27.1589 15.5364 27.8409 14.8665 27.8409 14.0649C27.8409 13.2634 27.1589 12.5935 26.3574 12.5935H22.7207C21.9192 12.5935 21.2372 13.2634 21.2372 14.0649C21.2372 14.8665 21.9192 15.5364 22.7207 15.5364H26.3574ZM16.8469 17.7375L13.1145 13.9932C12.8154 13.7061 12.7078 13.3352 13.0068 13.0481C13.27 12.7849 13.6528 12.8806 13.9519 13.1797L17.6604 16.9002L16.8469 17.7375ZM4.23805 21.6972C3.67579 22.2595 3.66382 23.2285 4.21411 23.7788C4.77636 24.353 5.74536 24.3651 6.30761 23.8147L8.80786 21.3145C9.37011 20.7522 9.38208 19.7951 8.8318 19.2328C8.26954 18.6706 7.30055 18.6587 6.73829 19.209L4.23805 21.6972ZM15.4712 22.774C15.4712 21.9725 14.8013 21.2905 13.9998 21.2905C13.1982 21.2905 12.5164 21.9725 12.5164 22.774V26.4107C12.5164 27.2121 13.1982 27.8941 13.9998 27.8941C14.8013 27.8941 15.4712 27.2121 15.4712 26.4107V22.774Z",...d,...n})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"clip_spec_ai_wand"},(0,e.createElement)("rect",{width:28,height:28,fill:c,...n}))))},y=(0,e.createElement)("svg",{width:145,height:32,viewBox:"0 0 145 32",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("g",{clipPath:"url(#clip_spec_ai_spectra_logo)"},(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.0347 32C24.8712 32 32.0347 24.8366 32.0347 16C32.0347 7.16343 24.8712 0 16.0347 0C7.1981 0 0.034668 7.16343 0.034668 16C0.034668 24.8366 7.1981 32 16.0347 32ZM20.9549 20.688C21.88 20.1032 22.4346 19.1247 22.4347 18.0775C22.4347 16.6224 21.3723 15.357 19.8655 15.0174L15.3508 13.8968C14.9252 13.8009 14.8232 13.2761 15.1853 13.0452L18.7026 10.8017C20.3398 9.75739 20.7655 7.66455 19.6534 6.12718C19.5144 5.93499 19.2358 5.88503 19.0311 6.01559L11.0854 11.3753C10.1779 11.9541 9.63467 12.9173 9.63467 13.9473C9.63467 15.3851 10.6843 16.6352 12.1731 16.9708L16.7614 18.108C17.1879 18.2042 17.2891 18.7307 16.9252 18.9607L13.382 21.2006C11.7402 22.2385 11.3053 24.3297 12.4106 25.8714C12.5488 26.0641 12.8271 26.1152 13.0324 25.9854L20.9549 20.688Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M46.1596 23.755C44.6665 23.755 43.3371 23.5107 42.1714 23.022C41.0057 22.5333 40.0649 21.8411 39.3491 20.9452L41.9873 18.7155C42.6009 19.3875 43.2962 19.8762 44.0734 20.1816C44.8506 20.4667 45.6379 20.6092 46.4357 20.6092C47.0693 20.6092 47.5809 20.4667 47.9695 20.1816C48.358 19.8965 48.5523 19.4995 48.5523 18.9904C48.5523 18.5425 48.3681 18.1862 48.0001 17.9214C47.6321 17.6771 46.7526 17.3717 45.3618 17.0052C43.2962 16.4758 41.834 15.7529 40.975 14.8367C40.2183 14.0222 39.84 13.0143 39.84 11.813C39.84 10.856 40.1263 10.0212 40.6989 9.30854C41.2715 8.57551 42.0283 8.00536 42.969 7.59814C43.9098 7.19092 44.9323 6.9873 46.0367 6.9873C47.3048 6.9873 48.5011 7.2113 49.6262 7.65926C50.7507 8.10717 51.6915 8.71805 52.4481 9.49176L50.1779 11.9657C49.6056 11.4363 48.9409 10.9985 48.1843 10.6524C47.4479 10.2859 46.7626 10.1026 46.1286 10.1026C44.6563 10.1026 43.92 10.6117 43.92 11.6297C43.9404 12.1184 44.1654 12.5053 44.5949 12.7903C45.0039 13.0754 45.9343 13.4114 47.3866 13.7982C49.3295 14.3073 50.6995 14.9792 51.4972 15.814C52.1925 16.5267 52.5404 17.4735 52.5404 18.6545C52.5404 19.6318 52.2538 20.5074 51.6815 21.2811C51.1292 22.0548 50.3722 22.6657 49.4113 23.1137C48.4499 23.5412 47.366 23.755 46.1596 23.755Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M54.6069 28.8V7.32324H58.1987L58.7176 9.27794C59.2497 8.62637 59.9756 8.0868 60.8958 7.65924C61.8161 7.23163 62.7875 7.01782 63.8101 7.01782C65.2213 7.01782 66.4789 7.37417 67.5834 8.0868C68.6878 8.79944 69.5569 9.78701 70.1909 11.0494C70.825 12.3118 71.1418 13.7473 71.1418 15.3559C71.1418 16.9644 70.825 18.3999 70.1909 19.6623C69.5569 20.9247 68.6878 21.9225 67.5834 22.6555C66.4789 23.3681 65.2213 23.7245 63.8101 23.7245C62.8488 23.7245 61.9185 23.531 61.0184 23.1442C60.1187 22.737 59.3928 22.2279 58.8405 21.617V26.9766L54.6069 28.8ZM62.8899 20.1205C64.1374 20.1205 65.1496 19.6827 65.9267 18.8072C66.7039 17.9113 67.0924 16.7608 67.0924 15.3559C67.0924 13.9713 66.7039 12.8412 65.9267 11.9657C65.1496 11.0698 64.1374 10.6218 62.8899 10.6218C61.6424 10.6218 60.6298 11.0596 59.8527 11.9351C59.0755 12.8107 58.6869 13.9509 58.6869 15.3559C58.6869 16.7608 59.0755 17.9113 59.8527 18.8072C60.6298 19.6827 61.6424 20.1205 62.8899 20.1205Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M81.5247 23.7551C79.7862 23.7551 78.263 23.3987 76.9538 22.6861C75.645 21.9734 74.6223 20.9961 73.8863 19.754C73.1704 18.5119 72.8125 17.0867 72.8125 15.4781C72.8125 13.8084 73.1604 12.3424 73.8557 11.08C74.5711 9.7972 75.5426 8.78934 76.77 8.05631C78.0175 7.32328 79.4488 6.95679 81.0648 6.95679C82.5167 6.95679 83.8155 7.31313 84.9606 8.02577C86.1263 8.71807 87.0264 9.66486 87.66 10.8662C88.3147 12.0675 88.6214 13.442 88.5807 14.9894L88.5496 16.3027H76.8925C77.1174 17.4633 77.6696 18.3796 78.5492 19.0516C79.4488 19.7235 80.5839 20.0594 81.9544 20.0594C82.6904 20.0594 83.3551 19.9475 83.9485 19.7235C84.562 19.4995 85.2162 19.1024 85.9115 18.5323L87.9362 21.3422C87.0571 22.116 86.0445 22.7166 84.8994 23.1442C83.7743 23.5514 82.6498 23.7551 81.5247 23.7551ZM81.0955 10.5608C78.7842 10.5608 77.3935 11.5992 76.9231 13.6761H84.5926V13.5539C84.5314 12.6784 84.1629 11.9657 83.4882 11.4159C82.8335 10.8458 82.0363 10.5608 81.0955 10.5608Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M98.2424 23.755C96.7083 23.755 95.3281 23.3885 94.1007 22.6555C92.8943 21.9225 91.9329 20.9248 91.2171 19.6624C90.5217 18.4 90.1743 16.9644 90.1743 15.3559C90.1743 13.7677 90.5217 12.3424 91.2171 11.08C91.9329 9.81757 92.8943 8.81985 94.1007 8.08682C95.3281 7.3538 96.7083 6.9873 98.2424 6.9873C99.6943 6.9873 101.014 7.25199 102.199 7.78141C103.406 8.31082 104.347 9.054 105.022 10.011L102.721 12.7598C102.292 12.1693 101.698 11.6705 100.942 11.2632C100.185 10.8356 99.4182 10.6218 98.6411 10.6218C97.7821 10.6218 97.0155 10.8356 96.3403 11.2632C95.6861 11.6705 95.1645 12.2304 94.7759 12.9431C94.4079 13.6557 94.2237 14.46 94.2237 15.3559C94.2237 16.2518 94.418 17.0561 94.8066 17.7687C95.1951 18.4814 95.7268 19.0515 96.4015 19.4791C97.0767 19.8863 97.8333 20.0899 98.6717 20.0899C99.4694 20.0899 100.206 19.9169 100.881 19.5707C101.555 19.2042 102.169 18.6952 102.721 18.0436L105.022 20.7924C104.327 21.6883 103.365 22.4112 102.138 22.9609C100.911 23.4903 99.6125 23.755 98.2424 23.755Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M110.652 23.4495C108.632 23.4495 106.995 21.8122 106.995 19.7924V11.0799V7.32315V5.02336L111.167 3.19995V7.32315H114.32V11.0799H111.167V18.7428C111.167 19.2478 111.576 19.6571 112.081 19.6571H114.32V23.4495H110.652Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M116.769 23.4496V7.32326H120.36L120.879 9.98045C121.411 9.10493 122.107 8.39224 122.966 7.84248C123.845 7.27238 124.765 6.9873 125.726 6.9873C126.094 6.9873 126.432 7.01784 126.739 7.07892C127.066 7.11965 127.342 7.18072 127.567 7.26218L126.432 11.8435C126.207 11.7213 125.921 11.6195 125.573 11.5381C125.225 11.4567 124.878 11.4159 124.53 11.4159C123.528 11.4159 122.679 11.7621 121.984 12.4544C121.309 13.1263 120.972 13.9815 120.972 15.0199V23.4496H116.769Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M135.074 23.755C133.724 23.755 132.507 23.3885 131.424 22.6555C130.34 21.9225 129.47 20.9248 128.816 19.6624C128.182 18.4 127.865 16.9644 127.865 15.3559C127.865 13.7066 128.182 12.261 128.816 11.0189C129.47 9.75649 130.35 8.76893 131.454 8.05629C132.579 7.34365 133.847 6.9873 135.258 6.9873C136.403 6.9873 137.405 7.22145 138.264 7.6898C139.123 8.13775 139.809 8.7282 140.32 9.46122V7.32326H144.492V23.4496H140.747L140.289 21.2811C139.717 21.9938 138.97 22.5842 138.05 23.0525C137.15 23.5209 136.158 23.755 135.074 23.755ZM136.209 20.1205C137.457 20.1205 138.469 19.6827 139.246 18.8072C140.023 17.9316 140.412 16.7812 140.412 15.3559C140.412 13.9306 140.023 12.7802 139.246 11.9046C138.469 11.0291 137.457 10.5913 136.209 10.5913C134.982 10.5913 133.98 11.0291 133.203 11.9046C132.446 12.7802 132.068 13.9306 132.068 15.3559C132.068 16.7812 132.446 17.9316 133.203 18.8072C133.98 19.6827 134.982 20.1205 136.209 20.1205Z",fill:"currentColor"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"clip_spec_ai_spectra_logo"},(0,e.createElement)("rect",{width:144.457,height:32,fill:"white",transform:"translate(0.034668)"})))),E=(0,e.createElement)("svg",{width:124,height:36,viewBox:"0 0 124 36",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("g",{clipPath:"url(#clip_spec_ai_astra_logo)"},(0,e.createElement)("mask",{id:"mask_spec_ai_astra_logo",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:0,y:0,width:123,height:36},(0,e.createElement)("path",{d:"M122.605 0H0.277344V35.2471H122.605V0Z",fill:"white"})),(0,e.createElement)("g",{mask:"url(#mask_spec_ai_astra_logo)"},(0,e.createElement)("path",{d:"M51.2049 23.3476H43.5876L41.8462 26.7455H37.7261L47.1131 8.40283H48.1891L56.8965 26.7455H52.8189L51.2049 23.3476ZM49.4775 19.6819L47.552 15.5932L45.4707 19.6819H49.4775Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M63.5369 18.9769C62.928 18.8218 61.7953 18.5256 60.7476 17.8207C59.2751 16.8479 58.4539 15.4381 58.3832 13.6898C58.2982 12.2235 58.7797 10.9405 59.7283 9.95356C61.1016 8.54369 63.0696 8.14893 64.0182 8.14893C66.6942 8.14893 68.1949 9.12174 69.54 9.98179L70.1487 10.3766L68.2232 13.4924L67.572 13.0695C66.3827 12.3081 65.6323 11.8147 64.0182 11.8147C63.7775 11.8147 62.8572 11.9979 62.39 12.4914C62.2342 12.6465 62.0502 12.9425 62.0785 13.5347C62.1068 14.4793 62.7581 15.0009 64.4429 15.4239C64.6553 15.4803 64.8111 15.5227 64.9668 15.579C66.7225 16.1853 68.0958 16.9889 69.0444 18.0181C69.993 19.0615 70.4886 20.3021 70.4886 21.5992C70.4886 22.8822 69.9364 24.2216 69.0161 25.2367C67.94 26.4351 66.4393 27.1259 64.7828 27.1259C63.9616 27.1259 62.8572 27.0696 61.5547 26.6748C59.8981 26.1532 58.4256 25.2367 57.1655 23.8973L59.8415 21.3877C61.7812 23.4603 63.9616 23.4603 64.7828 23.4603C66.3119 23.4603 66.8074 22.0785 66.8074 21.5992C66.8074 20.5982 65.6747 19.6818 63.7634 19.0332L63.6784 19.005L63.5369 18.9769Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M86.3605 8.40277V12.0685H81.2635V26.7595H77.5823V12.0544H72.457V8.38867H86.3605V8.40277Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M98.0829 26.7453L94.6428 19.3857H92.8023V26.7171H89.1211V8.37451H96.0584C99.1023 8.37451 101.581 10.8559 101.581 13.9012C101.581 16.1289 100.264 18.0322 98.352 18.9064L102.005 26.7594H98.0829V26.7453ZM92.8023 12.0402V15.7341H96.0584C97.0778 15.7341 97.8991 14.9163 97.8991 13.9012C97.8991 12.8861 97.0778 12.0402 96.0584 12.0402H92.8023Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M116.928 23.3476H109.311L107.569 26.7455H103.449L112.836 8.40283H113.912L122.605 26.7455H118.528L116.928 23.3476ZM115.187 19.6819L113.261 15.5932L111.179 19.6819H115.187Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M17.8337 35.1063C27.5298 35.1063 35.39 27.279 35.39 17.6237C35.39 7.96833 27.5298 0.141113 17.8337 0.141113C8.1376 0.141113 0.277344 7.96833 0.277344 17.6237C0.277344 27.279 8.1376 35.1063 17.8337 35.1063ZM17.6213 7.89556C16.1348 11.0183 14.6483 14.1445 13.1617 17.2706C11.675 20.3973 10.1882 23.5239 8.70154 26.647H12.5243C13.7278 24.229 14.9312 21.8076 16.1347 19.3861C17.3381 16.9646 18.5416 14.5432 19.7451 12.1252L17.6213 7.89556ZM19.745 19.4564C20.3573 18.1876 20.9696 16.9188 21.5855 15.65C22.4844 17.4826 23.3799 19.3154 24.2753 21.1481C25.171 22.9811 26.0665 24.814 26.9657 26.647H22.8597C22.6261 26.1043 22.3889 25.5649 22.1518 25.0257C21.9147 24.4864 21.6774 23.9471 21.4438 23.4043H17.9043H17.8335L17.9043 23.2633C18.5202 21.9943 19.1326 20.7254 19.745 19.4564Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}))),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"clip_spec_ai_astra_logo"},(0,e.createElement)("rect",{width:123.158,height:36,fill:"white",transform:"translate(0.277344)"})))),w=(0,e.createElement)("svg",{width:171,height:32,viewBox:"0 0 171 32",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("g",{clipPath:"url(#clip_spec_ai_cartflows_logo)"},(0,e.createElement)("mask",{id:"mask_spec_ai_cartflows_logo",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:0,y:0,width:171,height:32},(0,e.createElement)("path",{d:"M170.202 0H0.647949V31.3308H170.202V0Z",fill:"white"})),(0,e.createElement)("g",{mask:"url(#mask_spec_ai_cartflows_logo)"},(0,e.createElement)("path",{d:"M19.8602 2.03637C18.7416 3.13295 18.0352 4.64074 18.0352 6.34436V16.331C18.0352 17.7997 16.8381 18.9942 15.3662 18.9942H8.6547C7.18288 18.9942 5.98578 17.7997 5.98578 16.331C5.98578 14.8624 7.18288 13.668 8.6547 13.668H16.0139V8.34169H8.6547C8.20334 8.34169 7.75198 8.38086 7.32024 8.43957C3.53274 9.0858 0.647949 12.3756 0.647949 16.331C0.647949 19.8166 2.88513 22.7735 5.98578 23.87C6.41752 24.0267 6.86888 24.1442 7.32024 24.2225C7.75198 24.3008 8.20334 24.34 8.6547 24.34H15.3662C15.8176 24.34 16.269 24.3008 16.7007 24.2225C17.1521 24.1442 17.6034 24.0267 18.0352 23.87C21.1554 22.7735 23.373 19.8166 23.373 16.331V13.668C26.3167 13.668 28.7108 11.279 28.7108 8.34169H23.373V6.34436C23.373 5.9723 23.6674 5.67858 24.0402 5.67858H25.3747C27.0035 5.67858 28.4754 5.03238 29.5743 3.97497C30.5359 3.03504 31.2031 1.76223 31.3602 0.332764H24.0402C22.4311 0.352345 20.9396 0.998546 19.8602 2.03637Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M8.65476 25.6521C7.18293 25.6521 5.98584 26.8466 5.98584 28.3153C5.98584 29.7839 7.18293 30.9783 8.65476 30.9783C10.1266 30.9783 11.3237 29.7839 11.3237 28.3153C11.3237 26.8466 10.1266 25.6521 8.65476 25.6521Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M16.6616 25.6521C15.1898 25.6521 13.9927 26.8466 13.9927 28.3153C13.9927 29.7839 15.1898 30.9783 16.6616 30.9783C18.1335 30.9783 19.3305 29.7839 19.3305 28.3153C19.3305 26.8466 18.1531 25.6521 16.6616 25.6521Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M45.9215 27.2382C44.3908 27.2382 43.0171 27.0032 41.82 26.5333C40.6033 26.0633 39.5828 25.378 38.739 24.4772C37.8951 23.5568 37.2476 22.4603 36.7962 21.1483C36.3448 19.8363 36.1289 18.3481 36.1289 16.6445C36.1289 14.9605 36.3841 13.4527 36.9139 12.1407C37.4241 10.8288 38.1306 9.73217 39.0333 8.81183C39.9361 7.91104 40.9958 7.2061 42.1929 6.75572C43.4096 6.28576 44.7048 6.05078 46.0982 6.05078C46.942 6.05078 47.7073 6.10953 48.3942 6.2466C49.0811 6.38367 49.6698 6.52074 50.18 6.67739C50.6903 6.83405 51.1024 6.99071 51.4359 7.16694C51.7696 7.34318 52.0051 7.46067 52.1425 7.53899L51.0435 10.5742C50.5725 10.2805 49.9052 10.0063 49.0418 9.73217C48.1783 9.4776 47.2364 9.34051 46.2159 9.34051C45.3328 9.34051 44.5085 9.49714 43.7432 9.79087C42.9778 10.1042 42.3303 10.5546 41.7612 11.1616C41.2117 11.7686 40.7603 12.5323 40.4659 13.4331C40.152 14.3535 39.9949 15.4108 39.9949 16.6249C39.9949 17.7019 40.1127 18.681 40.3482 19.5818C40.5836 20.4825 40.9761 21.2658 41.4864 21.912C41.9966 22.5582 42.6443 23.0673 43.4096 23.4394C44.1946 23.8114 45.1366 23.9877 46.2159 23.9877C47.5308 23.9877 48.5905 23.8506 49.4146 23.596C50.2389 23.3414 50.8668 23.0869 51.3182 22.8519L52.3191 25.8675C52.0836 26.0241 51.7696 26.2004 51.3575 26.3375C50.9649 26.4941 50.494 26.6507 49.9445 26.7878C49.395 26.9249 48.7867 27.0424 48.0999 27.1403C47.4326 27.199 46.7065 27.2382 45.9215 27.2382Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M60.5224 11.1226C61.6998 11.1226 62.681 11.2597 63.4857 11.5729C64.2903 11.8666 64.9379 12.2779 65.4089 12.8066C65.8798 13.3353 66.2331 13.9815 66.4293 14.7256C66.6452 15.4697 66.7433 16.2922 66.7433 17.1929V26.4746C66.1938 26.5921 65.3696 26.7292 64.2707 26.9055C63.1716 27.0621 61.9157 27.16 60.542 27.16C59.6196 27.16 58.7758 27.0621 58.0105 26.8858C57.2451 26.7096 56.5975 26.4355 56.0676 26.0438C55.5182 25.6522 55.106 25.1431 54.8116 24.536C54.5173 23.9094 54.3604 23.1457 54.3604 22.2254C54.3604 21.3442 54.5369 20.6001 54.8706 19.9931C55.2238 19.3861 55.6751 18.8965 56.2638 18.5245C56.8526 18.1328 57.5198 17.8783 58.3048 17.702C59.0702 17.5258 59.8748 17.4474 60.7186 17.4474C61.1111 17.4474 61.5232 17.4671 61.955 17.5258C62.3867 17.5845 62.8381 17.6629 63.3287 17.7803V17.1929C63.3287 16.7817 63.2894 16.3901 63.1913 16.018C63.0931 15.6459 62.9166 15.3327 62.681 15.0389C62.4456 14.7647 62.1119 14.5298 61.7194 14.3732C61.327 14.2165 60.8168 14.1381 60.2083 14.1381C59.3842 14.1381 58.6384 14.1969 57.9516 14.3144C57.2647 14.4319 56.7152 14.569 56.2835 14.7256L55.8518 11.8666C56.3031 11.71 56.9508 11.5534 57.8142 11.3967C58.6188 11.2009 59.5412 11.1226 60.5224 11.1226ZM60.8168 24.3206C61.9157 24.3206 62.7399 24.2619 63.3091 24.1445V20.2085C63.1128 20.1498 62.8381 20.091 62.4652 20.0323C62.0923 19.9735 61.6802 19.9343 61.2288 19.9343C60.8364 19.9343 60.4439 19.9735 60.0514 20.0323C59.6393 20.091 59.2861 20.2085 58.9721 20.3652C58.658 20.5218 58.3833 20.7372 58.187 21.0309C57.9908 21.305 57.8927 21.6771 57.8927 22.0884C57.8927 22.9304 58.1478 23.5178 58.6777 23.8311C59.2075 24.164 59.914 24.3206 60.8168 24.3206Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M79.8126 14.6862C79.5182 14.5884 79.106 14.4905 78.5958 14.373C78.0856 14.2555 77.4772 14.2164 76.7904 14.2164C76.3979 14.2164 75.9858 14.2555 75.5344 14.3338C75.1027 14.4121 74.7887 14.4708 74.6121 14.5296V26.7878H71.0601V12.2386C71.747 11.984 72.6104 11.749 73.6308 11.514C74.6513 11.2986 75.7896 11.1812 77.0455 11.1812C77.281 11.1812 77.5557 11.2007 77.8697 11.2203C78.1838 11.2398 78.4977 11.2986 78.8117 11.3378C79.1257 11.3966 79.4201 11.4553 79.7145 11.514C80.0088 11.5728 80.2443 11.6511 80.4209 11.7098L79.8126 14.6862Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M82.7168 7.46074L86.2688 6.87329V11.4946H91.7248V14.4514H86.2688V20.6784C86.2688 21.9121 86.465 22.7737 86.8574 23.3023C87.2498 23.8311 87.9176 24.0857 88.8397 24.0857C89.4873 24.0857 90.0566 24.0073 90.5669 23.8898C91.057 23.7528 91.4696 23.6352 91.7635 23.4982L92.3522 26.2984C91.9404 26.4746 91.4107 26.6509 90.7429 26.8467C90.076 27.0229 89.2911 27.1208 88.3884 27.1208C87.2894 27.1208 86.3867 26.9641 85.6406 26.69C84.9147 26.3963 84.326 25.9655 83.8942 25.4172C83.4625 24.8689 83.1682 24.1835 82.9915 23.4003C82.8149 22.617 82.7364 21.7162 82.7364 20.698L82.7168 7.46074Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M95.335 26.8075V6.52075H108.189V9.65386H99.0444V14.8234H107.169V17.9565H99.0444V26.8075H95.335Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M116.648 27.101C115.608 27.0815 114.726 26.964 114.019 26.7486C113.332 26.5332 112.782 26.2198 112.37 25.8283C111.958 25.4171 111.664 24.9275 111.507 24.34C111.33 23.733 111.252 23.0673 111.252 22.3036V4.67998L114.804 4.09253V21.6182C114.804 22.049 114.823 22.4015 114.902 22.6952C114.981 22.9889 115.098 23.2434 115.275 23.4393C115.452 23.6351 115.686 23.7917 115.981 23.9093C116.295 24.0071 116.668 24.1051 117.139 24.1638L116.648 27.101Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M133.898 19.1315C133.898 20.3455 133.721 21.4421 133.368 22.4408C133.034 23.4394 132.524 24.2815 131.876 24.9865C131.228 25.6718 130.463 26.2201 129.561 26.6313C128.638 27.0229 127.637 27.2187 126.558 27.2187C125.459 27.2187 124.458 27.0229 123.556 26.6313C122.653 26.2397 121.887 25.6914 121.24 24.9865C120.592 24.2815 120.101 23.4394 119.728 22.4408C119.356 21.4421 119.179 20.3455 119.179 19.1315C119.179 17.9174 119.356 16.8208 119.728 15.8418C120.082 14.8627 120.592 14.0207 121.259 13.3157C121.907 12.6108 122.692 12.0821 123.594 11.6905C124.497 11.3183 125.478 11.1226 126.558 11.1226C127.637 11.1226 128.618 11.3183 129.54 11.6905C130.443 12.0821 131.228 12.6108 131.876 13.3157C132.524 14.0207 133.014 14.8627 133.387 15.8418C133.721 16.8208 133.898 17.9174 133.898 19.1315ZM130.267 19.1315C130.267 17.6042 129.933 16.4096 129.285 15.5088C128.638 14.6081 127.716 14.1773 126.538 14.1773C125.361 14.1773 124.438 14.6081 123.791 15.5088C123.143 16.39 122.81 17.6042 122.81 19.1315C122.81 20.6784 123.143 21.8925 123.791 22.7933C124.438 23.694 125.361 24.1445 126.538 24.1445C127.716 24.1445 128.618 23.694 129.285 22.7933C129.933 21.8925 130.267 20.6784 130.267 19.1315Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M146.399 17.1731C145.947 18.8767 145.456 20.5607 144.946 22.1861C144.436 23.8114 143.925 25.3583 143.415 26.8073H140.491C140.099 25.9066 139.687 24.8883 139.255 23.733C138.823 22.5777 138.391 21.3636 137.96 20.0516C137.528 18.7397 137.096 17.3689 136.665 15.9199C136.233 14.4708 135.821 13.0022 135.408 11.4944H139.156C139.333 12.3169 139.549 13.1981 139.785 14.1379C140.02 15.0974 140.255 16.057 140.511 17.036C140.766 18.0151 141.041 18.9746 141.295 19.9146C141.571 20.8545 141.845 21.7161 142.12 22.4994C142.415 21.5986 142.708 20.6782 142.984 19.6992C143.258 18.7592 143.533 17.7997 143.788 16.8402C144.043 15.8807 144.279 14.9408 144.514 14.0401C144.75 13.1393 144.946 12.2777 145.123 11.4749H147.85C148.027 12.2777 148.223 13.1197 148.439 14.0401C148.655 14.9408 148.89 15.8807 149.126 16.8402C149.381 17.7997 149.636 18.7592 149.911 19.6992C150.205 20.6587 150.48 21.579 150.774 22.4994C151.029 21.7161 151.304 20.8545 151.579 19.9146C151.854 18.9746 152.128 18.0151 152.383 17.036C152.658 16.057 152.894 15.0779 153.149 14.1379C153.385 13.1981 153.581 12.3169 153.777 11.4944H157.447C157.034 13.0022 156.603 14.4708 156.191 15.9199C155.759 17.3689 155.327 18.7397 154.895 20.0516C154.463 21.3636 154.032 22.5777 153.6 23.733C153.168 24.8883 152.756 25.9066 152.345 26.8073H149.46C148.949 25.3583 148.439 23.8309 147.89 22.1861C147.34 20.5607 146.849 18.8963 146.399 17.1731Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M163.963 24.2617C164.904 24.2617 165.591 24.1442 166.003 23.9288C166.435 23.6939 166.651 23.3218 166.651 22.7735C166.651 22.2644 166.435 21.8532 165.964 21.5203C165.493 21.1873 164.747 20.8153 163.687 20.4237C163.04 20.1887 162.451 19.9341 161.921 19.6795C161.372 19.4054 160.921 19.1117 160.528 18.7592C160.135 18.4068 159.841 17.9956 159.606 17.4864C159.37 16.9968 159.272 16.3899 159.272 15.6458C159.272 14.2358 159.782 13.1392 160.822 12.3168C161.843 11.514 163.256 11.1028 165.041 11.1028C165.944 11.1028 166.808 11.1811 167.632 11.3574C168.456 11.5336 169.065 11.6902 169.477 11.8469L168.829 14.7058C168.437 14.5296 167.946 14.3729 167.338 14.2163C166.729 14.0792 166.023 14.0009 165.238 14.0009C164.512 14.0009 163.923 14.1184 163.472 14.3729C163.02 14.6079 162.805 14.9799 162.805 15.5087C162.805 15.7632 162.843 15.9787 162.942 16.1744C163.02 16.3702 163.177 16.5465 163.393 16.7227C163.609 16.899 163.883 17.0556 164.237 17.2123C164.59 17.3885 165.022 17.5451 165.532 17.7214C166.376 18.0347 167.083 18.348 167.671 18.6417C168.26 18.9355 168.751 19.288 169.124 19.66C169.496 20.032 169.791 20.4824 169.968 20.972C170.144 21.4615 170.223 22.0294 170.223 22.7147C170.223 24.1834 169.673 25.2995 168.593 26.0437C167.494 26.8074 165.964 27.1794 163.942 27.1794C162.588 27.1794 161.51 27.0619 160.685 26.8465C159.861 26.6311 159.291 26.4549 158.958 26.3178L159.567 23.361C160.097 23.5764 160.724 23.7917 161.47 23.9876C162.177 24.1638 163.02 24.2617 163.963 24.2617Z",fill:"currentColor"}))),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"clip_spec_ai_cartflows_logo"},(0,e.createElement)("rect",{width:170.105,height:32,fill:"white",transform:"translate(0.435547)"})))),b=(0,e.createElement)("svg",{width:99,height:36,viewBox:"0 0 99 36",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("g",{clipPath:"url(#clip_spec_ai_starter_templates_logo)"},(0,e.createElement)("g",{opacity:.5},(0,e.createElement)("path",{d:"M24.5032 2.20013L22.0791 2.95518L24.2648 5.29978L27.9208 6.37276L26.9273 3.39231C26.6094 2.43857 25.5761 1.84247 24.5032 2.20013Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M2.52714 11.4197L3.36166 13.8438L5.70626 11.6582L6.77923 7.9624L3.7988 8.95587C2.76557 9.31352 2.16948 10.4262 2.52714 11.4197Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M8.3291 29.2627L9.32257 32.2431C9.60075 33.3161 10.7532 33.8327 11.7864 33.5546L14.3297 32.72L12.2235 30.4151L8.3291 29.2627Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M33.7224 24.1759L32.8879 21.6326L30.6624 23.7388L29.4702 27.6332L32.4508 26.6398C33.484 26.3616 34.1595 25.2489 33.7224 24.1759Z",fill:"currentColor"})),(0,e.createElement)("g",{opacity:.8},(0,e.createElement)("path",{d:"M20.1315 0.92843C19.3765 0.0939033 18.1843 0.0541644 17.3498 0.809211L15.2437 2.75643L19.2573 3.94861L24.2247 5.37925L22.0391 3.03461L20.1315 0.92843Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M1.33492 15.7114C0.500398 16.4664 0.460658 17.6586 1.21571 18.4931L3.04371 20.4801L4.15641 16.5856L5.62676 11.658L3.28214 13.8436L1.33492 15.7114Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M35.0341 17.0229L33.2061 15.0757L32.0139 19.0099L30.623 23.6991L32.8484 21.5929L34.8354 19.7252C35.7096 19.0894 35.7891 17.8574 35.0341 17.0229Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M12.144 30.3752L14.2502 32.6802L16.0783 34.6274C16.8333 35.4619 18.0652 35.5016 18.8599 34.7466L20.8469 32.9186L17.0319 31.8059L12.144 30.3752Z",fill:"currentColor"})),(0,e.createElement)("path",{d:"M33.1264 7.96245L27.8808 6.45234L24.2248 5.37941L19.3368 3.90903L15.3232 2.71686L10.8724 1.4452C9.75963 1.16703 8.5675 1.72338 8.28932 2.87581L6.73948 8.08164L5.70626 11.7774L4.23589 16.7051L3.12319 20.5995L1.73231 25.2491C1.45413 26.3617 2.01049 27.5539 3.16292 27.8321L8.40852 29.3422L12.2235 30.4549L17.0319 31.8855L20.8469 32.9982L25.4169 34.3493C26.5296 34.6275 27.7218 34.0712 28 32.9188L29.5101 27.7526L30.7023 23.8582L32.1328 19.1689L33.325 15.2348L34.6762 10.7045C34.8351 9.43276 34.239 8.24063 33.1264 7.96245ZM24.3837 9.47255V13.6054H18.8997H11.5479C11.5876 13.3272 11.7068 12.9696 11.8261 12.6517C12.0645 12.0556 12.4222 11.4595 12.9388 11.0224C13.4157 10.6249 14.0118 10.1481 14.568 9.90966C15.1642 9.63148 15.8795 9.51228 16.5551 9.51228H24.3837V9.47255ZM12.2235 26.322V22.1891H19.8137C19.9329 22.1891 20.0919 22.1891 20.2111 22.1494C20.3303 22.1097 20.4495 22.0301 20.5687 21.9109C20.6879 21.7917 20.7277 21.6725 20.8072 21.5533C20.8469 21.434 20.8469 21.3148 20.8469 21.1559C20.8469 21.0367 20.8469 20.8777 20.8072 20.7585C20.7674 20.6393 20.6879 20.52 20.5687 20.4008C20.4495 20.2817 20.3303 20.2419 20.2111 20.1624C20.0919 20.1227 19.9726 20.1227 19.8137 20.1227H16.6345C15.9987 20.1227 15.2039 20.0035 14.6476 19.7253C14.0515 19.4868 13.4554 19.1292 13.0183 18.6126C12.6208 18.2152 12.144 17.5396 11.9056 16.9833C11.7466 16.6256 11.6672 16.3474 11.6274 15.9898H16.6742H18.9792H19.8534C20.4893 15.9898 21.2841 16.1487 21.8404 16.3872C22.4365 16.6256 23.0326 16.9833 23.4697 17.4998C23.8671 17.8973 24.3439 18.5728 24.5824 19.1292C24.8606 19.7253 24.9798 20.4406 24.9798 21.1957C24.9798 21.8315 24.8208 22.6262 24.5824 23.1826C24.3439 23.7787 23.9863 24.3747 23.4697 24.8119C23.0723 25.2093 22.3967 25.6862 21.8404 25.9246C21.2045 26.2027 20.4893 26.322 19.7739 26.322H12.2235Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M43.4984 15.1948C41.9883 15.1948 40.7962 14.7576 39.9219 13.8436L40.9153 12.4528C41.6307 13.2078 42.5447 13.6053 43.6177 13.6053C44.0945 13.6053 44.4522 13.5258 44.6906 13.3271C44.9687 13.1284 45.088 12.9296 45.088 12.6515C45.088 12.4131 44.9687 12.2143 44.7304 12.0952C44.4919 11.9362 44.174 11.817 43.8164 11.7772C43.4587 11.6978 43.0613 11.6183 42.6242 11.4991C42.1871 11.3798 41.7897 11.2209 41.432 11.062C41.0743 10.903 40.7564 10.6645 40.518 10.3069C40.2795 9.94923 40.1603 9.55179 40.1603 9.03522C40.1603 8.31993 40.4385 7.68411 41.0346 7.20721C41.6307 6.69057 42.3857 6.45215 43.3792 6.45215C44.7304 6.45215 45.843 6.84956 46.6776 7.64431L45.6841 8.95576C45.0085 8.31993 44.174 8.04175 43.2203 8.04175C42.8229 8.04175 42.5447 8.12121 42.346 8.28014C42.1473 8.43913 42.0281 8.63778 42.0281 8.91596C42.0281 9.11468 42.1473 9.27361 42.3857 9.4326C42.6242 9.59158 42.9421 9.67105 43.2997 9.75051C43.6574 9.82997 44.0547 9.90943 44.4919 10.0684C44.9291 10.1876 45.2867 10.3466 45.6841 10.5056C46.0815 10.6645 46.3597 10.9427 46.5981 11.3003C46.8366 11.658 46.9558 12.0952 46.9558 12.572C46.9558 13.4065 46.6776 14.0424 46.0815 14.559C45.4456 14.9564 44.6112 15.1948 43.4984 15.1948Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M49.9762 15.1949C49.4198 15.1949 48.9828 15.0359 48.6647 14.7577C48.3467 14.4795 48.2275 14.0425 48.2275 13.5259V10.3069H47.1943V8.8763H48.2275V7.20728H49.8568V8.8763H51.0889V10.3069H49.8568V13.0887C49.8568 13.2874 49.8967 13.4463 50.0156 13.5656C50.1349 13.6848 50.2543 13.7643 50.4131 13.7643C50.6518 13.7643 50.8502 13.6848 50.9695 13.5656L51.3271 14.7975C51.0489 15.0757 50.572 15.1949 49.9762 15.1949Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M57.5262 15.0756H55.897V14.4398C55.4595 14.9563 54.7843 15.2345 53.9498 15.2345C53.3934 15.2345 52.9164 15.0358 52.4795 14.6782C52.042 14.3206 51.8037 13.804 51.8037 13.1681C51.8037 12.4926 52.0025 12.0157 52.4395 11.658C52.8765 11.3401 53.3535 11.1414 53.9498 11.1414C54.8238 11.1414 55.4595 11.3798 55.897 11.8964V11.0619C55.897 10.744 55.7777 10.5056 55.5395 10.3069C55.3007 10.1082 54.9831 10.0287 54.5456 10.0287C53.8704 10.0287 53.274 10.2671 52.7576 10.744L52.1613 9.67109C52.8765 9.0352 53.791 8.71729 54.8637 8.71729C55.6583 8.71729 56.2941 8.916 56.811 9.27365C57.3274 9.63129 57.5661 10.2274 57.5661 11.0619V15.0756H57.5262ZM54.6255 14.1218C55.2213 14.1218 55.6583 13.9629 55.9365 13.6052V12.8502C55.6583 12.4926 55.2213 12.3336 54.6255 12.3336C54.3074 12.3336 54.0292 12.413 53.791 12.572C53.5522 12.731 53.4728 12.9694 53.4728 13.2476C53.4728 13.5258 53.5922 13.7642 53.791 13.9231C54.0292 14.0424 54.3074 14.1218 54.6255 14.1218Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M60.7854 15.0757H59.1558V8.91607H60.7854V9.75061C61.0236 9.47243 61.3018 9.23398 61.6594 9.03526C62.017 8.83661 62.3751 8.75708 62.7327 8.75708V10.3466C62.6133 10.3069 62.4545 10.3069 62.2952 10.3069C62.017 10.3069 61.7389 10.3864 61.4212 10.5056C61.103 10.6248 60.9043 10.8235 60.7854 11.0223V15.0757Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M65.7926 15.1949C65.2362 15.1949 64.7988 15.0359 64.4811 14.7577C64.1629 14.4795 64.0436 14.0425 64.0436 13.5259V10.3069H63.0107V8.8763H64.0436V7.20728H65.6732V8.8763H66.905V10.3069H65.6732V13.0887C65.6732 13.2874 65.7127 13.4463 65.832 13.5656C65.9514 13.6848 66.0705 13.7643 66.2295 13.7643C66.4678 13.7643 66.6668 13.6848 66.7859 13.5656L67.1433 14.7975C66.8651 15.0757 66.4285 15.1949 65.7926 15.1949Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M65.7926 15.1949C65.2362 15.1949 64.7988 15.0359 64.4811 14.7577C64.1629 14.4795 64.0436 14.0425 64.0436 13.5259V10.3069H63.0107V8.8763H64.0436V7.20728H65.6732V8.8763H66.905V10.3069H65.6732V13.0887C65.6732 13.2874 65.7127 13.4463 65.832 13.5656C65.9514 13.6848 66.0705 13.7643 66.2295 13.7643C66.4678 13.7643 66.6668 13.6848 66.7859 13.5656L67.1433 14.7975C66.8651 15.0757 66.4285 15.1949 65.7926 15.1949Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M70.9587 15.1949C70.005 15.1949 69.2097 14.8769 68.6141 14.3207C68.0178 13.7643 67.6997 12.9297 67.6997 11.976C67.6997 11.062 68.0178 10.307 68.6141 9.67109C69.2097 9.03526 69.9651 8.75708 70.9188 8.75708C71.8325 8.75708 72.5879 9.07499 73.1442 9.71082C73.7006 10.3466 74.018 11.1415 74.018 12.1747V12.5323H69.4487C69.4879 12.9297 69.6869 13.2874 70.005 13.5656C70.3225 13.8438 70.7198 13.9629 71.2362 13.9629C71.5144 13.9629 71.7926 13.9232 72.1107 13.804C72.4288 13.6848 72.6671 13.5258 72.866 13.3668L73.5815 14.4001C72.866 14.9167 72.0315 15.1949 70.9587 15.1949ZM72.3489 11.3799C72.3097 11.0223 72.1905 10.7043 71.9523 10.4262C71.7134 10.148 71.3161 9.989 70.8396 9.989C70.3624 9.989 70.005 10.1083 69.7661 10.3864C69.5279 10.6646 69.3688 10.9825 69.3289 11.3402H72.3489V11.3799Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M76.7205 15.0757H75.0913V8.91607H76.7205V9.75061C76.9587 9.47243 77.2369 9.23398 77.5949 9.03526C77.9523 8.83661 78.3104 8.75708 78.6677 8.75708V10.3466C78.5486 10.3069 78.3896 10.3069 78.2305 10.3069C77.9523 10.3069 77.6741 10.3864 77.356 10.5056C77.0386 10.6248 76.8396 10.8235 76.7205 11.0223V15.0757Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M44.2535 28.0704H42.4255V21.1558H39.9219V19.5662H46.7173V21.1558H44.2535V28.0704Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M49.4989 28.2293C48.5452 28.2293 47.7504 27.9114 47.1542 27.355C46.5184 26.7589 46.2402 25.9642 46.2402 25.0104C46.2402 24.0964 46.5582 23.3413 47.1542 22.7055C47.7504 22.0697 48.5054 21.7915 49.459 21.7915C50.3734 21.7915 51.1281 22.1095 51.6844 22.7453C52.2408 23.3811 52.519 24.1759 52.519 25.2091V25.5667H47.9491C47.9888 25.9642 48.1875 26.3218 48.5054 26.6C48.8231 26.8782 49.2207 26.9974 49.7371 26.9974C50.0153 26.9974 50.2935 26.9576 50.6116 26.8384C50.9297 26.7192 51.168 26.5602 51.3668 26.4013L52.0819 27.4345C51.4062 27.9511 50.5717 28.2293 49.4989 28.2293ZM50.9297 24.4143C50.8898 24.0567 50.7704 23.7388 50.5322 23.4606C50.2935 23.1824 49.8964 23.0235 49.4195 23.0235C48.9425 23.0235 48.5849 23.1427 48.3464 23.4209C48.108 23.699 47.9491 24.017 47.9093 24.3745H50.9297V24.4143Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M62.9308 28.0705H61.3017V24.176C61.3017 23.5402 60.984 23.1825 60.3877 23.1825C60.1095 23.1825 59.8713 23.262 59.6725 23.3812C59.4338 23.5402 59.275 23.6992 59.1161 23.8978V28.0705H57.4865V24.176C57.4865 23.5402 57.1689 23.1825 56.5726 23.1825C56.3343 23.1825 56.0561 23.262 55.8574 23.3812C55.6192 23.5402 55.4598 23.6992 55.301 23.8978V28.0705H53.6719V21.9108H55.301V22.7056C55.4598 22.4672 55.738 22.2685 56.0961 22.0698C56.4932 21.8711 56.8907 21.752 57.2882 21.752C58.1623 21.752 58.7585 22.1096 58.9968 22.8646C59.1956 22.5466 59.4738 22.3083 59.8713 22.0698C60.2683 21.8314 60.6659 21.752 61.1029 21.752C61.6992 21.752 62.1362 21.9109 62.4543 22.2288C62.772 22.5466 62.9308 23.0236 62.9308 23.6196V28.0705Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M68.0576 28.2293C67.3023 28.2293 66.6667 27.9114 66.1503 27.2755V30.4149H64.5205V21.9108H66.1503V22.7055C66.6268 22.0697 67.263 21.7915 68.0576 21.7915C68.8522 21.7915 69.5277 22.0697 70.0442 22.6658C70.5612 23.2619 70.7995 24.017 70.7995 25.0502C70.7995 26.0436 70.5612 26.8384 70.0442 27.4345C69.5277 27.9114 68.8522 28.2293 68.0576 28.2293ZM67.5412 26.7589C68.0177 26.7589 68.3757 26.6 68.6539 26.2423C68.9314 25.9244 69.0905 25.4873 69.0905 24.931C69.0905 24.4143 68.9314 23.9772 68.6539 23.6593C68.3757 23.3413 67.9778 23.1824 67.5412 23.1824C67.263 23.1824 67.0241 23.2619 66.7459 23.3811C66.4677 23.5003 66.2687 23.699 66.1503 23.8977V26.0834C66.2687 26.282 66.5076 26.441 66.7459 26.5602C67.0241 26.7192 67.3023 26.7589 67.5412 26.7589Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M73.5813 28.0704H71.9521V19.5662H73.5813V28.0704Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M80.4956 28.0705H78.8665V27.4347C78.4292 27.9512 77.7537 28.2294 76.9192 28.2294C76.3628 28.2294 75.8857 28.0307 75.4484 27.673C75.0119 27.3154 74.7729 26.7988 74.7729 26.163C74.7729 25.4874 74.9719 25.0105 75.4085 24.6528C75.8464 24.335 76.3229 24.1363 76.9192 24.1363C77.793 24.1363 78.4292 24.3747 78.8665 24.8913V24.0965C78.8665 23.7786 78.7467 23.5402 78.5084 23.3414C78.2702 23.1428 77.9521 23.0633 77.5148 23.0633C76.8393 23.0633 76.2431 23.3018 75.7266 23.7786L75.1303 22.7056C75.8464 22.0698 76.7601 21.752 77.8329 21.752C78.6276 21.752 79.2638 21.9506 79.7802 22.3083C80.2967 22.666 80.5356 23.262 80.5356 24.0965V28.0705H80.4956ZM77.5947 27.1167C78.1903 27.1167 78.6276 26.9577 78.9057 26.6001V25.845C78.6276 25.4874 78.1903 25.3285 77.5947 25.3285C77.2766 25.3285 76.9984 25.4079 76.7601 25.5668C76.5212 25.7258 76.442 25.9643 76.442 26.2425C76.442 26.5206 76.5612 26.759 76.7601 26.918C76.9984 27.0372 77.2373 27.1167 77.5947 27.1167Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M84.1915 28.2293C83.6351 28.2293 83.1979 28.0703 82.8798 27.7921C82.5623 27.514 82.4432 27.0768 82.4432 26.5602V23.3016H81.4097V21.871H82.4432V20.2019H84.0724V21.871H85.3042V23.3016H84.0724V26.0833C84.0724 26.282 84.1123 26.441 84.2314 26.5602C84.3505 26.6794 84.4697 26.7589 84.6287 26.7589C84.867 26.7589 85.066 26.6794 85.1851 26.5602L85.5425 27.7921C85.225 28.0703 84.7878 28.2293 84.1915 28.2293Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M89.3179 28.2293C88.3642 28.2293 87.5696 27.9114 86.9733 27.355C86.3377 26.7589 86.0596 25.9642 86.0596 25.0104C86.0596 24.0964 86.377 23.3413 86.9733 22.7055C87.5696 22.0697 88.3243 21.7915 89.278 21.7915C90.1924 21.7915 90.9471 22.1095 91.5034 22.7453C92.0598 23.3811 92.338 24.1759 92.338 25.2091V25.5667H87.7679C87.8079 25.9642 88.0068 26.3218 88.3243 26.6C88.6424 26.8782 89.0397 26.9974 89.5561 26.9974C89.8343 26.9974 90.1125 26.9576 90.4306 26.8384C90.7487 26.7192 90.987 26.5602 91.1853 26.4013L91.9014 27.4345C91.2252 27.9511 90.3907 28.2293 89.3179 28.2293ZM90.7487 24.4143C90.7088 24.0567 90.5897 23.7388 90.3508 23.4606C90.1125 23.1824 89.7152 23.0235 89.238 23.0235C88.7615 23.0235 88.4035 23.1427 88.1652 23.4209C87.927 23.699 87.7679 24.017 87.7287 24.3745H90.7487V24.4143Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M95.7159 28.2293C95.1995 28.2293 94.6824 28.1498 94.1659 27.9908C93.6888 27.8319 93.2522 27.5935 92.9341 27.2755L93.6495 26.1231C93.8878 26.3615 94.2059 26.5205 94.6032 26.7192C95.0005 26.8782 95.3978 26.9576 95.7558 26.9576C96.1132 26.9576 96.3515 26.9179 96.5105 26.7987C96.6696 26.6795 96.7887 26.5205 96.7887 26.3218C96.7887 26.1628 96.7088 26.0436 96.5105 25.9642C96.3122 25.8847 96.1132 25.8052 95.835 25.7654C95.5569 25.7257 95.2787 25.6462 94.9606 25.5667C94.6431 25.4873 94.3649 25.3681 94.0867 25.2488C93.8086 25.1296 93.6096 24.9309 93.4106 24.6925C93.2123 24.4143 93.1324 24.0964 93.1324 23.7388C93.1324 23.1824 93.3713 22.7453 93.8086 22.3479C94.2451 21.9902 94.8814 21.7915 95.676 21.7915C96.6296 21.7915 97.4642 22.0697 98.1796 22.626L97.5434 23.7388C97.3451 23.5401 97.1068 23.3811 96.7488 23.2221C96.4313 23.103 96.0733 23.0235 95.7159 23.0235C95.4377 23.0235 95.1995 23.0632 95.0005 23.1824C94.8015 23.3016 94.7223 23.4606 94.7223 23.6195C94.7223 23.7388 94.8015 23.858 95.0005 23.9772C95.1595 24.0567 95.3978 24.1362 95.676 24.1759C95.9542 24.2156 96.2323 24.2951 96.5505 24.3745C96.8686 24.4541 97.1467 24.5733 97.4249 24.6925C97.7031 24.8117 97.9414 25.0104 98.0998 25.2885C98.2595 25.5667 98.3779 25.8847 98.3779 26.282C98.3779 26.8782 98.1397 27.3153 97.6632 27.7127C97.186 28.0306 96.5105 28.2293 95.7159 28.2293Z",fill:"currentColor"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"clip_spec_ai_starter_templates_logo"},(0,e.createElement)("rect",{width:98.1818,height:36,fill:"white",transform:"translate(0.540527)"})))),x=(0,e.createElement)("svg",{width:197,height:32,viewBox:"0 0 197 32",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("g",{clipPath:"url(#clip_spec_ai_presto_player_logo)"},(0,e.createElement)("mask",{id:"mask_spec_ai_presto_player_logo",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:0,y:0,width:197,height:32},(0,e.createElement)("path",{d:"M196.266 0H0.909668V31.3308H196.266V0Z",fill:"white"})),(0,e.createElement)("g",{mask:"url(#mask_spec_ai_presto_player_logo)"},(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M55.0934 18.8195C55.0934 21.6678 52.7897 23.689 49.4723 23.689C46.9842 23.689 44.8649 22.5866 43.7592 20.749L46.4315 18.452C47.1686 19.6465 48.4588 20.2896 49.6567 20.2896C50.8545 20.2896 51.5919 19.7384 51.5919 19.0033C51.5919 18.2682 50.9468 17.717 49.3803 17.1657L48.2743 16.7981C45.6941 15.9713 44.312 14.4093 44.312 12.2961C44.312 9.17221 46.6156 7.42651 49.7489 7.42651C51.684 7.42651 53.3426 8.06968 54.8169 9.63166L52.5132 12.0205C51.7761 11.1936 50.7624 10.7341 49.7489 10.7341C48.7352 10.7341 47.8137 11.1017 47.8137 11.9286C47.8137 12.7555 48.5508 13.2149 50.2095 13.7661L51.3154 14.1337C53.6191 14.9606 55.0934 16.4306 55.0934 18.8195ZM6.25433 7.61025C9.38743 7.61025 11.7833 9.99915 11.7833 13.0312C11.7833 16.0632 9.38743 18.452 6.25433 18.3601H4.41135V23.3217H0.909668V7.61025H6.25433ZM6.16217 15.0525C7.45228 15.0525 8.37376 14.2255 8.37376 13.0312C8.37376 11.9286 7.45228 11.0098 6.16217 10.9179H4.41135V15.0525H6.16217ZM18.6945 23.4136H15.1928V7.70213H21.3668C24.3156 7.70213 26.2508 9.90728 26.2508 12.4798C26.2508 14.5012 25.2372 15.7875 23.2098 16.5225L28.7387 23.4136H24.4998L19.2474 16.89H18.6945V23.4136ZM18.6945 10.9179V13.858H20.9982C22.0118 13.858 22.749 13.3068 22.749 12.388C22.749 11.4692 22.0118 10.9179 20.9982 10.9179H18.6945ZM30.9503 23.4136H40.9025V20.014H34.452V17.1656H39.6124V13.7661H34.452V10.9179H40.9025V7.70213H30.9503V23.4136ZM61.2674 23.4136H64.8612V11.0098H68.9159V7.70213H57.2129V11.1016H61.2674V23.4136ZM86.8848 15.6037C86.8848 20.0139 83.2912 23.689 78.7758 23.689C74.2605 23.689 70.6667 20.0139 70.6667 15.6037C70.6667 11.1935 74.2605 7.51837 78.7758 7.51837C83.1991 7.51837 86.8848 11.1016 86.8848 15.6037ZM74.1684 15.6037C74.1684 18.0845 76.1957 20.2896 78.7758 20.2896C81.356 20.2896 83.3833 18.0845 83.3833 15.6037C83.3833 13.1229 81.356 10.9179 78.7758 10.9179C76.1957 10.9179 74.1684 13.1229 74.1684 15.6037Z",fill:"currentColor"}),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M148.072 21.644H146.137V5.10571H148.072V21.644ZM134.066 26.3299H132.13V10.5266H134.066V12.2723C134.802 11.1697 136.277 10.3428 137.936 10.3428C140.977 10.3428 143.372 13.0073 143.372 16.1312C143.372 19.347 140.977 21.9196 137.936 21.9196C136.277 21.9196 134.802 21.0927 134.066 19.9901V26.3299ZM137.659 20.1738C139.871 20.1738 141.437 18.3363 141.437 16.1312C141.437 13.9261 139.871 12.1804 137.659 12.0885C135.447 12.0885 133.881 13.9261 133.881 16.1312C133.881 18.4281 135.447 20.1738 137.659 20.1738ZM150.744 16.1312C150.744 19.2551 153.048 21.9196 156.181 21.9196C157.84 21.9196 159.314 21.0927 160.051 19.9901V21.7359H161.986V10.7103H160.051V12.456C159.314 11.3535 157.84 10.5266 156.181 10.5266C153.14 10.4347 150.744 13.0073 150.744 16.1312ZM160.236 16.1312C160.236 18.3363 158.669 20.1738 156.458 20.1738C154.246 20.1738 152.679 18.4281 152.679 16.1312C152.679 13.9261 154.246 12.0885 156.458 12.0885C158.761 12.1804 160.236 13.9261 160.236 16.1312ZM175.809 10.6185L168.53 26.3299H166.502L168.898 21.0927L164.014 10.6185H166.134L169.911 18.9795L173.69 10.6185H175.809ZM176.638 16.1312C176.638 19.347 178.942 21.9196 182.352 21.9196C184.655 21.9196 186.314 20.9089 187.328 19.2551L185.853 18.2445C185.208 19.4388 184.103 20.1739 182.352 20.1739C180.14 20.1739 178.573 18.612 178.482 16.5906H187.604V16.0393C187.604 12.456 185.208 10.5266 182.352 10.5266C179.034 10.4347 176.638 12.9155 176.638 16.1312ZM182.352 12.0885C184.01 12.0885 185.484 13.1911 185.762 15.0287H178.758C179.034 13.1911 180.693 12.0885 182.352 12.0885ZM192.304 21.644H190.369V10.7103H192.304V12.8235C192.764 11.3535 194.147 10.5266 195.436 10.5266C195.714 10.5266 195.99 10.5266 196.266 10.6185V12.5479C195.898 12.3641 195.621 12.3641 195.253 12.3641C193.87 12.3641 192.304 13.5586 192.304 16.1312V21.644Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M113.741 5.10571L122.6 10.4436L113.741 15.5493V5.10571Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M95.5576 5.10571L104.65 10.4436L95.5576 15.5493V5.10571Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M113.741 15.5495L122.6 10.4436V20.8872L113.741 15.5495Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M113.741 5.10571V15.5493L104.65 10.4436L113.741 5.10571Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M113.741 5.10576L104.65 10.4436V0L113.741 5.10576Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M104.65 10.4436V20.8872L95.5576 15.5494L104.65 10.4436Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M104.65 0V10.4436L95.5576 5.10576L104.65 0Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M104.65 20.8872V31.3308L95.5576 25.993L104.65 20.8872Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M113.741 15.5493L122.6 20.8872L113.741 25.9929V15.5493Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M95.5576 15.5493L104.65 20.8872L95.5576 25.9929V15.5493Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M113.741 15.5493V25.9929L104.65 20.8872L113.741 15.5493Z",fill:"currentColor"}))),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"clip_spec_ai_presto_player_logo"},(0,e.createElement)("rect",{width:196.211,height:32,fill:"white",transform:"translate(0.508301)"})))),L=(0,e.createElement)("svg",{width:326,height:43,viewBox:"0 0 326 43",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M0 8.46676C0 5.44802 2.44802 3 5.46676 3H38.2673C41.2872 3 43.7341 5.44802 43.7341 8.46676V30.3338C43.7341 33.3537 41.2872 35.8006 38.2673 35.8006H11.0658L3.51075 41.5494C2.07081 42.645 0 41.6183 0 39.8088V22.4671V22.4606V8.46676ZM12.8863 17.2092C12.356 16.1246 11.0461 15.6741 9.96153 16.2044C8.87584 16.7347 8.42647 18.0445 8.95674 19.1291C11.2878 23.8994 16.1904 27.1926 21.867 27.1926C27.5437 27.1926 32.4463 23.8994 34.7774 19.1291C35.3076 18.0434 34.8583 16.7347 33.7726 16.2044C32.688 15.6741 31.3781 16.1246 30.8478 17.2092C29.222 20.5352 25.8097 22.8192 21.867 22.8192C17.9244 22.8192 14.5121 20.5363 12.8863 17.2092Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M54.6719 29.3347L56.6477 26.5989C58.2689 28.4734 61.3594 30.0946 64.7031 30.0946C68.4522 30.0946 70.4787 28.4228 70.4787 26.0416C70.4787 23.3565 67.4896 22.4952 64.1965 21.6846C60.0421 20.722 55.3305 19.6074 55.3305 14.5412C55.3305 10.7414 58.6236 7.49902 64.4498 7.49902C68.6041 7.49902 71.5426 9.06956 73.5184 11.0454L71.7452 13.6799C70.276 11.9067 67.5909 10.5895 64.4498 10.5895C61.106 10.5895 59.0289 12.1093 59.0289 14.2878C59.0289 16.669 61.8153 17.4289 65.0071 18.1889C69.2628 19.1515 74.177 20.3674 74.177 25.7883C74.177 29.892 70.884 33.1851 64.5511 33.1851C60.5488 33.1851 57.1543 31.9692 54.6719 29.3347ZM94.5862 32.5771V29.132C92.7117 31.2599 89.52 33.1851 85.8722 33.1851C80.7553 33.1851 78.0702 30.7026 78.0702 25.383V8.10697H81.8699V24.2178C81.8699 28.5241 84.0484 29.7907 87.3415 29.7907C90.3306 29.7907 93.1677 28.0681 94.5862 26.0923V8.10697H98.386V32.5771H94.5862ZM103.694 32.5771V8.10697H107.494V12.0587C109.47 9.47487 112.307 7.60034 115.651 7.60034V11.5014C115.195 11.4001 114.739 11.3494 114.131 11.3494C111.801 11.3494 108.609 13.2746 107.494 15.2504V32.5771H103.694ZM116.719 20.3167C116.719 13.2239 121.785 7.49902 128.777 7.49902C136.173 7.49902 140.53 13.2746 140.53 20.6207V21.5833H120.721C121.025 26.1936 124.268 30.044 129.536 30.044C132.323 30.044 135.16 28.9294 137.085 26.9535L138.909 29.436C136.477 31.8678 133.184 33.1851 129.182 33.1851C121.937 33.1851 116.719 27.9668 116.719 20.3167ZM128.726 10.6401C123.508 10.6401 120.924 15.0478 120.721 18.7968H136.781C136.731 15.1491 134.299 10.6401 128.726 10.6401Z",fill:"currentColor"}),(0,e.createElement)("path",{d:"M150.936 32.9997H145.867V9.1905H162.716V13.6525H150.936V18.6856H162.466V23.1476H150.936V32.9997ZM183.108 32.9997H166.26V9.1905H183.108V13.6525H171.329V18.6856H182.859V23.1476H171.329V28.5377H183.108V32.9997ZM203.919 32.9997H187.071V9.1905H203.919V13.6525H192.14V18.6856H203.67V23.1476H192.14V28.5377H203.919V32.9997ZM217.27 32.9997H207.882V9.1905H217.27C221.006 9.1905 224.04 10.2852 226.372 12.4745C228.728 14.6639 229.906 17.5434 229.906 21.113C229.906 24.6826 228.74 27.562 226.408 29.7514C224.076 31.9169 221.03 32.9997 217.27 32.9997ZM217.27 28.5377C219.554 28.5377 221.363 27.8238 222.696 26.396C224.052 24.9681 224.73 23.2071 224.73 21.113C224.73 18.9236 224.076 17.1388 222.767 15.7586C221.458 14.3545 219.626 13.6525 217.27 13.6525H212.951V28.5377H217.27ZM246.425 32.9997H233.503V9.1905H246.033C248.198 9.1905 249.876 9.78544 251.066 10.9753C252.28 12.1414 252.886 13.5692 252.886 15.2588C252.886 16.7105 252.494 17.9241 251.708 18.8998C250.923 19.8755 249.947 20.4823 248.781 20.7203C250.066 20.9107 251.149 21.5651 252.03 22.6836C252.91 23.8021 253.35 25.099 253.35 26.5744C253.35 28.4306 252.744 29.9656 251.53 31.1792C250.316 32.3929 248.615 32.9997 246.425 32.9997ZM244.926 18.7213C245.783 18.7213 246.461 18.4834 246.961 18.0074C247.461 17.5315 247.71 16.9127 247.71 16.1512C247.71 15.3897 247.449 14.771 246.925 14.295C246.425 13.7953 245.759 13.5454 244.926 13.5454H238.572V18.7213H244.926ZM245.105 28.6091C246.057 28.6091 246.806 28.3711 247.353 27.8952C247.901 27.3954 248.175 26.7172 248.175 25.8605C248.175 25.0752 247.901 24.4208 247.353 23.8972C246.806 23.3499 246.057 23.0762 245.105 23.0762H238.572V28.6091H245.105ZM279.376 32.9997H273.593L272.13 28.9661H261.921L260.421 32.9997H254.639L263.848 9.1905H270.202L279.376 32.9997ZM270.737 24.5041L267.025 14.2593L263.313 24.5041H270.737ZM291.884 33.4281C288.267 33.4281 285.245 32.2858 282.817 30.0012C280.414 27.6929 279.212 24.7301 279.212 21.113C279.212 17.4958 280.414 14.5449 282.817 12.2604C285.245 9.95202 288.267 8.79785 291.884 8.79785C296.334 8.79785 299.654 10.7373 301.843 14.6163L297.488 16.7581C296.965 15.7586 296.191 14.9376 295.168 14.295C294.169 13.6287 293.074 13.2956 291.884 13.2956C289.719 13.2956 287.934 14.0333 286.53 15.5087C285.126 16.9841 284.424 18.8522 284.424 21.113C284.424 23.3737 285.126 25.2418 286.53 26.7172C287.934 28.1927 289.719 28.9304 291.884 28.9304C293.074 28.9304 294.169 28.6091 295.168 27.9666C296.191 27.3241 296.965 26.4911 297.488 25.4679L301.843 27.5739C299.583 31.4767 296.263 33.4281 291.884 33.4281ZM325.243 32.9997H318.996L311.785 23.4689L309.929 25.682V32.9997H304.86V9.1905H309.929V19.8279L318.354 9.1905H324.6L315.141 20.3633L325.243 32.9997Z",fill:"currentColor"}));var _=()=>{const n=e=>{e.preventDefault(),window.location.assign(zip_ai_react.auth_middleware)},r=()=>(0,e.createElement)("div",{className:"flex flex-col items-center gap-4"},(0,e.createElement)("button",{className:"flex items-center justify-center gap-4 px-28 py-4 rounded text-base text-white bg-spec hover:bg-spec-hover focus-visible:bg-spec-hover transition-colors",onClick:n},(0,t.__)("Get Started Now","zip-ai"),(0,e.createElement)("span",null,"→")),(0,e.createElement)("div",{className:"flex items-center justify-center gap-2 text-slate-500"},(0,e.createElement)("a",{href:zip_ai_react?.admin_url,className:"text-sm underline hover:text-slate-700 transition-colors"},(0,t.__)("Go back to the dashboard","zip-ai")))),o=t=>(0,e.createElement)("div",{className:"flex gap-3"},(0,e.createElement)("div",{className:"w-7 text-slate-900"},t?.icon({width:28,height:28})),(0,e.createElement)("div",{className:"flex flex-col gap-2"},(0,e.createElement)("h2",{className:"text-base leading-7 font-semibold text-slate-900"},t?.title),(0,e.createElement)("p",{className:"text-sm font-normal text-slate-500"},t?.description)));return(0,e.createElement)("main",{className:"flex items-center justify-center w-full min-h-screen p-16 bg-slate-50"},(0,e.createElement)("article",{className:"flex flex-col items-center justify-center w-full max-w-3xl p-0 rounded bg-white shadow-overlay"},(0,e.createElement)("section",{className:"flex flex-col items-center gap-8 w-full px-12 py-10"},p({width:56,height:56,color:"#ff580e"}),(0,e.createElement)("section",{className:"flex flex-col items-center gap-6"},(0,e.createElement)("h1",{className:"text-2xl leading-7 font-bold text-center text-slate-900"},(0,t.__)("Welcome to the Zip AI Setup Wizard!","zip-ai")),(0,e.createElement)("p",{className:"text-sm font-normal text-center text-slate-500"},(0,t.__)("Zip AI is your WordPress assistant, accessible right within your backend. Create persuasive content, generate custom code, and get answers to your WordPress queries in seconds. The possibilities are endless!","zip-ai")),r())),(0,e.createElement)("section",{className:"grid grid-cols-2 gap-6 w-full px-12 py-10 bg-violet-50"},o({icon:g,title:(0,t.__)("Craft Compelling Content","zip-ai"),description:(0,t.__)("With Zip, you can effortlessly create persuasive, engaging copy that resonates with your audience.","zip-ai")}),o({icon:m,title:(0,t.__)("AI-Powered Block Patterns","zip-ai"),description:(0,t.__)("Zip can personalize and customize the block patterns and section templates tailored to your website's unique needs.","zip-ai")}),o({icon:f,title:(0,t.__)("WordPress Wizardry","zip-ai"),description:(0,t.__)("Got questions? Zip has the answers. Whether it's troubleshooting, or customizing your site, Zip has your back.","zip-ai")}),o({icon:h,title:(0,t.__)("Personalized Templates","zip-ai"),description:(0,t.__)("Say goodbye to generic designs and say hello to Zip's personalized page templates, tailored just for you.","zip-ai")}),o({icon:C,title:(0,t.__)("Generate Custom Code","zip-ai"),description:(0,t.__)("No more struggling with complex coding issues. Zip can whip up custom code, functions, and CSS tailored to your needs.","zip-ai")}),o({icon:v,title:(0,t.__)("Custom Pages with AI","zip-ai"),description:(0,t.sprintf)(/* translators: %s: Percentage Ascii Code */
(0,t.__)("With Zip AI by your side, you can create beautiful, 100%s custom web pages without the need for any design or coding skills.","zip-ai"),"%")})),(0,e.createElement)("section",{className:"flex flex-col items-center gap-12 w-full px-12 py-10"},(0,e.createElement)("h2",{className:"text-2xl leading-6 font-bold text-center text-slate-900"},(0,t.__)("From the Team Behind Some Iconic WordPress Products","zip-ai")),(0,e.createElement)("div",{className:"grid grid-cols-3 items-center gap-16 w-full text-slate-700 opacity-50"},(0,e.createElement)("div",{className:"flex justify-end"},E),(0,e.createElement)("div",{className:"flex justify-center"},w),(0,e.createElement)("div",{className:"flex justify-start"},b),(0,e.createElement)("div",{className:"flex justify-end"},x),(0,e.createElement)("div",{className:"flex justify-center"},L),(0,e.createElement)("div",{className:"flex justify-start"},y)),r())))};function H(e,t){return H=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},H(e,t)}function M(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,H(e,t)}var V=window.React,S=o.t(V,2),P=o.n(V),k=o(697),T=o.n(k);function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},N.apply(this,arguments)}function Z(e){return"/"===e.charAt(0)}function O(e,t){for(var n=t,r=n+1,o=e.length;r<o;n+=1,r+=1)e[n]=e[r];e.pop()}var R=function(e,t){void 0===t&&(t="");var n,r=e&&e.split("/")||[],o=t&&t.split("/")||[],a=e&&Z(e),l=t&&Z(t),i=a||l;if(e&&Z(e)?o=r:r.length&&(o.pop(),o=o.concat(r)),!o.length)return"/";if(o.length){var s=o[o.length-1];n="."===s||".."===s||""===s}else n=!1;for(var c=0,u=o.length;u>=0;u--){var d=o[u];"."===d?O(o,u):".."===d?(O(o,u),c++):c&&(O(o,u),c--)}if(!i)for(;c--;c)o.unshift("..");!i||""===o[0]||o[0]&&Z(o[0])||o.unshift("");var f=o.join("/");return n&&"/"!==f.substr(-1)&&(f+="/"),f},A=!0,F="Invariant failed";function j(e,t){if(!e){if(A)throw new Error(F);var n="function"==typeof t?t():t,r=n?"".concat(F,": ").concat(n):F;throw new Error(r)}}function D(e){return"/"===e.charAt(0)?e:"/"+e}function I(e,t){return function(e,t){return 0===e.toLowerCase().indexOf(t.toLowerCase())&&-1!=="/?#".indexOf(e.charAt(t.length))}(e,t)?e.substr(t.length):e}function z(e){return"/"===e.charAt(e.length-1)?e.slice(0,-1):e}function $(e){var t=e.pathname,n=e.search,r=e.hash,o=t||"/";return n&&"?"!==n&&(o+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(o+="#"===r.charAt(0)?r:"#"+r),o}function B(e,t,n,r){var o;"string"==typeof e?(o=function(e){var t=e||"/",n="",r="",o=t.indexOf("#");-1!==o&&(r=t.substr(o),t=t.substr(0,o));var a=t.indexOf("?");return-1!==a&&(n=t.substr(a),t=t.substr(0,a)),{pathname:t,search:"?"===n?"":n,hash:"#"===r?"":r}}(e),o.state=t):(void 0===(o=N({},e)).pathname&&(o.pathname=""),o.search?"?"!==o.search.charAt(0)&&(o.search="?"+o.search):o.search="",o.hash?"#"!==o.hash.charAt(0)&&(o.hash="#"+o.hash):o.hash="",void 0!==t&&void 0===o.state&&(o.state=t));try{o.pathname=decodeURI(o.pathname)}catch(e){throw e instanceof URIError?new URIError('Pathname "'+o.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):e}return n&&(o.key=n),r?o.pathname?"/"!==o.pathname.charAt(0)&&(o.pathname=R(o.pathname,r.pathname)):o.pathname=r.pathname:o.pathname||(o.pathname="/"),o}function U(){var e=null,t=[];return{setPrompt:function(t){return e=t,function(){e===t&&(e=null)}},confirmTransitionTo:function(t,n,r,o){if(null!=e){var a="function"==typeof e?e(t,n):e;"string"==typeof a?"function"==typeof r?r(a,o):o(!0):o(!1!==a)}else o(!0)},appendListener:function(e){var n=!0;function r(){n&&e.apply(void 0,arguments)}return t.push(r),function(){n=!1,t=t.filter((function(e){return e!==r}))}},notifyListeners:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.forEach((function(e){return e.apply(void 0,n)}))}}}var W=!("undefined"==typeof window||!window.document||!window.document.createElement);function q(e,t){t(window.confirm(e))}var G="popstate",K="hashchange";function Y(){try{return window.history.state||{}}catch(e){return{}}}var J=o(779),X=o.n(J);function Q(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}o(864),o(679);var ee=1073741823,te="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==o.g?o.g:{},ne=P().createContext||function(e,t){var n,r,o,a="__create-react-context-"+((te[o="__global_unique_id__"]=(te[o]||0)+1)+"__"),l=function(e){function n(){for(var t,n,r,o=arguments.length,a=new Array(o),l=0;l<o;l++)a[l]=arguments[l];return(t=e.call.apply(e,[this].concat(a))||this).emitter=(n=t.props.value,r=[],{on:function(e){r.push(e)},off:function(e){r=r.filter((function(t){return t!==e}))},get:function(){return n},set:function(e,t){n=e,r.forEach((function(e){return e(n,t)}))}}),t}M(n,e);var r=n.prototype;return r.getChildContext=function(){var e;return(e={})[a]=this.emitter,e},r.componentWillReceiveProps=function(e){if(this.props.value!==e.value){var n,r=this.props.value,o=e.value;!function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}(r,o)?(n="function"==typeof t?t(r,o):ee,0!=(n|=0)&&this.emitter.set(e.value,n)):n=0}},r.render=function(){return this.props.children},n}(P().Component);l.childContextTypes=((n={})[a]=T().object.isRequired,n);var i=function(t){function n(){for(var e,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=t.call.apply(t,[this].concat(r))||this).observedBits=void 0,e.state={value:e.getValue()},e.onUpdate=function(t,n){0!=((0|e.observedBits)&n)&&e.setState({value:e.getValue()})},e}M(n,t);var r=n.prototype;return r.componentWillReceiveProps=function(e){var t=e.observedBits;this.observedBits=null==t?ee:t},r.componentDidMount=function(){this.context[a]&&this.context[a].on(this.onUpdate);var e=this.props.observedBits;this.observedBits=null==e?ee:e},r.componentWillUnmount=function(){this.context[a]&&this.context[a].off(this.onUpdate)},r.getValue=function(){return this.context[a]?this.context[a].get():e},r.render=function(){return(e=this.props.children,Array.isArray(e)?e[0]:e)(this.state.value);var e},n}(P().Component);return i.contextTypes=((r={})[a]=T().object,r),{Provider:l,Consumer:i}},re=function(e){var t=ne();return t.displayName=e,t},oe=re("Router-History"),ae=re("Router"),le=function(e){function t(t){var n;return(n=e.call(this,t)||this).state={location:t.history.location},n._isMounted=!1,n._pendingLocation=null,t.staticContext||(n.unlisten=t.history.listen((function(e){n._pendingLocation=e}))),n}M(t,e),t.computeRootMatch=function(e){return{path:"/",url:"/",params:{},isExact:"/"===e}};var n=t.prototype;return n.componentDidMount=function(){var e=this;this._isMounted=!0,this.unlisten&&this.unlisten(),this.props.staticContext||(this.unlisten=this.props.history.listen((function(t){e._isMounted&&e.setState({location:t})}))),this._pendingLocation&&this.setState({location:this._pendingLocation})},n.componentWillUnmount=function(){this.unlisten&&(this.unlisten(),this._isMounted=!1,this._pendingLocation=null)},n.render=function(){return P().createElement(ae.Provider,{value:{history:this.props.history,location:this.state.location,match:t.computeRootMatch(this.state.location.pathname),staticContext:this.props.staticContext}},P().createElement(oe.Provider,{children:this.props.children||null,value:this.props.history}))},t}(P().Component);P().Component,P().Component;var ie={},se=0;function ce(e,t){void 0===t&&(t={}),("string"==typeof t||Array.isArray(t))&&(t={path:t});var n=t,r=n.path,o=n.exact,a=void 0!==o&&o,l=n.strict,i=void 0!==l&&l,s=n.sensitive,c=void 0!==s&&s;return[].concat(r).reduce((function(t,n){if(!n&&""!==n)return null;if(t)return t;var r=function(e,t){var n=""+t.end+t.strict+t.sensitive,r=ie[n]||(ie[n]={});if(r[e])return r[e];var o=[],a={regexp:X()(e,o,t),keys:o};return se<1e4&&(r[e]=a,se++),a}(n,{end:a,strict:i,sensitive:c}),o=r.regexp,l=r.keys,s=o.exec(e);if(!s)return null;var u=s[0],d=s.slice(1),f=e===u;return a&&!f?null:{path:n,url:"/"===n&&""===u?"/":u,isExact:f,params:l.reduce((function(e,t,n){return e[t.name]=d[n],e}),{})}}),null)}var ue=function(e){function t(){return e.apply(this,arguments)||this}return M(t,e),t.prototype.render=function(){var e=this;return P().createElement(ae.Consumer,null,(function(t){t||j(!1);var n=e.props.location||t.location,r=N({},t,{location:n,match:e.props.computedMatch?e.props.computedMatch:e.props.path?ce(n.pathname,e.props):t.match}),o=e.props,a=o.children,l=o.component,i=o.render;return Array.isArray(a)&&function(e){return 0===P().Children.count(e)}(a)&&(a=null),P().createElement(ae.Provider,{value:r},r.match?a?"function"==typeof a?a(r):a:l?P().createElement(l,r):i?i(r):null:"function"==typeof a?a(r):null)}))},t}(P().Component);P().Component;var de=function(e){function t(){return e.apply(this,arguments)||this}return M(t,e),t.prototype.render=function(){var e=this;return P().createElement(ae.Consumer,null,(function(t){t||j(!1);var n,r,o=e.props.location||t.location;return P().Children.forEach(e.props.children,(function(e){if(null==r&&P().isValidElement(e)){n=e;var a=e.props.path||e.props.from;r=a?ce(o.pathname,N({},e.props,{path:a})):t.match}})),r?P().cloneElement(n,{location:o,computedMatch:r}):null}))},t}(P().Component),fe=P().useContext;var pe=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(t=e.call.apply(e,[this].concat(r))||this).history=function(e){void 0===e&&(e={}),W||j(!1);var t,n=window.history,r=(-1===(t=window.navigator.userAgent).indexOf("Android 2.")&&-1===t.indexOf("Android 4.0")||-1===t.indexOf("Mobile Safari")||-1!==t.indexOf("Chrome")||-1!==t.indexOf("Windows Phone"))&&window.history&&"pushState"in window.history,o=!(-1===window.navigator.userAgent.indexOf("Trident")),a=e,l=a.forceRefresh,i=void 0!==l&&l,s=a.getUserConfirmation,c=void 0===s?q:s,u=a.keyLength,d=void 0===u?6:u,f=e.basename?z(D(e.basename)):"";function p(e){var t=e||{},n=t.key,r=t.state,o=window.location,a=o.pathname+o.search+o.hash;return f&&(a=I(a,f)),B(a,r,n)}function m(){return Math.random().toString(36).substr(2,d)}var C=U();function h(e){N(V,e),V.length=n.length,C.notifyListeners(V.location,V.action)}function v(e){(function(e){return void 0===e.state&&-1===navigator.userAgent.indexOf("CriOS")})(e)||E(p(e.state))}function g(){E(p(Y()))}var y=!1;function E(e){y?(y=!1,h()):C.confirmTransitionTo(e,"POP",c,(function(t){t?h({action:"POP",location:e}):function(e){var t=V.location,n=b.indexOf(t.key);-1===n&&(n=0);var r=b.indexOf(e.key);-1===r&&(r=0);var o=n-r;o&&(y=!0,L(o))}(e)}))}var w=p(Y()),b=[w.key];function x(e){return f+$(e)}function L(e){n.go(e)}var _=0;function H(e){1===(_+=e)&&1===e?(window.addEventListener(G,v),o&&window.addEventListener(K,g)):0===_&&(window.removeEventListener(G,v),o&&window.removeEventListener(K,g))}var M=!1,V={length:n.length,action:"POP",location:w,createHref:x,push:function(e,t){var o="PUSH",a=B(e,t,m(),V.location);C.confirmTransitionTo(a,o,c,(function(e){if(e){var t=x(a),l=a.key,s=a.state;if(r)if(n.pushState({key:l,state:s},null,t),i)window.location.href=t;else{var c=b.indexOf(V.location.key),u=b.slice(0,c+1);u.push(a.key),b=u,h({action:o,location:a})}else window.location.href=t}}))},replace:function(e,t){var o="REPLACE",a=B(e,t,m(),V.location);C.confirmTransitionTo(a,o,c,(function(e){if(e){var t=x(a),l=a.key,s=a.state;if(r)if(n.replaceState({key:l,state:s},null,t),i)window.location.replace(t);else{var c=b.indexOf(V.location.key);-1!==c&&(b[c]=a.key),h({action:o,location:a})}else window.location.replace(t)}}))},go:L,goBack:function(){L(-1)},goForward:function(){L(1)},block:function(e){void 0===e&&(e=!1);var t=C.setPrompt(e);return M||(H(1),M=!0),function(){return M&&(M=!1,H(-1)),t()}},listen:function(e){var t=C.appendListener(e);return H(1),function(){H(-1),t()}}};return V}(t.props),t}return M(t,e),t.prototype.render=function(){return P().createElement(le,{history:this.history,children:this.props.children})},t}(P().Component);P().Component;var me=function(e,t){return"function"==typeof e?e(t):e},Ce=function(e,t){return"string"==typeof e?B(e,null,null,t):e},he=function(e){return e},ve=P().forwardRef;void 0===ve&&(ve=he);var ge=ve((function(e,t){var n=e.innerRef,r=e.navigate,o=e.onClick,a=Q(e,["innerRef","navigate","onClick"]),l=a.target,i=N({},a,{onClick:function(e){try{o&&o(e)}catch(t){throw e.preventDefault(),t}e.defaultPrevented||0!==e.button||l&&"_self"!==l||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)||(e.preventDefault(),r())}});return i.ref=he!==ve&&t||n,P().createElement("a",i)})),ye=ve((function(e,t){var n=e.component,r=void 0===n?ge:n,o=e.replace,a=e.to,l=e.innerRef,i=Q(e,["component","replace","to","innerRef"]);return P().createElement(ae.Consumer,null,(function(e){e||j(!1);var n=e.history,s=Ce(me(a,e.location),e.location),c=s?n.createHref(s):"",u=N({},i,{href:c,navigate:function(){var t=me(a,e.location),r=$(e.location)===$(Ce(t));(o||r?n.replace:n.push)(t)}});return he!==ve?u.ref=t||l:u.innerRef=l,P().createElement(r,u)}))})),Ee=function(e){return e},we=P().forwardRef;function be(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map((e=>`"${e}"`)).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,be),r}function xe(...e){return Array.from(new Set(e.flatMap((e=>"string"==typeof e?e.split(" "):[])))).filter(Boolean).join(" ")}void 0===we&&(we=Ee),we((function(e,t){var n=e["aria-current"],r=void 0===n?"page":n,o=e.activeClassName,a=void 0===o?"active":o,l=e.activeStyle,i=e.className,s=e.exact,c=e.isActive,u=e.location,d=e.sensitive,f=e.strict,p=e.style,m=e.to,C=e.innerRef,h=Q(e,["aria-current","activeClassName","activeStyle","className","exact","isActive","location","sensitive","strict","style","to","innerRef"]);return P().createElement(ae.Consumer,null,(function(e){e||j(!1);var n=u||e.location,o=Ce(me(m,n),n),v=o.pathname,g=v&&v.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1"),y=g?ce(n.pathname,{path:g,exact:s,sensitive:d,strict:f}):null,E=!!(c?c(y,n):y),w="function"==typeof i?i(E):i,b="function"==typeof p?p(E):p;E&&(w=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((function(e){return e})).join(" ")}(w,a),b=N({},b,l));var x=N({"aria-current":E&&r||null,className:w,style:b,to:o},h);return Ee!==we?x.ref=t||C:x.innerRef=C,P().createElement(ye,x)}))}));var Le,_e,He=((_e=He||{})[_e.None=0]="None",_e[_e.RenderStrategy=1]="RenderStrategy",_e[_e.Static=2]="Static",_e),Me=((Le=Me||{})[Le.Unmount=0]="Unmount",Le[Le.Hidden=1]="Hidden",Le);function Ve({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:a=!0,name:l}){let i=Pe(t,e);if(a)return Se(i,n,r,l);let s=null!=o?o:0;if(2&s){let{static:e=!1,...t}=i;if(e)return Se(t,n,r,l)}if(1&s){let{unmount:e=!0,...t}=i;return be(e?0:1,{0(){return null},1(){return Se({...t,hidden:!0,style:{display:"none"}},n,r,l)}})}return Se(i,n,r,l)}function Se(e,t={},n,r){let{as:o=n,children:a,refName:l="ref",...i}=Ne(e,["unmount","static"]),s=void 0!==e.ref?{[l]:e.ref}:{},c="function"==typeof a?a(t):a;"className"in i&&i.className&&"function"==typeof i.className&&(i.className=i.className(t));let u={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r);e&&(u["data-headlessui-state"]=n.join(" "))}if(o===V.Fragment&&Object.keys(Te(i)).length>0){if(!(0,V.isValidElement)(c)||Array.isArray(c)&&c.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(i).map((e=>`  - ${e}`)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>`  - ${e}`)).join("\n")].join("\n"));let e=c.props,t="function"==typeof(null==e?void 0:e.className)?(...t)=>xe(null==e?void 0:e.className(...t),i.className):xe(null==e?void 0:e.className,i.className),n=t?{className:t}:{};return(0,V.cloneElement)(c,Object.assign({},Pe(c.props,Te(Ne(i,["ref"]))),u,s,function(...e){return{ref:e.every((e=>null==e))?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}}(c.ref,s.ref),n))}return(0,V.createElement)(o,Object.assign({},Ne(i,["ref"]),o!==V.Fragment&&s,o!==V.Fragment&&u),c)}function Pe(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map((e=>[e,void 0]))));for(let e in n)Object.assign(t,{[e](t,...r){let o=n[e];for(let e of o){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;e(t,...r)}}});return t}function ke(e){var t;return Object.assign((0,V.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function Te(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function Ne(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}var Ze=Object.defineProperty,Oe=(e,t,n)=>(((e,t,n)=>{t in e?Ze(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let Re=new class{constructor(){Oe(this,"current",this.detect()),Oe(this,"handoffState","pending"),Oe(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}},Ae=(e,t)=>{Re.isServer?(0,V.useEffect)(e,t):(0,V.useLayoutEffect)(e,t)};function Fe(e){let t=(0,V.useRef)(e);return Ae((()=>{t.current=e}),[e]),t}let je=function(e){let t=Fe(e);return V.useCallback(((...e)=>t.current(...e)),[t])},De=Symbol();function Ie(e,t=!0){return Object.assign(e,{[De]:t})}function ze(...e){let t=(0,V.useRef)(e);(0,V.useEffect)((()=>{t.current=e}),[e]);let n=je((e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)}));return e.every((e=>null==e||(null==e?void 0:e[De])))?void 0:n}function $e(){let e=function(){let e="undefined"==typeof document;return"useSyncExternalStore"in S&&S.useSyncExternalStore((()=>()=>{}),(()=>!1),(()=>!e))}(),[t,n]=V.useState(Re.isHandoffComplete);return t&&!1===Re.isHandoffComplete&&n(!1),V.useEffect((()=>{!0!==t&&n(!0)}),[t]),V.useEffect((()=>Re.handoff()),[]),!e&&t}var Be;let Ue=null!=(Be=V.useId)?Be:function(){let e=$e(),[t,n]=V.useState(e?()=>Re.nextId():null);return Ae((()=>{null===t&&n(Re.nextId())}),[t]),null!=t?""+t:void 0};var We=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(We||{});function qe(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=""===(null==t?void 0:t.getAttribute("disabled"));return(!r||!function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r}let Ge=(0,V.createContext)(null);Ge.displayName="OpenClosedContext";var Ke,Ye=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(Ye||{});function Je(){return(0,V.useContext)(Ge)}function Xe({value:e,children:t}){return V.createElement(Ge.Provider,{value:e},t)}function Qe(e){var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";return"string"==typeof n&&"button"===n.toLowerCase()?"button":void 0}function et(e,t){let[n,r]=(0,V.useState)((()=>Qe(e)));return Ae((()=>{r(Qe(e))}),[e.type,e.as]),Ae((()=>{n||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&r("button")}),[n,t]),n}function tt(e){return Re.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}let nt=null!=(Ke=V.startTransition)?Ke:function(e){e()};var rt,ot=((rt=ot||{})[rt.Open=0]="Open",rt[rt.Closed=1]="Closed",rt),at=(e=>(e[e.ToggleDisclosure=0]="ToggleDisclosure",e[e.CloseDisclosure=1]="CloseDisclosure",e[e.SetButtonId=2]="SetButtonId",e[e.SetPanelId=3]="SetPanelId",e[e.LinkPanel=4]="LinkPanel",e[e.UnlinkPanel=5]="UnlinkPanel",e))(at||{});let lt={0:e=>({...e,disclosureState:be(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4(e){return!0===e.linkedPanel?e:{...e,linkedPanel:!0}},5(e){return!1===e.linkedPanel?e:{...e,linkedPanel:!1}},2(e,t){return e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId}},3(e,t){return e.panelId===t.panelId?e:{...e,panelId:t.panelId}}},it=(0,V.createContext)(null);function st(e){let t=(0,V.useContext)(it);if(null===t){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,st),t}return t}it.displayName="DisclosureContext";let ct=(0,V.createContext)(null);function ut(e){let t=(0,V.useContext)(ct);if(null===t){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ut),t}return t}ct.displayName="DisclosureAPIContext";let dt=(0,V.createContext)(null);function ft(e,t){return be(t.type,lt,e,t)}dt.displayName="DisclosurePanelContext";let pt=V.Fragment,mt=He.RenderStrategy|He.Static,Ct=ke((function(e,t){let{defaultOpen:n=!1,...r}=e,o=(0,V.useRef)(null),a=ze(t,Ie((e=>{o.current=e}),void 0===e.as||e.as===V.Fragment)),l=(0,V.useRef)(null),i=(0,V.useRef)(null),s=(0,V.useReducer)(ft,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:i,panelRef:l,buttonId:null,panelId:null}),[{disclosureState:c,buttonId:u},d]=s,f=je((e=>{d({type:1});let t=tt(o);if(!t||!u)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(u):t.getElementById(u);null==n||n.focus()})),p=(0,V.useMemo)((()=>({close:f})),[f]),m=(0,V.useMemo)((()=>({open:0===c,close:f})),[c,f]),C={ref:a};return V.createElement(it.Provider,{value:s},V.createElement(ct.Provider,{value:p},V.createElement(Xe,{value:be(c,{0:Ye.Open,1:Ye.Closed})},Ve({ourProps:C,theirProps:r,slot:m,defaultTag:pt,name:"Disclosure"}))))})),ht=ke((function(e,t){let n=Ue(),{id:r=`headlessui-disclosure-button-${n}`,...o}=e,[a,l]=st("Disclosure.Button"),i=(0,V.useContext)(dt),s=null!==i&&i===a.panelId,c=(0,V.useRef)(null),u=ze(c,t,s?null:a.buttonRef);(0,V.useEffect)((()=>{if(!s)return l({type:2,buttonId:r}),()=>{l({type:2,buttonId:null})}}),[r,l,s]);let d=je((e=>{var t;if(s){if(1===a.disclosureState)return;switch(e.key){case We.Space:case We.Enter:e.preventDefault(),e.stopPropagation(),l({type:0}),null==(t=a.buttonRef.current)||t.focus()}}else switch(e.key){case We.Space:case We.Enter:e.preventDefault(),e.stopPropagation(),l({type:0})}})),f=je((e=>{e.key===We.Space&&e.preventDefault()})),p=je((t=>{var n;qe(t.currentTarget)||e.disabled||(s?(l({type:0}),null==(n=a.buttonRef.current)||n.focus()):l({type:0}))})),m=(0,V.useMemo)((()=>({open:0===a.disclosureState})),[a]),C=et(e,c);return Ve({ourProps:s?{ref:u,type:C,onKeyDown:d,onClick:p}:{ref:u,id:r,type:C,"aria-expanded":0===a.disclosureState,"aria-controls":a.linkedPanel?a.panelId:void 0,onKeyDown:d,onKeyUp:f,onClick:p},theirProps:o,slot:m,defaultTag:"button",name:"Disclosure.Button"})})),vt=ke((function(e,t){let n=Ue(),{id:r=`headlessui-disclosure-panel-${n}`,...o}=e,[a,l]=st("Disclosure.Panel"),{close:i}=ut("Disclosure.Panel"),s=ze(t,a.panelRef,(e=>{nt((()=>l({type:e?4:5})))}));(0,V.useEffect)((()=>(l({type:3,panelId:r}),()=>{l({type:3,panelId:null})})),[r,l]);let c=Je(),u=null!==c?(c&Ye.Open)===Ye.Open:0===a.disclosureState,d=(0,V.useMemo)((()=>({open:0===a.disclosureState,close:i})),[a,i]),f={ref:s,id:r};return V.createElement(dt.Provider,{value:a.panelId},Ve({ourProps:f,theirProps:o,slot:d,defaultTag:"div",features:mt,visible:u,name:"Disclosure.Panel"}))})),gt=Object.assign(Ct,{Button:ht,Panel:vt});const yt=e=>e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","),Et=e=>e.filter(Boolean).join(" ");var wt=n=>{const{iconLogo:r=!0,title:o="",children:a}=n;return(0,e.createElement)(gt,{as:"nav",className:"bg-white shadow"},(0,e.createElement)("div",{className:"max-w-3xl mx-auto px-6 lg:max-w-7xl"},(0,e.createElement)("div",{className:"relative flex justify-between h-16"},(0,e.createElement)("div",{className:"flex-1 flex items-center justify-center sm:items-stretch sm:justify-start"},r?(0,e.createElement)(ye,{to:{pathname:"admin.php",search:`?page=${zip_ai_react.page_slug}`},className:"flex-shrink-0 flex items-center justify-start focus:outline-none focus-visible:outline-none focus:shadow-none active:outline-none active:shadow-none transition-colors"},(0,e.createElement)("div",{className:"flex-shrink-0 flex items-center"},p({className:Et(["h-10 text-zip",o&&"mr-6"])}),o&&(0,e.createElement)("h1",{className:"h-6 m-0 text-xl leading-6 font-semibold text-slate-800"},o))):null,a&&(0,e.createElement)("div",{className:"sm:ml-8 sm:flex sm:space-x-8"},a)),(0,e.createElement)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-2 gap-8 sm:static sm:inset-auto sm:ml-6 sm:pr-0"},(0,e.createElement)("a",{className:"text-base text-slate-500 focus:text-spec focus-visible:text-spec active:text-spec hover:text-spec transition-colors",href:"https://wpspectra.com/zip-ai/",target:"_blank",rel:"noreferrer"},(0,t.__)("Visit Website","zip-ai"))))))},bt=window.wp.components;let xt=(0,V.createContext)(null);function Lt(){let e=(0,V.useContext)(xt);if(null===e){let e=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,Lt),e}return e}let _t=ke((function(e,t){let n=Ue(),{id:r=`headlessui-label-${n}`,passive:o=!1,...a}=e,l=Lt(),i=ze(t);Ae((()=>l.register(r)),[r,l.register]);let s={ref:i,...l.props,id:r};return o&&("onClick"in s&&(delete s.htmlFor,delete s.onClick),"onClick"in a&&delete a.onClick),Ve({ourProps:s,theirProps:a,slot:l.slot||{},defaultTag:"label",name:l.name||"Label"})})),Ht=Object.assign(_t,{}),Mt=(0,V.createContext)(null);function Vt(){let e=(0,V.useContext)(Mt);if(null===e){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,Vt),e}return e}function St(){let[e,t]=(0,V.useState)([]);return[e.length>0?e.join(" "):void 0,(0,V.useMemo)((()=>function(e){let n=je((e=>(t((t=>[...t,e])),()=>t((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),r=(0,V.useMemo)((()=>({register:n,slot:e.slot,name:e.name,props:e.props})),[n,e.slot,e.name,e.props]);return V.createElement(Mt.Provider,{value:r},e.children)}),[t])]}let Pt=ke((function(e,t){let n=Ue(),{id:r=`headlessui-description-${n}`,...o}=e,a=Vt(),l=ze(t);return Ae((()=>a.register(r)),[r,a.register]),Ve({ourProps:{ref:l,...a.props,id:r},theirProps:o,slot:a.slot||{},defaultTag:"p",name:a.name||"Description"})})),kt=Object.assign(Pt,{});var Tt=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(Tt||{});let Nt=ke((function(e,t){let{features:n=1,...r}=e;return Ve({ourProps:{ref:t,"aria-hidden":2==(2&n)||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...4==(4&n)&&2!=(2&n)&&{display:"none"}}},theirProps:r,slot:{},defaultTag:"div",name:"Hidden"})}));function Zt(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}function Ot(){let e=[],t={addEventListener(e,n,r,o){return e.addEventListener(n,r,o),t.add((()=>e.removeEventListener(n,r,o)))},requestAnimationFrame(...e){let n=requestAnimationFrame(...e);return t.add((()=>cancelAnimationFrame(n)))},nextFrame(...e){return t.requestAnimationFrame((()=>t.requestAnimationFrame(...e)))},setTimeout(...e){let n=setTimeout(...e);return t.add((()=>clearTimeout(n)))},microTask(...e){let n={current:!0};return Zt((()=>{n.current&&e[0]()})),t.add((()=>{n.current=!1}))},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add((()=>{Object.assign(e.style,{[t]:r})}))},group(e){let t=Ot();return e(t),this.add((()=>t.dispose()))},add(t){return e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}},dispose(){for(let t of e.splice(0))t()}};return t}function Rt(){let[e]=(0,V.useState)(Ot);return(0,V.useEffect)((()=>()=>e.dispose()),[e]),e}let At=(0,V.createContext)(null);At.displayName="GroupContext";let Ft=V.Fragment,jt=ke((function(e,t){let n=Ue(),{id:r=`headlessui-switch-${n}`,checked:o,defaultChecked:a=!1,onChange:l,name:i,value:s,form:c,...u}=e,d=(0,V.useContext)(At),f=(0,V.useRef)(null),p=ze(f,t,null===d?null:d.setSwitch),[m,C]=function(e,t,n){let[r,o]=(0,V.useState)(n),a=void 0!==e,l=(0,V.useRef)(a),i=(0,V.useRef)(!1),s=(0,V.useRef)(!1);return!a||l.current||i.current?!a&&l.current&&!s.current&&(s.current=!0,l.current=a,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")):(i.current=!0,l.current=a,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")),[a?e:r,je((e=>(a||o(e),null==t?void 0:t(e))))]}(o,l,a),h=je((()=>null==C?void 0:C(!m))),v=je((e=>{if(qe(e.currentTarget))return e.preventDefault();e.preventDefault(),h()})),g=je((e=>{e.key===We.Space?(e.preventDefault(),h()):e.key===We.Enter&&function(e){var t,n;let r=null!=(t=null==e?void 0:e.form)?t:e.closest("form");if(r){for(let t of r.elements)if(t!==e&&("INPUT"===t.tagName&&"submit"===t.type||"BUTTON"===t.tagName&&"submit"===t.type||"INPUT"===t.nodeName&&"image"===t.type))return void t.click();null==(n=r.requestSubmit)||n.call(r)}}(e.currentTarget)})),y=je((e=>e.preventDefault())),E=(0,V.useMemo)((()=>({checked:m})),[m]),w={id:r,ref:p,role:"switch",type:et(e,f),tabIndex:0,"aria-checked":m,"aria-labelledby":null==d?void 0:d.labelledby,"aria-describedby":null==d?void 0:d.describedby,onClick:v,onKeyUp:g,onKeyPress:y},b=Rt();return(0,V.useEffect)((()=>{var e;let t=null==(e=f.current)?void 0:e.closest("form");t&&void 0!==a&&b.addEventListener(t,"reset",(()=>{C(a)}))}),[f,C]),V.createElement(V.Fragment,null,null!=i&&m&&V.createElement(Nt,{features:Tt.Hidden,...Te({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:c,checked:m,name:i,value:s})}),Ve({ourProps:w,theirProps:u,slot:E,defaultTag:"button",name:"Switch"}))})),Dt=Object.assign(jt,{Group:function(e){var t;let[n,r]=(0,V.useState)(null),[o,a]=function(){let[e,t]=(0,V.useState)([]);return[e.length>0?e.join(" "):void 0,(0,V.useMemo)((()=>function(e){let n=je((e=>(t((t=>[...t,e])),()=>t((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),r=(0,V.useMemo)((()=>({register:n,slot:e.slot,name:e.name,props:e.props})),[n,e.slot,e.name,e.props]);return V.createElement(xt.Provider,{value:r},e.children)}),[t])]}(),[l,i]=St(),s=(0,V.useMemo)((()=>({switch:n,setSwitch:r,labelledby:o,describedby:l})),[n,r,o,l]),c=e;return V.createElement(i,{name:"Switch.Description"},V.createElement(a,{name:"Switch.Label",props:{htmlFor:null==(t=s.switch)?void 0:t.id,onClick(e){n&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},V.createElement(At.Provider,{value:s},Ve({ourProps:{},theirProps:c,defaultTag:Ft,name:"Switch.Group"}))))},Label:Ht,Description:kt});function It(){let e=(0,V.useRef)(!1);return Ae((()=>(e.current=!0,()=>{e.current=!1})),[]),e}function zt(e,...t){e&&t.length>0&&e.classList.add(...t)}function $t(e,...t){e&&t.length>0&&e.classList.remove(...t)}function Bt(e=""){return e.split(" ").filter((e=>e.trim().length>1))}let Ut=(0,V.createContext)(null);Ut.displayName="TransitionContext";var Wt=(e=>(e.Visible="visible",e.Hidden="hidden",e))(Wt||{});let qt=(0,V.createContext)(null);function Gt(e){return"children"in e?Gt(e.children):e.current.filter((({el:e})=>null!==e.current)).filter((({state:e})=>"visible"===e)).length>0}function Kt(e,t){let n=Fe(e),r=(0,V.useRef)([]),o=It(),a=Rt(),l=je(((e,t=Me.Hidden)=>{let l=r.current.findIndex((({el:t})=>t===e));-1!==l&&(be(t,{[Me.Unmount](){r.current.splice(l,1)},[Me.Hidden](){r.current[l].state="hidden"}}),a.microTask((()=>{var e;!Gt(r)&&o.current&&(null==(e=n.current)||e.call(n))})))})),i=je((e=>{let t=r.current.find((({el:t})=>t===e));return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>l(e,Me.Unmount)})),s=(0,V.useRef)([]),c=(0,V.useRef)(Promise.resolve()),u=(0,V.useRef)({enter:[],leave:[],idle:[]}),d=je(((e,n,r)=>{s.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter((([t])=>t!==e))),null==t||t.chains.current[n].push([e,new Promise((e=>{s.current.push(e)}))]),null==t||t.chains.current[n].push([e,new Promise((e=>{Promise.all(u.current[n].map((([e,t])=>t))).then((()=>e()))}))]),"enter"===n?c.current=c.current.then((()=>null==t?void 0:t.wait.current)).then((()=>r(n))):r(n)})),f=je(((e,t,n)=>{Promise.all(u.current[t].splice(0).map((([e,t])=>t))).then((()=>{var e;null==(e=s.current.shift())||e()})).then((()=>n(t)))}));return(0,V.useMemo)((()=>({children:r,register:i,unregister:l,onStart:d,onStop:f,wait:c,chains:u})),[i,l,r,d,f,u,c])}function Yt(){}qt.displayName="NestingContext";let Jt=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function Xt(e){var t;let n={};for(let r of Jt)n[r]=null!=(t=e[r])?t:Yt;return n}let Qt=He.RenderStrategy,en=ke((function(e,t){let{show:n,appear:r=!1,unmount:o=!0,...a}=e,l=(0,V.useRef)(null),i=ze(l,t);$e();let s=Je();if(void 0===n&&null!==s&&(n=(s&Ye.Open)===Ye.Open),![!0,!1].includes(n))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[c,u]=(0,V.useState)(n?"visible":"hidden"),d=Kt((()=>{u("hidden")})),[f,p]=(0,V.useState)(!0),m=(0,V.useRef)([n]);Ae((()=>{!1!==f&&m.current[m.current.length-1]!==n&&(m.current.push(n),p(!1))}),[m,n]);let C=(0,V.useMemo)((()=>({show:n,appear:r,initial:f})),[n,r,f]);(0,V.useEffect)((()=>{if(n)u("visible");else if(Gt(d)){let e=l.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&u("hidden")}else u("hidden")}),[n,d]);let h={unmount:o},v=je((()=>{var t;f&&p(!1),null==(t=e.beforeEnter)||t.call(e)})),g=je((()=>{var t;f&&p(!1),null==(t=e.beforeLeave)||t.call(e)}));return V.createElement(qt.Provider,{value:d},V.createElement(Ut.Provider,{value:C},Ve({ourProps:{...h,as:V.Fragment,children:V.createElement(tn,{ref:i,...h,...a,beforeEnter:v,beforeLeave:g})},theirProps:{},defaultTag:V.Fragment,features:Qt,visible:"visible"===c,name:"Transition"})))})),tn=ke((function(e,t){var n,r;let{beforeEnter:o,afterEnter:a,beforeLeave:l,afterLeave:i,enter:s,enterFrom:c,enterTo:u,entered:d,leave:f,leaveFrom:p,leaveTo:m,...C}=e,h=(0,V.useRef)(null),v=ze(h,t),g=null==(n=C.unmount)||n?Me.Unmount:Me.Hidden,{show:y,appear:E,initial:w}=function(){let e=(0,V.useContext)(Ut);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[b,x]=(0,V.useState)(y?"visible":"hidden"),L=function(){let e=(0,V.useContext)(qt);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:_,unregister:H}=L;(0,V.useEffect)((()=>_(h)),[_,h]),(0,V.useEffect)((()=>{if(g===Me.Hidden&&h.current)return y&&"visible"!==b?void x("visible"):be(b,{hidden:()=>H(h),visible:()=>_(h)})}),[b,h,_,H,y,g]);let M=Fe({base:Bt(C.className),enter:Bt(s),enterFrom:Bt(c),enterTo:Bt(u),entered:Bt(d),leave:Bt(f),leaveFrom:Bt(p),leaveTo:Bt(m)}),S=function(e){let t=(0,V.useRef)(Xt(e));return(0,V.useEffect)((()=>{t.current=Xt(e)}),[e]),t}({beforeEnter:o,afterEnter:a,beforeLeave:l,afterLeave:i}),P=$e();(0,V.useEffect)((()=>{if(P&&"visible"===b&&null===h.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}),[h,b,P]);let k=E&&y&&w,T=!P||w&&!E?"idle":y?"enter":"leave",N=function(e=0){let[t,n]=(0,V.useState)(e),r=It(),o=(0,V.useCallback)((e=>{r.current&&n((t=>t|e))}),[t,r]),a=(0,V.useCallback)((e=>Boolean(t&e)),[t]),l=(0,V.useCallback)((e=>{r.current&&n((t=>t&~e))}),[n,r]),i=(0,V.useCallback)((e=>{r.current&&n((t=>t^e))}),[n]);return{flags:t,addFlag:o,hasFlag:a,removeFlag:l,toggleFlag:i}}(0),Z=je((e=>be(e,{enter:()=>{N.addFlag(Ye.Opening),S.current.beforeEnter()},leave:()=>{N.addFlag(Ye.Closing),S.current.beforeLeave()},idle:()=>{}}))),O=je((e=>be(e,{enter:()=>{N.removeFlag(Ye.Opening),S.current.afterEnter()},leave:()=>{N.removeFlag(Ye.Closing),S.current.afterLeave()},idle:()=>{}}))),R=Kt((()=>{x("hidden"),H(h)}),L);!function({immediate:e,container:t,direction:n,classes:r,onStart:o,onStop:a}){let l=It(),i=Rt(),s=Fe(n);Ae((()=>{e&&(s.current="enter")}),[e]),Ae((()=>{let e=Ot();i.add(e.dispose);let n=t.current;if(n&&"idle"!==s.current&&l.current)return e.dispose(),o.current(s.current),e.add(function(e,t,n,r){let o=n?"enter":"leave",a=Ot(),l=void 0!==r?function(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}(r):()=>{};"enter"===o&&(e.removeAttribute("hidden"),e.style.display="");let i=be(o,{enter:()=>t.enter,leave:()=>t.leave}),s=be(o,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),c=be(o,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return $t(e,...t.base,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),zt(e,...t.base,...i,...c),a.nextFrame((()=>{$t(e,...t.base,...i,...c),zt(e,...t.base,...i,...s),function(e,t){let n=Ot();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:o}=getComputedStyle(e),[a,l]=[r,o].map((e=>{let[t=0]=e.split(",").filter(Boolean).map((e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e))).sort(((e,t)=>t-e));return t})),i=a+l;if(0!==i){n.group((n=>{n.setTimeout((()=>{t(),n.dispose()}),i),n.addEventListener(e,"transitionrun",(e=>{e.target===e.currentTarget&&n.dispose()}))}));let r=n.addEventListener(e,"transitionend",(e=>{e.target===e.currentTarget&&(t(),r())}))}else t();n.add((()=>t())),n.dispose}(e,(()=>($t(e,...t.base,...i),zt(e,...t.base,...t.entered),l())))})),a.dispose}(n,r.current,"enter"===s.current,(()=>{e.dispose(),a.current(s.current)}))),e.dispose}),[n])}({immediate:k,container:h,classes:M,direction:T,onStart:Fe((e=>{R.onStart(h,e,Z)})),onStop:Fe((e=>{R.onStop(h,e,O),"leave"===e&&!Gt(R)&&(x("hidden"),H(h))}))});let A=C,F={ref:v};return k?A={...A,className:xe(C.className,...M.current.enter,...M.current.enterFrom)}:(A.className=xe(C.className,null==(r=h.current)?void 0:r.className),""===A.className&&delete A.className),V.createElement(qt.Provider,{value:R},V.createElement(Xe,{value:be(b,{visible:Ye.Open,hidden:Ye.Closed})|N.flags},Ve({ourProps:F,theirProps:A,defaultTag:"div",features:Qt,visible:"visible"===b,name:"Transition.Child"})))})),nn=ke((function(e,t){let n=null!==(0,V.useContext)(Ut),r=null!==Je();return V.createElement(V.Fragment,null,!n&&r?V.createElement(en,{ref:t,...e}):V.createElement(tn,{ref:t,...e}))})),rn=Object.assign(en,{Child:nn,Root:en}),on=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>`${e}:not([tabindex='-1'])`)).join(",");var an=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(an||{}),ln=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(ln||{}),sn=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(sn||{});var cn=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(cn||{});var un=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(un||{});function dn(e){null==e||e.focus({preventScroll:!0})}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",(e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")}),!0),document.addEventListener("click",(e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")}),!0));let fn=["textarea","input"].join(",");function pn(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){let a=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,l=Array.isArray(e)?n?function(e,t=(e=>e)){return e.slice().sort(((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let a=r.compareDocumentPosition(o);return a&Node.DOCUMENT_POSITION_FOLLOWING?-1:a&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}(e):e:function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(on)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}(e);o.length>0&&l.length>1&&(l=l.filter((e=>!o.includes(e)))),r=null!=r?r:a.activeElement;let i,s=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,l.indexOf(r))-1;if(4&t)return Math.max(0,l.indexOf(r))+1;if(8&t)return l.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=32&t?{preventScroll:!0}:{},d=0,f=l.length;do{if(d>=f||d+f<=0)return 0;let e=c+d;if(16&t)e=(e+f)%f;else{if(e<0)return 3;if(e>=f)return 1}i=l[e],null==i||i.focus(u),d+=s}while(i!==a.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,fn))&&n}(i)&&i.select(),2}function mn(e,t,n){let r=Fe(t);(0,V.useEffect)((()=>{function t(e){r.current(e)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)}),[e,n])}var Cn=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(Cn||{});function hn(...e){return(0,V.useMemo)((()=>tt(...e)),[...e])}function vn(e,t,n,r){let o=Fe(n);(0,V.useEffect)((()=>{function n(e){o.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)}),[e,t,r])}function gn(e,t){let n=(0,V.useRef)([]),r=je(e);(0,V.useEffect)((()=>{let e=[...n.current];for(let[o,a]of t.entries())if(n.current[o]!==a){let o=r(t,e);return n.current=t,o}}),[r,...t])}function yn(e){let t=je(e),n=(0,V.useRef)(!1);(0,V.useEffect)((()=>(n.current=!1,()=>{n.current=!0,Zt((()=>{n.current&&t()}))})),[t])}function En(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}var wn=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(wn||{});let bn=ke((function(e,t){let n=(0,V.useRef)(null),r=ze(n,t),{initialFocus:o,containers:a,features:l=30,...i}=e;$e()||(l=1);let s=hn(n);!function({ownerDocument:e},t){let n=function(e=!0){let t=(0,V.useRef)(Ln.slice());return gn((([e],[n])=>{!0===n&&!1===e&&Zt((()=>{t.current.splice(0)})),!1===n&&!0===e&&(t.current=Ln.slice())}),[e,Ln,t]),je((()=>{var e;return null!=(e=t.current.find((e=>null!=e&&e.isConnected)))?e:null}))}(t);gn((()=>{t||(null==e?void 0:e.activeElement)===(null==e?void 0:e.body)&&dn(n())}),[t]),yn((()=>{t&&dn(n())}))}({ownerDocument:s},Boolean(16&l));let c=function({ownerDocument:e,container:t,initialFocus:n},r){let o=(0,V.useRef)(null),a=It();return gn((()=>{if(!r)return;let l=t.current;l&&Zt((()=>{if(!a.current)return;let t=null==e?void 0:e.activeElement;if(null!=n&&n.current){if((null==n?void 0:n.current)===t)return void(o.current=t)}else if(l.contains(t))return void(o.current=t);null!=n&&n.current?dn(n.current):pn(l,an.First)===ln.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.current=null==e?void 0:e.activeElement}))}),[r]),o}({ownerDocument:s,container:n,initialFocus:o},Boolean(2&l));!function({ownerDocument:e,container:t,containers:n,previousActiveElement:r},o){let a=It();vn(null==e?void 0:e.defaultView,"focus",(e=>{if(!o||!a.current)return;let l=En(n);t.current instanceof HTMLElement&&l.add(t.current);let i=r.current;if(!i)return;let s=e.target;s&&s instanceof HTMLElement?Hn(l,s)?(r.current=s,dn(s)):(e.preventDefault(),e.stopPropagation(),dn(i)):dn(r.current)}),!0)}({ownerDocument:s,container:n,containers:a,previousActiveElement:c},Boolean(8&l));let u=function(){let e=(0,V.useRef)(0);return mn("keydown",(t=>{"Tab"===t.key&&(e.current=t.shiftKey?1:0)}),!0),e}(),d=je((e=>{let t=n.current;t&&be(u.current,{[Cn.Forwards]:()=>{pn(t,an.First,{skipElements:[e.relatedTarget]})},[Cn.Backwards]:()=>{pn(t,an.Last,{skipElements:[e.relatedTarget]})}})})),f=Rt(),p=(0,V.useRef)(!1),m={ref:r,onKeyDown(e){"Tab"==e.key&&(p.current=!0,f.requestAnimationFrame((()=>{p.current=!1})))},onBlur(e){let t=En(a);n.current instanceof HTMLElement&&t.add(n.current);let r=e.relatedTarget;r instanceof HTMLElement&&"true"!==r.dataset.headlessuiFocusGuard&&(Hn(t,r)||(p.current?pn(n.current,be(u.current,{[Cn.Forwards]:()=>an.Next,[Cn.Backwards]:()=>an.Previous})|an.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&dn(e.target)))}};return V.createElement(V.Fragment,null,Boolean(4&l)&&V.createElement(Nt,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:Tt.Focusable}),Ve({ourProps:m,theirProps:i,defaultTag:"div",name:"FocusTrap"}),Boolean(4&l)&&V.createElement(Nt,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:Tt.Focusable}))})),xn=Object.assign(bn,{features:wn}),Ln=[];function Hn(e,t){for(let n of e)if(n.contains(t))return!0;return!1}!function(e){function t(){"loading"!==document.readyState&&((()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&Ln[0]!==e.target&&(Ln.unshift(e.target),Ln=Ln.filter((e=>null!=e&&e.isConnected)),Ln.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})})(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}();var Mn=window.ReactDOM;let Vn=(0,V.createContext)(!1);function Sn(e){return V.createElement(Vn.Provider,{value:e.force},e.children)}let Pn=V.Fragment,kn=V.Fragment,Tn=(0,V.createContext)(null),Nn=(0,V.createContext)(null),Zn=ke((function(e,t){let n=e,r=(0,V.useRef)(null),o=ze(Ie((e=>{r.current=e})),t),a=hn(r),l=function(e){let t=(0,V.useContext)(Vn),n=(0,V.useContext)(Tn),r=hn(e),[o,a]=(0,V.useState)((()=>{if(!t&&null!==n||Re.isServer)return null;let e=null==r?void 0:r.getElementById("headlessui-portal-root");if(e)return e;if(null===r)return null;let o=r.createElement("div");return o.setAttribute("id","headlessui-portal-root"),r.body.appendChild(o)}));return(0,V.useEffect)((()=>{null!==o&&(null!=r&&r.body.contains(o)||null==r||r.body.appendChild(o))}),[o,r]),(0,V.useEffect)((()=>{t||null!==n&&a(n.current)}),[n,a,t]),o}(r),[i]=(0,V.useState)((()=>{var e;return Re.isServer?null:null!=(e=null==a?void 0:a.createElement("div"))?e:null})),s=(0,V.useContext)(Nn),c=$e();return Ae((()=>{!l||!i||l.contains(i)||(i.setAttribute("data-headlessui-portal",""),l.appendChild(i))}),[l,i]),Ae((()=>{if(i&&s)return s.register(i)}),[s,i]),yn((()=>{var e;!l||!i||(i instanceof Node&&l.contains(i)&&l.removeChild(i),l.childNodes.length<=0&&(null==(e=l.parentElement)||e.removeChild(l)))})),c&&l&&i?(0,Mn.createPortal)(Ve({ourProps:{ref:o},theirProps:n,defaultTag:Pn,name:"Portal"}),i):null})),On=ke((function(e,t){let{target:n,...r}=e,o={ref:ze(t)};return V.createElement(Tn.Provider,{value:n},Ve({ourProps:o,theirProps:r,defaultTag:kn,name:"Popover.Group"}))})),Rn=Object.assign(Zn,{Group:On}),An=(0,V.createContext)((()=>{}));An.displayName="StackContext";var Fn=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(Fn||{});function jn({children:e,onUpdate:t,type:n,element:r,enabled:o}){let a=(0,V.useContext)(An),l=je(((...e)=>{null==t||t(...e),a(...e)}));return Ae((()=>{let e=void 0===o||!0===o;return e&&l(0,n,r),()=>{e&&l(1,n,r)}}),[l,n,r,o]),V.createElement(An.Provider,{value:l},e)}function Dn(e,t,n){let r=Fe(t);(0,V.useEffect)((()=>{function t(e){r.current(e)}return document.addEventListener(e,t,n),()=>document.removeEventListener(e,t,n)}),[e,n])}function In(e,t,n=!0){let r=(0,V.useRef)(!1);function o(n,o){if(!r.current||n.defaultPrevented)return;let a=o(n);if(null===a||!a.getRootNode().contains(a)||!a.isConnected)return;let l=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e);for(let e of l){if(null===e)continue;let t=e instanceof HTMLElement?e:e.current;if(null!=t&&t.contains(a)||n.composed&&n.composedPath().includes(t))return}return!function(e,t=0){var n;return e!==(null==(n=tt(e))?void 0:n.body)&&be(t,{0(){return e.matches(on)},1(){let t=e;for(;null!==t;){if(t.matches(on))return!0;t=t.parentElement}return!1}})}(a,cn.Loose)&&-1!==a.tabIndex&&n.preventDefault(),t(n,a)}(0,V.useEffect)((()=>{requestAnimationFrame((()=>{r.current=n}))}),[n]);let a=(0,V.useRef)(null);Dn("pointerdown",(e=>{var t,n;r.current&&(a.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),!0),Dn("mousedown",(e=>{var t,n;r.current&&(a.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),!0),Dn("click",(e=>{a.current&&(o(e,(()=>a.current)),a.current=null)}),!0),Dn("touchend",(e=>o(e,(()=>e.target instanceof HTMLElement?e.target:null))),!0),mn("blur",(e=>o(e,(()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null))),!0)}const zn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},{useState:$n,useEffect:Bn,useLayoutEffect:Un,useDebugValue:Wn}=S;function qn(e){const t=e.getSnapshot,n=e.value;try{const e=t();return!zn(n,e)}catch{return!0}}const Gn="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t,n){return t()}:function(e,t,n){const r=t(),[{inst:o},a]=$n({inst:{value:r,getSnapshot:t}});return Un((()=>{o.value=r,o.getSnapshot=t,qn(o)&&a({inst:o})}),[e,r,t]),Bn((()=>(qn(o)&&a({inst:o}),e((()=>{qn(o)&&a({inst:o})})))),[e]),Wn(r),r},Kn="useSyncExternalStore"in S?(e=>e.useSyncExternalStore)(S):Gn;function Yn(){let e;return{before({doc:t}){var n;let r=t.documentElement;e=(null!=(n=t.defaultView)?n:window).innerWidth-r.clientWidth},after({doc:t,d:n}){let r=t.documentElement,o=r.clientWidth-r.offsetWidth,a=e-o;n.style(r,"paddingRight",`${a}px`)}}}function Jn(){if(!(/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0))return{};let e;return{before(){e=window.pageYOffset},after({doc:t,d:n,meta:r}){function o(e){return r.containers.flatMap((e=>e())).some((t=>t.contains(e)))}n.microTask((()=>{if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=Ot();e.style(t.documentElement,"scroll-behavior","auto"),n.add((()=>n.microTask((()=>e.dispose()))))}n.style(t.body,"marginTop",`-${e}px`),window.scrollTo(0,0);let r=null;n.addEventListener(t,"click",(e=>{if(e.target instanceof HTMLElement)try{let n=e.target.closest("a");if(!n)return;let{hash:a}=new URL(n.href),l=t.querySelector(a);l&&!o(l)&&(r=l)}catch{}}),!0),n.addEventListener(t,"touchmove",(e=>{e.target instanceof HTMLElement&&!o(e.target)&&e.preventDefault()}),{passive:!1}),n.add((()=>{window.scrollTo(0,window.pageYOffset+e),r&&r.isConnected&&(r.scrollIntoView({block:"nearest"}),r=null)}))}))}}}function Xn(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let Qn=function(e,t){let n=new Map,r=new Set;return{getSnapshot(){return n},subscribe(e){return r.add(e),()=>r.delete(e)},dispatch(e,...o){let a=t[e].call(n,...o);a&&(n=a,r.forEach((e=>e())))}}}(0,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:Ot(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:Xn(n)},o=[Jn(),Yn(),{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];o.forEach((({before:e})=>null==e?void 0:e(r))),o.forEach((({after:e})=>null==e?void 0:e(r)))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});Qn.subscribe((()=>{let e=Qn.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&Qn.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&Qn.dispatch("TEARDOWN",n)}}));let er=new Map,tr=new Map;function nr(e,t=!0){Ae((()=>{var n;if(!t)return;let r="function"==typeof e?e():e.current;if(!r)return;let o=null!=(n=tr.get(r))?n:0;return tr.set(r,o+1),0!==o||(er.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),function(){var e;if(!r)return;let t=null!=(e=tr.get(r))?e:1;if(1===t?tr.delete(r):tr.set(r,t-1),1!==t)return;let n=er.get(r);n&&(null===n["aria-hidden"]?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",n["aria-hidden"]),r.inert=n.inert,er.delete(r))}}),[e,t])}var rr=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(rr||{}),or=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(or||{});let ar={0(e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},lr=(0,V.createContext)(null);function ir(e){let t=(0,V.useContext)(lr);if(null===t){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ir),t}return t}function sr(e,t){return be(t.type,ar,e,t)}lr.displayName="DialogContext";let cr=He.RenderStrategy|He.Static,ur=ke((function(e,t){var n;let r=Ue(),{id:o=`headlessui-dialog-${r}`,open:a,onClose:l,initialFocus:i,__demoMode:s=!1,...c}=e,[u,d]=(0,V.useState)(0),f=Je();void 0===a&&null!==f&&(a=(f&Ye.Open)===Ye.Open);let p=(0,V.useRef)(null),m=ze(p,t),C=hn(p),h=e.hasOwnProperty("open")||null!==f,v=e.hasOwnProperty("onClose");if(!h&&!v)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!h)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!v)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof a)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${a}`);if("function"!=typeof l)throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${l}`);let g=a?0:1,[y,E]=(0,V.useReducer)(sr,{titleId:null,descriptionId:null,panelRef:(0,V.createRef)()}),w=je((()=>l(!1))),b=je((e=>E({type:0,id:e}))),x=!!$e()&&!s&&0===g,L=u>1,_=null!==(0,V.useContext)(lr),[H,M]=function(){let e=(0,V.useContext)(Nn),t=(0,V.useRef)([]),n=je((n=>(t.current.push(n),e&&e.register(n),()=>r(n)))),r=je((n=>{let r=t.current.indexOf(n);-1!==r&&t.current.splice(r,1),e&&e.unregister(n)})),o=(0,V.useMemo)((()=>({register:n,unregister:r,portals:t})),[n,r,t]);return[t,(0,V.useMemo)((()=>function({children:e}){return V.createElement(Nn.Provider,{value:o},e)}),[o])]}(),{resolveContainers:S,mainTreeNodeRef:P,MainTreeNode:k}=function({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){var r;let o=(0,V.useRef)(null!=(r=null==n?void 0:n.current)?r:null),a=hn(o),l=je((()=>{var n;let r=[];for(let t of e)null!==t&&(t instanceof HTMLElement?r.push(t):"current"in t&&t.current instanceof HTMLElement&&r.push(t.current));if(null!=t&&t.current)for(let e of t.current)r.push(e);for(let e of null!=(n=null==a?void 0:a.querySelectorAll("html > *, body > *"))?n:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(o.current)||r.some((t=>e.contains(t)))||r.push(e));return r}));return{resolveContainers:l,contains:je((e=>l().some((t=>t.contains(e))))),mainTreeNodeRef:o,MainTreeNode:(0,V.useMemo)((()=>function(){return null!=n?null:V.createElement(Nt,{features:Tt.Hidden,ref:o})}),[o,n])}}({portals:H,defaultContainers:[null!=(n=y.panelRef.current)?n:p.current]}),T=L?"parent":"leaf",N=null!==f&&(f&Ye.Closing)===Ye.Closing,Z=!_&&!N&&x,O=(0,V.useCallback)((()=>{var e,t;return null!=(t=Array.from(null!=(e=null==C?void 0:C.querySelectorAll("body > *"))?e:[]).find((e=>"headlessui-portal-root"!==e.id&&e.contains(P.current)&&e instanceof HTMLElement)))?t:null}),[P]);nr(O,Z);let R=!!L||x,A=(0,V.useCallback)((()=>{var e,t;return null!=(t=Array.from(null!=(e=null==C?void 0:C.querySelectorAll("[data-headlessui-portal]"))?e:[]).find((e=>e.contains(P.current)&&e instanceof HTMLElement)))?t:null}),[P]);nr(A,R),In(S,w,!(!x||L));let F=!(L||0!==g);vn(null==C?void 0:C.defaultView,"keydown",(e=>{F&&(e.defaultPrevented||e.key===We.Escape&&(e.preventDefault(),e.stopPropagation(),w()))})),function(e,t,n=(()=>[document.body])){!function(e,t,n){let r=function(e){return Kn(e.subscribe,e.getSnapshot,e.getSnapshot)}(Qn),o=e?r.get(e):void 0,a=!!o&&o.count>0;Ae((()=>{if(e&&t)return Qn.dispatch("PUSH",e,n),()=>Qn.dispatch("POP",e,n)}),[t,e])}(e,t,(e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}}))}(C,!(N||0!==g||_),S),(0,V.useEffect)((()=>{if(0!==g||!p.current)return;let e=new ResizeObserver((e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&w()}}));return e.observe(p.current),()=>e.disconnect()}),[g,p,w]);let[j,D]=St(),I=(0,V.useMemo)((()=>[{dialogState:g,close:w,setTitleId:b},y]),[g,y,w,b]),z=(0,V.useMemo)((()=>({open:0===g})),[g]),$={ref:m,id:o,role:"dialog","aria-modal":0===g||void 0,"aria-labelledby":y.titleId,"aria-describedby":j};return V.createElement(jn,{type:"Dialog",enabled:0===g,element:p,onUpdate:je(((e,t)=>{"Dialog"===t&&be(e,{[Fn.Add]:()=>d((e=>e+1)),[Fn.Remove]:()=>d((e=>e-1))})}))},V.createElement(Sn,{force:!0},V.createElement(Rn,null,V.createElement(lr.Provider,{value:I},V.createElement(Rn.Group,{target:p},V.createElement(Sn,{force:!1},V.createElement(D,{slot:z,name:"Dialog.Description"},V.createElement(xn,{initialFocus:i,containers:S,features:x?be(T,{parent:xn.features.RestoreFocus,leaf:xn.features.All&~xn.features.FocusLock}):xn.features.None},V.createElement(M,null,Ve({ourProps:$,theirProps:c,slot:z,defaultTag:"div",features:cr,visible:0===g,name:"Dialog"}))))))))),V.createElement(k,null))})),dr=ke((function(e,t){let n=Ue(),{id:r=`headlessui-dialog-backdrop-${n}`,...o}=e,[{dialogState:a},l]=ir("Dialog.Backdrop"),i=ze(t);(0,V.useEffect)((()=>{if(null===l.panelRef.current)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")}),[l.panelRef]);let s=(0,V.useMemo)((()=>({open:0===a})),[a]);return V.createElement(Sn,{force:!0},V.createElement(Rn,null,Ve({ourProps:{ref:i,id:r,"aria-hidden":!0},theirProps:o,slot:s,defaultTag:"div",name:"Dialog.Backdrop"})))})),fr=ke((function(e,t){let n=Ue(),{id:r=`headlessui-dialog-panel-${n}`,...o}=e,[{dialogState:a},l]=ir("Dialog.Panel"),i=ze(t,l.panelRef),s=(0,V.useMemo)((()=>({open:0===a})),[a]);return Ve({ourProps:{ref:i,id:r,onClick:je((e=>{e.stopPropagation()}))},theirProps:o,slot:s,defaultTag:"div",name:"Dialog.Panel"})})),pr=ke((function(e,t){let n=Ue(),{id:r=`headlessui-dialog-overlay-${n}`,...o}=e,[{dialogState:a,close:l}]=ir("Dialog.Overlay");return Ve({ourProps:{ref:ze(t),id:r,"aria-hidden":!0,onClick:je((e=>{if(e.target===e.currentTarget){if(qe(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),l()}}))},theirProps:o,slot:(0,V.useMemo)((()=>({open:0===a})),[a]),defaultTag:"div",name:"Dialog.Overlay"})})),mr=ke((function(e,t){let n=Ue(),{id:r=`headlessui-dialog-title-${n}`,...o}=e,[{dialogState:a,setTitleId:l}]=ir("Dialog.Title"),i=ze(t);(0,V.useEffect)((()=>(l(r),()=>l(null))),[r,l]);let s=(0,V.useMemo)((()=>({open:0===a})),[a]);return Ve({ourProps:{ref:i,id:r},theirProps:o,slot:s,defaultTag:"h2",name:"Dialog.Title"})})),Cr=Object.assign(ur,{Backdrop:dr,Panel:fr,Overlay:pr,Title:mr,Description:kt});var hr=t=>{const{showPopup:n,setShowPopup:r,popupContent:o,popupAccept:a,popupCancel:l}=t,i=(0,e.useRef)(null);return(0,e.createElement)(rn.Root,{show:n,as:e.Fragment},(0,e.createElement)(Cr,{as:"div",className:"fixed backdrop-blur-sm inset-0 overflow-y-auto",style:{zIndex:99999},initialFocus:i,onClose:r},(0,e.createElement)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},(0,e.createElement)(rn.Child,{as:e.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0"},(0,e.createElement)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"})),(0,e.createElement)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true"},"​"),(0,e.createElement)(rn.Child,{as:e.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95",enterTo:"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-y-0 sm:scale-100",leaveTo:"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},(0,e.createElement)("div",{className:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},(0,e.createElement)("div",{className:"sm:flex sm:items-start"},(0,e.createElement)("div",{className:"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-50 sm:mx-0 sm:h-16 sm:w-16"},(0,e.createElement)("svg",{className:"h-8 w-8 stroke-red-600",viewBox:"0 0 34 33",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M17 9.83333V16.5M17 23.1667H17.0167M32 16.5C32 24.7843 25.2843 31.5 17 31.5C8.71573 31.5 2 24.7843 2 16.5C2 8.21573 8.71573 1.5 17 1.5C25.2843 1.5 32 8.21573 32 16.5Z",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"}))),(0,e.createElement)("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left"},(0,e.createElement)(Cr.Title,{as:"h3",className:"text-2xl font-semibold text-slate-800"},o.title),(0,e.createElement)("p",{className:"mt-2 text-sm text-slate-500"},o.description))),(0,e.createElement)("div",{className:"mt-6 sm:flex sm:flex-row sm:ml-20"},(0,e.createElement)("button",{type:"button",className:"w-full inline-flex justify-center rounded border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white focus:bg-red-700 hover:bg-red-700 focus:outline-none sm:mr-3 sm:w-auto sm:text-sm",onClick:a.callback},a.label),(0,e.createElement)("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded border border-slate-200 shadow-sm px-4 py-2 bg-white text-base font-medium text-slate-800 focus:bg-gray-50 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:w-auto sm:text-sm",onClick:l.callback,ref:i},l.label)))))))},vr=window.wp.hooks,gr=()=>{const[n,r]=(0,e.useState)(zip_ai_react?.is_ai_assistant_enabled?"enabled":"disabled"),[o,l]=(0,e.useState)(!1),i=zip_ai_react.credit_details;let c="bg-green-500";switch(!0){case i?.percentage>=i.threshold.high:c="bg-red-500";break;case i?.percentage>=i.threshold.medium:c="bg-amber-500"}return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("main",{className:"py-10"},(0,e.createElement)("div",{className:"max-w-3xl mx-auto px-6 lg:max-w-7xl"},(0,e.createElement)("div",{className:"flex justify-center gap-4 items-start lg:gap-5 xl:gap-10"},(0,e.createElement)("div",{className:"space-y-4 flex h-full flex-col justify-start lg:space-y-5 xl:space-y-10"},(0,vr.applyFilters)("zip_ai_before_welcome_sidebar"),(0,e.createElement)(e.Fragment,null,(0,vr.applyFilters)("zip_ai_render_credits_card",!0)&&(0,e.createElement)("section",{className:"box-border flex-1 flex flex-col gap-4 p-6 rounded-lg bg-white shadow-sm overflow-hidden transition hover:shadow-hover"},(0,e.createElement)("div",{className:"flex gap-2 pb-4 items-start justify-between w-full"},(0,e.createElement)("h3",{className:"text-sm font-semibold text-slate-500"},(0,vr.applyFilters)("zip_ai_credits_card_header",(0,t.__)("Words Written","zip-ai"))),(0,e.createElement)("a",{className:"flex items-center gap-1 px-2 py-0.5 rounded-full text-xs bg-violet-50 text-slate-500",href:zip_ai_react?.credit_topup_url,target:"_blank",rel:"noreferrer noopener"},(0,vr.applyFilters)("zip_ai_credits_card_link_content",(0,t.__)("Get more Credits","zip-ai")),function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,e.createElement)("svg",{width:a,height:a,viewBox:"0 0 12 12",fill:s,...t},(0,e.createElement)("path",{d:"M5 3H3C2.44772 3 2 3.44772 2 4V9C2 9.55228 2.44772 10 3 10H8C8.55228 10 9 9.55228 9 9V7M7 2H10M10 2V5M10 2L5 7",...u,...n}))}({width:12,height:12}))),(0,e.createElement)("div",{className:"flex gap-2 items-center justify-between w-full"},(0,e.createElement)("div",{className:"flex items-end gap-1"},(0,e.createElement)("span",{className:"text-2xl leading-7 font-semibold text-slate-800"},yt(i.used)),(0,e.createElement)("span",{className:"text-sm font-semibold text-slate-500"},(0,t.sprintf)(/* translators: %s: number of words */
(0,t.__)("of %s","zip-ai"),yt(i.total))))),(0,e.createElement)("div",{className:"zip-ai__data-bar bg-slate-200"},(0,e.createElement)("div",{className:Et(["zip-ai__data-bar--progress",c]),style:{width:`${i.percentage}%`}})))),(0,e.createElement)(e.Fragment,null,(0,vr.applyFilters)("zip_ai_render_enable_card",!0)&&(0,e.createElement)("section",{className:"box-border rounded-lg flex flex-col gap-4 p-6 bg-white shadow-sm overflow-hidden transition hover:shadow-hover"},(0,e.createElement)("div",{className:"flex w-full items-center justify-between"},(0,e.createElement)("h3",{className:"text-slate-800 text-xl font-medium"},(0,vr.applyFilters)("zip_ai_render_enable_card_header",(0,t.__)("Enable Zip AI","zip-ai"))),(0,e.createElement)(Dt,{checked:"enabled"===n,onChange:()=>{const e="enabled"===n?"disabled":"enabled";r(e);const t=new FormData;t.append("action","zip_ai_toggle_assistant_status_ajax"),t.append("nonce",zip_ai_react.admin_nonce),t.append("enable_zip_chat",e),fetch(zip_ai_react.ajax_url,{method:"POST",credentials:"same-origin",body:t}).then((e=>e.json())).then((e=>{if(!e.success)throw new Error})).catch((()=>{r(n)}))},className:Et(["enabled"===n?"bg-spec":"bg-slate-200","relative inline-flex flex-shrink-0 h-5 w-[2.4rem] items-center border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none"])},(0,e.createElement)("span",{"aria-hidden":"true",className:Et(["enabled"===n?"translate-x-5":"translate-x-0","pointer-events-none inline-block h-3.5 w-3.5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"])}))),(0,e.createElement)("p",{className:"text-slate-500 text-sm"},(0,vr.applyFilters)("zip_ai_render_enable_card_description",(0,t.__)("You can easily enable or disable Zip AI from this section. Simply toggle the switch on or off with a single click.","zip-ai"))))),(0,e.createElement)("section",{className:"box-border rounded-lg flex flex-col items-start gap-4 p-6 bg-white shadow-sm overflow-hidden transition hover:shadow-hover"},(0,e.createElement)("h3",{className:"text-slate-800 text-xl font-medium"},(0,t.__)("Zip is Authorized","zip-ai"),(0,e.createElement)(bt.Dashicon,{icon:"yes",className:"text-2xl leading-7 text-green-500"})),(0,e.createElement)("p",{className:"text-slate-500 text-sm"},(0,t.__)("Your website is connected to AI. You can disconnect at any time.","zip-ai")),(0,e.createElement)("button",{className:"mt-2 w-auto text-base text-spec focus:text-spec focus-visible:text-spec-hover active:text-spec-hover hover:text-spec-hover border border-spec rounded-md px-4 py-2 transition duration-150 ease-in-out",onClick:()=>{l(!0)},target:"_blank",rel:"noreferrer"},(0,vr.applyFilters)("zip_ai_render_revoke_card_button",(0,t.__)("Revoke Access","zip-ai")))),(0,vr.applyFilters)("zip_ai_after_welcome_sidebar"))))),(0,e.createElement)(hr,{showPopup:o,setShowPopup:l,popupContent:{title:(0,t.__)("Revoke Access","zip-ai"),description:`${(0,t.__)("Are you sure you want to disconnect?","zip-ai")}\n${(0,t.__)("You will need to reconnect to use AI features again.","zip-ai")}`},popupAccept:{label:(0,t.__)("Revoke","zip-ai"),callback:()=>{localStorage.removeItem("zipAiAuthorizationStatus"),window.location.assign(zip_ai_react.auth_revoke_url)}},popupCancel:{label:(0,t.__)("Cancel","zip-ai"),callback:()=>{l(!1)}}}))},yr=()=>{const n=new URLSearchParams(fe(ae).location.search).get("page");return zip_ai_react.page_slug!==n?(0,e.createElement)("p",null,(0,t.__)("Default route fallback","zip-ai")):(0,e.createElement)(gr,null)},Er=()=>((0,e.useEffect)((()=>{zip_ai_react?.is_authorized&&!localStorage.getItem("zipAiAuthorizationStatus")&&localStorage.setItem("zipAiAuthorizationStatus",!0)}),[]),(0,e.createElement)(pe,null,(0,e.createElement)(wt,{title:(0,t.__)("Zip - Your AI Assistant","zip-ai")}),(0,e.createElement)(de,null,(0,e.createElement)(ue,{path:"/"},(0,e.createElement)(yr,null)))));(0,e.render)((0,e.createElement)((()=>zip_ai_react?.is_authorized?(0,e.createElement)(Er,null):(0,e.createElement)(_,null)),null),document.getElementById("zip-ai-dashboard-app"))}()}();