<svg width="480" height="345" viewBox="0 0 480 345" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_25567_55585" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="480" height="345">
<rect y="0.427734" width="480" height="344.571" fill="url(#paint0_radial_25567_55585)"/>
</mask>
<g mask="url(#mask0_25567_55585)">
<line x1="11.1462" y1="2.14258" x2="11.1462" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="28.2868" y1="2.14258" x2="28.2868" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="45.4275" y1="2.14258" x2="45.4274" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="62.5759" y1="2.14258" x2="62.5759" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="79.7165" y1="2.14258" x2="79.7165" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="96.8571" y1="2.14258" x2="96.8571" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="114.006" y1="2.14258" x2="114.006" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="131.146" y1="2.14258" x2="131.146" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="148.287" y1="2.14258" x2="148.287" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="165.427" y1="2.14258" x2="165.427" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="182.576" y1="2.14258" x2="182.576" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="199.717" y1="2.14258" x2="199.717" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="216.857" y1="2.14258" x2="216.857" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="234.006" y1="2.14258" x2="234.006" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="251.146" y1="2.14258" x2="251.146" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="268.287" y1="2.14258" x2="268.287" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="285.427" y1="2.14258" x2="285.427" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="302.576" y1="2.14258" x2="302.576" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="319.717" y1="2.14258" x2="319.716" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="336.857" y1="2.14258" x2="336.857" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="354.006" y1="2.14258" x2="354.006" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="371.146" y1="2.14258" x2="371.146" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="388.287" y1="2.14258" x2="388.287" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="405.427" y1="2.14258" x2="405.427" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="422.576" y1="2.14258" x2="422.576" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="439.717" y1="2.14258" x2="439.716" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="456.857" y1="2.14258" x2="456.857" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="474.006" y1="2.14258" x2="474.006" y2="345" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="335.568" x2="476.578" y2="335.568" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="318.425" x2="476.578" y2="318.425" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="301.282" x2="476.578" y2="301.282" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="284.139" x2="476.578" y2="284.139" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="266.996" x2="476.578" y2="266.996" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="249.853" x2="476.578" y2="249.853" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="232.71" x2="476.578" y2="232.71" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="215.568" x2="476.578" y2="215.568" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="198.425" x2="476.578" y2="198.425" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="181.282" x2="476.578" y2="181.282" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="164.139" x2="476.578" y2="164.139" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="146.996" x2="476.578" y2="146.996" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="129.853" x2="476.578" y2="129.853" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="112.71" x2="476.578" y2="112.71" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="95.5675" x2="476.578" y2="95.5675" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="78.4247" x2="476.578" y2="78.4247" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="61.2818" x2="476.578" y2="61.2818" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="44.139" x2="476.578" y2="44.1389" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="26.996" x2="476.578" y2="26.996" stroke="#E4EAF1" stroke-width="1.71429"/>
<line x1="8.57813" y1="9.85318" x2="476.578" y2="9.85318" stroke="#E4EAF1" stroke-width="1.71429"/>
</g>
<defs>
<radialGradient id="paint0_radial_25567_55585" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(240 172.713) rotate(90) scale(172.286 240)">
<stop offset="0.912036" stop-color="#D9D9D9"/>
<stop offset="1" stop-color="#D9D9D9" stop-opacity="0.5"/>
</radialGradient>
</defs>
</svg>
