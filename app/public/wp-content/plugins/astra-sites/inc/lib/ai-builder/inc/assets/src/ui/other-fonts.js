export const FONTS = [
	{
		'body-font-family': "'Source Sans Pro', sans-serif",
		'body-font-family-slug': 'source-sans-pro',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Playfair Display', serif",
		'headings-font-family-slug': 'playfair-display',
		'headings-font-weight': 700,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Lato', sans-serif",
		'body-font-family-slug': 'lato',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Poppins', sans-serif",
		'headings-font-family-slug': 'poppins',
		'headings-font-weight': 700,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Lato', sans-serif",
		'body-font-family-slug': 'lato',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 17,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Montserrat', sans-serif",
		'headings-font-family-slug': 'montserrat',
		'headings-font-weight': 700,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Karla', sans-serif",
		'body-font-family-slug': 'karla',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 17,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Rubik', sans-serif",
		'headings-font-family-slug': 'rubik',
		'headings-font-weight': 700,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Roboto', sans-serif",
		'body-font-family-slug': 'roboto',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Roboto Condensed', sans-serif",
		'headings-font-family-slug': 'roboto-condensed',
		'headings-font-weight': 700,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Figtree', sans-serif",
		'body-font-family-slug': 'inter',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 17,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Merriweather', serif",
		'headings-font-family-slug': 'merriweather',
		'headings-font-weight': 700,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Open Sans', sans-serif",
		'body-font-family-slug': 'open-sans',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Vollkorn', serif",
		'headings-font-family-slug': 'vollkorn',
		'headings-font-weight': 700,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
	{
		'body-font-family': "'Work Sans', sans-serif",
		'body-font-family-slug': 'work-sans',
		'body-font-variant': '',
		'body-font-weight': 400,
		'font-size-body': {
			desktop: 16,
			tablet: '',
			mobile: '',
			'desktop-unit': 'px',
			'tablet-unit': 'px',
			'mobile-unit': 'px',
		},
		'body-line-height': '',
		'headings-font-family': "'Open Sans', sans-serif",
		'headings-font-family-slug': 'open-sans',
		'headings-font-weight': 700,
		'headings-line-height': '',
		'headings-font-variant': '',
	},
];
