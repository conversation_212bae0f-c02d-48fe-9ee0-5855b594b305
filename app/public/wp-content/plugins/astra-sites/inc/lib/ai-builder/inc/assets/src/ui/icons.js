import { classNames } from '../helpers';

export const WandIcon = ( { className } ) => (
	<svg
		className={ className }
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		strokeWidth="1.5"
	>
		<path
			d="M12.5 5L15 7.5M5 17.5L17.5 5L15 2.5L2.5 15L5 17.5ZM7.5 2.5C7.5 2.94203 7.67559 3.36595 7.98816 3.67851C8.30072 3.99107 8.72464 4.16667 9.16667 4.16667C8.72464 4.16667 8.30072 4.34226 7.98816 4.65482C7.67559 4.96738 7.5 5.39131 7.5 5.83333C7.5 5.39131 7.3244 4.96738 7.01184 4.65482C6.69928 4.34226 6.27536 4.16667 5.83333 4.16667C6.27536 4.16667 6.69928 3.99107 7.01184 3.67851C7.3244 3.36595 7.5 2.94203 7.5 2.5ZM15.8333 10.8333C15.8333 11.2754 16.0089 11.6993 16.3215 12.0118C16.634 12.3244 17.058 12.5 17.5 12.5C17.058 12.5 16.634 12.6756 16.3215 12.9882C16.0089 13.3007 15.8333 13.7246 15.8333 14.1667C15.8333 13.7246 15.6577 13.3007 15.3452 12.9882C15.0326 12.6756 14.6087 12.5 14.1667 12.5C14.6087 12.5 15.0326 12.3244 15.3452 12.0118C15.6577 11.6993 15.8333 11.2754 15.8333 10.8333Z"
			stroke="currentColor"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const DesignLibraryLogo = ( { className } ) => (
	<svg
		className={ className }
		width="173"
		height="30"
		viewBox="0 0 173 30"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g clipPath="url(#clip0_207_15102)">
			{ ! aiBuilderVars.is_white_label && (
				<path
					fillRule="evenodd"
					clipRule="evenodd"
					d="M12 27C18.6274 27 24 21.6274 24 15C24 8.37257 18.6274 3 12 3C5.37257 3 0 8.37257 0 15C0 21.6274 5.37257 27 12 27ZM15.6902 18.516C16.384 18.0774 16.8 17.3435 16.8 16.5581C16.8 15.4668 16.0032 14.5178 14.8731 14.2631L11.4871 13.4226C11.1679 13.3507 11.0914 12.9571 11.3629 12.7839L14.0009 11.1013C15.2289 10.318 15.5481 8.74841 14.714 7.59538C14.6098 7.45125 14.4008 7.41377 14.2473 7.51169L8.28802 11.5315C7.60745 11.9655 7.2 12.688 7.2 13.4605C7.2 14.5388 7.98723 15.4764 9.10385 15.7281L12.545 16.581C12.865 16.6531 12.9408 17.048 12.6679 17.2205L10.0105 18.9005C8.77913 19.6789 8.45294 21.2473 9.28193 22.4035C9.38558 22.5481 9.59434 22.5864 9.74829 22.489L15.6902 18.516Z"
					fill="#F0F0FF"
				/>
			) }
			<path
				d="M38.3636 22H33.4347V7.45455H38.4631C39.9072 7.45455 41.1477 7.74574 42.1847 8.32812C43.2263 8.90578 44.0265 9.73674 44.5852 10.821C45.1439 11.9053 45.4233 13.2027 45.4233 14.7131C45.4233 16.2282 45.1416 17.5303 44.5781 18.6193C44.0194 19.7083 43.2121 20.544 42.1562 21.1264C41.1051 21.7088 39.8409 22 38.3636 22ZM36.0696 19.7202H38.2358C39.2491 19.7202 40.0942 19.5355 40.7713 19.1662C41.4484 18.7921 41.9574 18.2358 42.2983 17.4972C42.6392 16.7538 42.8097 15.8258 42.8097 14.7131C42.8097 13.6004 42.6392 12.6771 42.2983 11.9432C41.9574 11.2045 41.4531 10.6529 40.7855 10.2884C40.1226 9.91903 39.2988 9.73438 38.3139 9.73438H36.0696V19.7202ZM52.6428 22.2131C51.549 22.2131 50.6044 21.9858 49.8089 21.5312C49.0182 21.072 48.4098 20.4233 47.9837 19.5852C47.5575 18.7424 47.3445 17.7505 47.3445 16.6094C47.3445 15.4872 47.5575 14.5024 47.9837 13.6548C48.4145 12.8026 49.0159 12.1397 49.7876 11.6662C50.5594 11.188 51.4661 10.9489 52.5078 10.9489C53.1802 10.9489 53.8146 11.0578 54.4112 11.2756C55.0125 11.4886 55.5429 11.8201 56.0021 12.2699C56.4661 12.7197 56.8307 13.2926 57.0959 13.9886C57.361 14.6799 57.4936 15.5038 57.4936 16.4602V17.2486H48.5518V15.5156H55.0291C55.0244 15.0232 54.9179 14.5852 54.7095 14.2017C54.5012 13.8134 54.21 13.508 53.8359 13.2855C53.4666 13.063 53.0357 12.9517 52.5433 12.9517C52.0178 12.9517 51.5561 13.0795 51.1584 13.3352C50.7607 13.5862 50.4505 13.9176 50.228 14.3295C50.0102 14.7367 49.8989 15.1842 49.8942 15.6719V17.1847C49.8942 17.8191 50.0102 18.3636 50.2422 18.8182C50.4742 19.268 50.7985 19.6136 51.2152 19.8551C51.6319 20.0919 52.1196 20.2102 52.6783 20.2102C53.0523 20.2102 53.3909 20.1581 53.6939 20.054C53.9969 19.9451 54.2597 19.7865 54.4822 19.5781C54.7048 19.3698 54.8729 19.1117 54.9865 18.804L57.3871 19.0739C57.2356 19.7083 56.9467 20.2623 56.5206 20.7358C56.0992 21.2045 55.5594 21.5691 54.9013 21.8295C54.2431 22.0852 53.4903 22.2131 52.6428 22.2131ZM68.2908 13.9744L65.9471 14.2301C65.8808 13.9934 65.7648 13.7708 65.5991 13.5625C65.4381 13.3542 65.2203 13.1861 64.9457 13.0582C64.671 12.9304 64.3349 12.8665 63.9371 12.8665C63.4021 12.8665 62.9523 12.9825 62.5877 13.2145C62.2279 13.4465 62.0503 13.7472 62.055 14.1165C62.0503 14.4337 62.1663 14.6918 62.4031 14.8906C62.6445 15.0895 63.0423 15.2528 63.5962 15.3807L65.457 15.7784C66.4892 16.0009 67.2563 16.3537 67.7582 16.8366C68.2648 17.3196 68.5205 17.9517 68.5252 18.733C68.5205 19.4195 68.3192 20.0256 67.9215 20.5511C67.5285 21.072 66.9817 21.4792 66.2809 21.7727C65.5801 22.0663 64.7752 22.2131 63.8661 22.2131C62.5309 22.2131 61.4561 21.9337 60.6417 21.375C59.8273 20.8116 59.342 20.0279 59.1857 19.0241L61.6928 18.7827C61.8065 19.2751 62.0479 19.6468 62.4173 19.8977C62.7866 20.1487 63.2672 20.2741 63.859 20.2741C64.4698 20.2741 64.9599 20.1487 65.3292 19.8977C65.7032 19.6468 65.8903 19.3366 65.8903 18.9673C65.8903 18.6548 65.7695 18.3968 65.5281 18.1932C65.2913 17.9896 64.922 17.8333 64.4201 17.7244L62.5593 17.3338C61.5129 17.116 60.7388 16.7491 60.2369 16.233C59.735 15.7121 59.4864 15.054 59.4911 14.2585C59.4864 13.5862 59.6687 13.0038 60.038 12.5114C60.4121 12.0142 60.9305 11.6307 61.5934 11.3608C62.261 11.0862 63.0304 10.9489 63.9016 10.9489C65.18 10.9489 66.1862 11.2211 66.9201 11.7656C67.6587 12.3101 68.1156 13.0464 68.2908 13.9744ZM70.6452 22V11.0909H73.2163V22H70.6452ZM71.9379 9.54261C71.5307 9.54261 71.1803 9.40767 70.8867 9.13778C70.5932 8.86316 70.4464 8.53409 70.4464 8.15057C70.4464 7.76231 70.5932 7.43324 70.8867 7.16335C71.1803 6.88873 71.5307 6.75142 71.9379 6.75142C72.3498 6.75142 72.7002 6.88873 72.989 7.16335C73.2826 7.43324 73.4293 7.76231 73.4293 8.15057C73.4293 8.53409 73.2826 8.86316 72.989 9.13778C72.7002 9.40767 72.3498 9.54261 71.9379 9.54261ZM80.6044 26.3182C79.6811 26.3182 78.888 26.1927 78.2251 25.9418C77.5623 25.6955 77.0296 25.3641 76.6271 24.9474C76.2247 24.5308 75.9453 24.0691 75.7891 23.5625L78.1044 23.0014C78.2086 23.2145 78.3601 23.4252 78.5589 23.6335C78.7578 23.8466 79.0253 24.0218 79.3615 24.1591C79.7024 24.3011 80.1309 24.3722 80.647 24.3722C81.3762 24.3722 81.9799 24.1946 82.4581 23.8395C82.9363 23.4891 83.1754 22.9115 83.1754 22.1065V20.0398H83.0476C82.915 20.3049 82.7209 20.5772 82.4652 20.8565C82.2143 21.1359 81.8804 21.3703 81.4638 21.5597C81.0518 21.7491 80.5334 21.8438 79.9084 21.8438C79.0703 21.8438 78.3104 21.6473 77.6286 21.2543C76.9515 20.8565 76.4117 20.2647 76.0092 19.4787C75.6115 18.688 75.4126 17.6984 75.4126 16.5099C75.4126 15.312 75.6115 14.3011 76.0092 13.4773C76.4117 12.6487 76.9538 12.0213 77.6357 11.5952C78.3175 11.1643 79.0774 10.9489 79.9155 10.9489C80.5547 10.9489 81.0803 11.0578 81.4922 11.2756C81.9089 11.4886 82.2403 11.7467 82.4865 12.0497C82.7327 12.348 82.9197 12.6297 83.0476 12.8949H83.1896V11.0909H85.7251V22.1776C85.7251 23.1103 85.5026 23.8821 85.0575 24.4929C84.6125 25.1037 84.004 25.5606 83.2322 25.8636C82.4605 26.1667 81.5845 26.3182 80.6044 26.3182ZM80.6257 19.8267C81.1702 19.8267 81.6342 19.6941 82.0178 19.429C82.4013 19.1638 82.6925 18.7827 82.8913 18.2855C83.0902 17.7884 83.1896 17.1918 83.1896 16.4957C83.1896 15.8092 83.0902 15.2079 82.8913 14.6918C82.6972 14.1757 82.4084 13.7756 82.0249 13.4915C81.6461 13.2027 81.1797 13.0582 80.6257 13.0582C80.0528 13.0582 79.5746 13.2074 79.1911 13.5057C78.8075 13.804 78.5187 14.2135 78.3246 14.7344C78.1304 15.2505 78.0334 15.8376 78.0334 16.4957C78.0334 17.1634 78.1304 17.7481 78.3246 18.25C78.5234 18.7472 78.8146 19.1354 79.1982 19.4148C79.5864 19.6894 80.0623 19.8267 80.6257 19.8267ZM90.9311 15.608V22H88.3601V11.0909H90.8175V12.9446H90.9453C91.1963 12.3338 91.5964 11.8485 92.1456 11.4886C92.6996 11.1288 93.3838 10.9489 94.1982 10.9489C94.951 10.9489 95.6068 11.1098 96.1655 11.4318C96.7289 11.7538 97.1645 12.2202 97.4723 12.831C97.7848 13.4418 97.9387 14.1828 97.9339 15.054V22H95.3629V15.4517C95.3629 14.7225 95.1735 14.152 94.7947 13.7401C94.4207 13.3281 93.9022 13.1222 93.2393 13.1222C92.7895 13.1222 92.3894 13.2216 92.0391 13.4205C91.6934 13.6146 91.4212 13.8963 91.2223 14.2656C91.0282 14.6349 90.9311 15.0824 90.9311 15.608ZM105.622 22V7.45455H108.257V19.7912H114.663V22H105.622ZM116.837 22V11.0909H119.408V22H116.837ZM118.129 9.54261C117.722 9.54261 117.372 9.40767 117.078 9.13778C116.785 8.86316 116.638 8.53409 116.638 8.15057C116.638 7.76231 116.785 7.43324 117.078 7.16335C117.372 6.88873 117.722 6.75142 118.129 6.75142C118.541 6.75142 118.892 6.88873 119.18 7.16335C119.474 7.43324 119.621 7.76231 119.621 8.15057C119.621 8.53409 119.474 8.86316 119.18 9.13778C118.892 9.40767 118.541 9.54261 118.129 9.54261ZM122.165 22V7.45455H124.736V12.8949H124.843C124.975 12.6297 125.162 12.348 125.404 12.0497C125.645 11.7467 125.972 11.4886 126.384 11.2756C126.796 11.0578 127.321 10.9489 127.961 10.9489C128.803 10.9489 129.563 11.1643 130.24 11.5952C130.922 12.0213 131.462 12.6534 131.86 13.4915C132.262 14.3248 132.463 15.3475 132.463 16.5597C132.463 17.7576 132.267 18.7756 131.874 19.6136C131.481 20.4517 130.946 21.0909 130.269 21.5312C129.592 21.9716 128.825 22.1918 127.968 22.1918C127.343 22.1918 126.824 22.0876 126.412 21.8793C126 21.6709 125.669 21.42 125.418 21.1264C125.172 20.8281 124.98 20.5464 124.843 20.2812H124.694V22H122.165ZM124.686 16.5455C124.686 17.2509 124.786 17.8688 124.985 18.3991C125.188 18.9295 125.48 19.3437 125.858 19.642C126.242 19.9356 126.706 20.0824 127.25 20.0824C127.819 20.0824 128.294 19.9309 128.678 19.6278C129.061 19.3201 129.35 18.901 129.544 18.3707C129.743 17.8357 129.843 17.2273 129.843 16.5455C129.843 15.8684 129.746 15.267 129.551 14.7415C129.357 14.2159 129.069 13.804 128.685 13.5057C128.301 13.2074 127.823 13.0582 127.25 13.0582C126.701 13.0582 126.235 13.2027 125.851 13.4915C125.468 13.7803 125.176 14.1851 124.978 14.706C124.783 15.2268 124.686 15.84 124.686 16.5455ZM134.649 22V11.0909H137.142V12.9091H137.256C137.455 12.2794 137.795 11.794 138.278 11.4531C138.766 11.1075 139.322 10.9347 139.947 10.9347C140.089 10.9347 140.248 10.9418 140.423 10.956C140.603 10.9654 140.752 10.982 140.871 11.0057V13.3707C140.762 13.3329 140.589 13.2997 140.352 13.2713C140.12 13.2382 139.895 13.2216 139.678 13.2216C139.209 13.2216 138.787 13.3234 138.413 13.527C138.044 13.7259 137.753 14.0028 137.54 14.358C137.327 14.7131 137.22 15.1226 137.22 15.5866V22H134.649ZM145.523 22.2202C144.831 22.2202 144.209 22.0971 143.655 21.8509C143.106 21.5999 142.67 21.2306 142.348 20.7429C142.031 20.2552 141.872 19.6539 141.872 18.9389C141.872 18.3234 141.986 17.8144 142.213 17.4119C142.44 17.0095 142.75 16.6875 143.143 16.446C143.536 16.2045 143.979 16.0223 144.472 15.8991C144.969 15.7713 145.482 15.679 146.013 15.6222C146.652 15.5559 147.17 15.4967 147.568 15.4446C147.966 15.3878 148.255 15.3026 148.435 15.1889C148.619 15.0705 148.712 14.8883 148.712 14.642V14.5994C148.712 14.0644 148.553 13.6501 148.236 13.3565C147.919 13.063 147.462 12.9162 146.865 12.9162C146.235 12.9162 145.736 13.0535 145.366 13.3281C145.002 13.6027 144.756 13.9271 144.628 14.3011L142.227 13.9602C142.417 13.2973 142.729 12.7434 143.165 12.2983C143.6 11.8485 144.133 11.5123 144.763 11.2898C145.393 11.0625 146.089 10.9489 146.851 10.9489C147.376 10.9489 147.9 11.0104 148.42 11.1335C148.941 11.2566 149.417 11.4602 149.848 11.7443C150.279 12.0237 150.625 12.4048 150.885 12.8878C151.15 13.3707 151.283 13.9744 151.283 14.6989V22H148.811V20.5014H148.726C148.57 20.8045 148.349 21.0885 148.065 21.3537C147.786 21.6141 147.433 21.8248 147.007 21.9858C146.586 22.142 146.091 22.2202 145.523 22.2202ZM146.19 20.331C146.706 20.331 147.154 20.2292 147.533 20.0256C147.911 19.8172 148.203 19.5426 148.406 19.2017C148.615 18.8608 148.719 18.4891 148.719 18.0866V16.8011C148.638 16.8674 148.501 16.929 148.307 16.9858C148.117 17.0426 147.904 17.0923 147.668 17.1349C147.431 17.1776 147.196 17.2154 146.964 17.2486C146.732 17.2817 146.531 17.3101 146.361 17.3338C145.977 17.3859 145.634 17.4711 145.331 17.5895C145.028 17.7079 144.789 17.8736 144.614 18.0866C144.438 18.295 144.351 18.5649 144.351 18.8963C144.351 19.3698 144.524 19.7273 144.869 19.9688C145.215 20.2102 145.655 20.331 146.19 20.331ZM153.868 22V11.0909H156.361V12.9091H156.474C156.673 12.2794 157.014 11.794 157.497 11.4531C157.985 11.1075 158.541 10.9347 159.166 10.9347C159.308 10.9347 159.467 10.9418 159.642 10.956C159.822 10.9654 159.971 10.982 160.089 11.0057V13.3707C159.981 13.3329 159.808 13.2997 159.571 13.2713C159.339 13.2382 159.114 13.2216 158.896 13.2216C158.428 13.2216 158.006 13.3234 157.632 13.527C157.263 13.7259 156.972 14.0028 156.759 14.358C156.545 14.7131 156.439 15.1226 156.439 15.5866V22H153.868ZM163.728 26.0909C163.377 26.0909 163.053 26.0625 162.755 26.0057C162.461 25.9536 162.227 25.892 162.051 25.821L162.648 23.8182C163.022 23.9271 163.356 23.9792 163.65 23.9744C163.943 23.9697 164.201 23.8774 164.424 23.6974C164.651 23.5223 164.843 23.2287 164.999 22.8168L165.219 22.2273L161.263 11.0909H163.99L166.505 19.3295H166.618L169.14 11.0909H171.874L167.506 23.321C167.302 23.8987 167.033 24.3935 166.696 24.8054C166.36 25.2221 165.948 25.5393 165.461 25.7571C164.978 25.9796 164.4 26.0909 163.728 26.0909Z"
				fill="white"
			/>
		</g>
		<defs>
			<clipPath id="clip0_207_15102">
				<rect width="173" height="30" fill="white" />
			</clipPath>
		</defs>
	</svg>
);

export const CheckCircleColorfulIcon = ( { className } ) => (
	<svg
		className={ className }
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<rect x="0.5" y="0.5" width="23" height="23" rx="11.5" fill="#2563EB" />
		<path
			d="M8.25 12.375L11.25 15.375L15.75 8.625"
			stroke="white"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<rect
			x="0.5"
			y="0.5"
			width="23"
			height="23"
			rx="11.5"
			stroke="#2563EB"
		/>
	</svg>
);

export const SpectraLogo = ( { className } ) => (
	<svg
		className={ className }
		width="40"
		height="40"
		viewBox="0 0 40 40"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			fillRule="evenodd"
			clipRule="evenodd"
			d="M20 40C31.0457 40 40 31.0457 40 20C40 8.95429 31.0457 0 20 0C8.95429 0 0 8.95429 0 20C0 31.0457 8.95429 40 20 40ZM26.1503 25.86C27.3066 25.129 27.9999 23.9058 28 22.5969C28.0001 20.7781 26.6721 19.1963 24.7885 18.7718L19.1451 17.371C18.6131 17.2511 18.4857 16.5951 18.9382 16.3065L23.3349 13.5021C25.3814 12.1967 25.9135 9.58069 24.5234 7.65897C24.3497 7.41874 24.0014 7.35629 23.7455 7.51949L13.8134 14.2191C12.6791 14.9426 12 16.1466 12 17.4342C12 19.2313 13.3121 20.7941 15.1731 21.2135L20.9084 22.635C21.4416 22.7552 21.568 23.4133 21.1131 23.7009L16.6842 26.5008C14.6319 27.7982 14.0882 30.4121 15.4699 32.3392C15.6426 32.5801 15.9906 32.6439 16.2471 32.4817L26.1503 25.86Z"
			fill="#5A03EF"
		/>
	</svg>
);

export const SuccessIcon = ( { className } ) => (
	<svg
		className={ className }
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			d="M9 12.75L11.25 15L15 9.75M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
			stroke="#0BC16A"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const SyncIcon = ( { className } ) => (
	<svg
		className={ className }
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			d="M16.0228 9.34841H21.0154V9.34663M2.98413 19.6444V14.6517M2.98413 14.6517L7.97677 14.6517M2.98413 14.6517L6.16502 17.8347C7.15555 18.8271 8.41261 19.58 9.86436 19.969C14.2654 21.1483 18.7892 18.5364 19.9685 14.1353M4.03073 9.86484C5.21 5.46374 9.73377 2.85194 14.1349 4.03121C15.5866 4.4202 16.8437 5.17312 17.8342 6.1655L21.0154 9.34663M21.0154 4.3558V9.34663"
			stroke="#5A03EF"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const ErrorIcon = ( { className } ) => (
	<svg
		className={ className }
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			d="M11.9998 8.99994V12.7499M2.69653 16.1256C1.83114 17.6256 2.91371 19.4999 4.64544 19.4999H19.3541C21.0858 19.4999 22.1684 17.6256 21.303 16.1256L13.9487 3.37807C13.0828 1.87723 10.9167 1.87723 10.0509 3.37807L2.69653 16.1256ZM11.9998 15.7499H12.0073V15.7574H11.9998V15.7499Z"
			stroke="#F92B0F"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const FacebookIcon = ( { className, width = 20, height = 20 } ) => (
	<svg
		width={ width }
		height={ height }
		className={ className }
		viewBox="0 0 20 20"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g id="Icon">
			<path
				id="Vector"
				d="M2.89 2H17.12C17.61 2 18 2.39 18 2.88V17.12C18 17.6 17.61 18 17.12 18H13.04V11.8H15.12L15.43 9.39H13.04V7.85C13.04 7.15 13.24 6.67 14.24 6.67H15.52V4.51C15.3 4.48 14.54 4.42 13.66 4.42C11.81 4.42 10.55 5.54 10.55 7.61V9.39H8.46V11.8H10.55V18H2.89C2.65568 18 2.43081 17.9076 2.26418 17.7429C2.09756 17.5781 2.00263 17.3543 2 17.12V2.88C2 2.39 2.4 2 2.89 2Z"
				fill="currentColor"
			/>
		</g>
	</svg>
);

export const InstagramIcon = ( { className, width = 20, height = 20 } ) => (
	<svg
		width={ width }
		height={ height }
		className={ className }
		viewBox="0 0 20 20"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g id="Icon">
			<path
				id="Vector"
				d="M12.7 10C12.7 8.5 11.5 7.3 10 7.3C8.5 7.3 7.3 8.5 7.3 10C7.3 11.5 8.5 12.7 10 12.7C11.5 12.7 12.7 11.5 12.7 10ZM14.1 10C14.1 12.3 12.3 14.1 10 14.1C7.7 14.1 5.9 12.3 5.9 10C5.9 7.7 7.7 5.9 10 5.9C12.3 5.9 14.1 7.7 14.1 10ZM15.2 5.7C15.2 6.3 14.8 6.7 14.2 6.7C13.6 6.7 13.2 6.3 13.2 5.7C13.2 5.1 13.6 4.7 14.2 4.7C14.8 4.7 15.2 5.2 15.2 5.7ZM10 3.4C8.8 3.4 6.3 3.3 5.3 3.7C4.6 4 4 4.6 3.8 5.3C3.4 6.3 3.5 8.8 3.5 10C3.5 11.2 3.4 13.7 3.8 14.7C4 15.4 4.6 16 5.3 16.2C6.3 16.6 8.9 16.5 10 16.5C11.1 16.5 13.7 16.6 14.7 16.2C15.4 15.9 15.9 15.4 16.2 14.7C16.6 13.6 16.5 11.1 16.5 10C16.5 8.9 16.6 6.3 16.2 5.3C16 4.6 15.4 4 14.7 3.8C13.7 3.3 11.2 3.4 10 3.4ZM18 10V13.3C18 14.5 17.6 15.7 16.7 16.7C15.8 17.6 14.6 18 13.3 18H6.7C5.5 18 4.3 17.6 3.3 16.7C2.5 15.8 2 14.6 2 13.3V10V6.7C2 5.4 2.5 4.2 3.3 3.3C4.3 2.5 5.5 2 6.7 2H13.3C14.5 2 15.7 2.4 16.7 3.3C17.5 4.2 18 5.4 18 6.7V10Z"
				fill="currentColor"
			/>
		</g>
	</svg>
);

export const LinkedInIcon = ( { className, width = 20, height = 20 } ) => (
	<svg
		width={ width }
		height={ height }
		className={ className }
		viewBox="0 0 20 20"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g id="Icon">
			<path
				id="Vector"
				d="M2.5002 18H5.5002V6.9H2.5002V18ZM4.0002 2C3.0002 2 2.2002 2.8 2.2002 3.8C2.2002 4.8 3.0002 5.6 4.0002 5.6C5.0002 5.6 5.8002 4.8 5.8002 3.8C5.8002 2.8 5.0002 2 4.0002 2ZM10.6002 8.6V6.9H7.60019V18H10.6002V12.3C10.6002 9.1 14.7002 8.9 14.7002 12.3V18H17.7002V11.2C17.7002 5.8 12.0002 6 10.6002 8.6Z"
				fill="currentColor"
			/>
		</g>
	</svg>
);

export const TwitterIcon = ( { className, width = 20, height = 20 } ) => (
	<svg
		width={ width }
		height={ height }
		className={ className }
		viewBox="0 0 20 20"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g id="Icon">
			<path
				id="Vector"
				d="M18.9396 4.46023C18.4496 5.19023 17.8296 5.84023 17.1096 6.36023C17.1196 6.51023 17.1196 6.67023 17.1196 6.83023C17.1196 11.6802 13.4296 17.2702 6.68957 17.2702C4.61957 17.2702 2.68957 16.6602 1.05957 15.6202C1.34957 15.6502 1.63957 15.6702 1.93957 15.6702C3.65957 15.6702 5.23957 15.0802 6.48957 14.1002C5.72491 14.0844 4.98429 13.8302 4.37115 13.373C3.758 12.9158 3.30296 12.2786 3.06957 11.5502C3.28957 11.5902 3.51957 11.6202 3.75957 11.6202C4.08957 11.6202 4.41957 11.5702 4.71957 11.4902C3.89017 11.32 3.14485 10.8689 2.60929 10.2131C2.07373 9.55734 1.78068 8.73693 1.77957 7.89023V7.85023C2.27957 8.12023 2.83957 8.29024 3.43957 8.31024C2.93737 7.97337 2.52593 7.51794 2.24163 6.98422C1.95733 6.4505 1.80893 5.85495 1.80957 5.25023C1.80957 4.58023 1.98957 3.95023 2.30957 3.41023C4.11957 5.63023 6.81957 7.09023 9.86957 7.24023C9.80957 6.97023 9.76957 6.69023 9.76957 6.40023C9.77222 5.42862 10.16 4.49771 10.848 3.81161C11.536 3.12552 12.468 2.74023 13.4396 2.74023C14.4996 2.74023 15.4496 3.18023 16.1196 3.90023C16.9496 3.73023 17.7396 3.43023 18.4496 3.01023C18.1696 3.86023 17.5896 4.58023 16.8296 5.03023C17.5582 4.9506 18.27 4.7583 18.9396 4.46023Z"
				fill="currentColor"
			/>
		</g>
	</svg>
);

export const YouTubeIcon = ( { className, width = 20, height = 20 } ) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			x="0px"
			y="0px"
			width={ width }
			height={ height }
			viewBox="0 0 50 50"
			className={ className }
		>
			<path
				fill="currentColor"
				d="M 44.898438 14.5 C 44.5 12.300781 42.601563 10.699219 40.398438 10.199219 C 37.101563 9.5 31 9 24.398438 9 C 17.800781 9 11.601563 9.5 8.300781 10.199219 C 6.101563 10.699219 4.199219 12.199219 3.800781 14.5 C 3.398438 17 3 20.5 3 25 C 3 29.5 3.398438 33 3.898438 35.5 C 4.300781 37.699219 6.199219 39.300781 8.398438 39.800781 C 11.898438 40.5 17.898438 41 24.5 41 C 31.101563 41 37.101563 40.5 40.601563 39.800781 C 42.800781 39.300781 44.699219 37.800781 45.101563 35.5 C 45.5 33 46 29.398438 46.101563 25 C 45.898438 20.5 45.398438 17 44.898438 14.5 Z M 19 32 L 19 18 L 31.199219 25 Z"
			/>
		</svg>
	);
};

export const GoogleIcon = ( { className, width = 20, height = 20 } ) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			x="0px"
			y="0px"
			width={ width }
			height={ height }
			viewBox="0 0 24 24"
			className={ className }
		>
			<path
				fill="currentColor"
				d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032 s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2 C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"
			/>
		</svg>
	);
};

export const YelpIcon = ( { className, width = 20, height = 20 } ) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			viewBox="0 0 16 16"
			x="0px"
			y="0px"
			width={ width }
			height={ height }
			className={ className }
		>
			<path
				fill="currentColor"
				d="M4.015.729C4.828.094 6.391-.087 7.034.037c.644.123.98.48.984.916L8.055 6.5c.003.436-.225.836-.507.89-.283.053-.7-.206-.93-.577L3.685 2.056c-.227-.371-.685-.535.33-1.327zm-.974 10.55 2.953-1.037c.411-.145.78-.548.821-.898.04-.35-.259-.766-.665-.925L3.031 7.192c-.406-.16-.875.218-.998.836 0 0-.074 2.153 0 2.598.075.446.596.799 1.008.653zm5.151.519c.008-.437-.226-.814-.522-.841-.296-.027-.766.223-1.047.556l-2.06 2.452c-.28.333-.141 1.115.268 1.264l2.154.742c.41.148 1.132-.29 1.14-.726l.067-3.447zm5.119-.571-2.583-.737c-.419-.12-.894-.108-1.055.027-.161.133-.121.556.09.938l1.63 2.961c.21.381 1.054.316 1.286-.054 0 0 .982-1.438 1.163-1.978.181-.541-.112-1.038-.531-1.157zm.629-4.164c-.116-.375-.924-1.479-1.361-1.855-.437-.377-.937-.33-1.218.001L9.696 7.178c-.282.331-.358.857-.17 1.165.188.309.689.484 1.115.389l2.879-.53c.52-.124.539-.764.42-1.139z"
			/>
		</svg>
	);
};

export const ExternalLinkIcon = ( { className, width = 16, height = 16 } ) => (
	<svg
		className={ className }
		width={ width }
		height={ height }
		viewBox="0 0 16 16"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g id="Icon">
			<g id="outlines">
				<path
					id="Vector 27 (Stroke)"
					fillRule="evenodd"
					clipRule="evenodd"
					d="M2.83301 4.66634C2.83301 3.65382 3.65382 2.83301 4.66634 2.83301H6.83301V3.83301H4.66634C4.2061 3.83301 3.83301 4.2061 3.83301 4.66634V11.333C3.83301 11.7932 4.2061 12.1663 4.66634 12.1663H11.333C11.7932 12.1663 12.1663 11.7932 12.1663 11.333V8.90708H13.1663V11.333C13.1663 12.3455 12.3455 13.1663 11.333 13.1663H4.66634C3.65382 13.1663 2.83301 12.3455 2.83301 11.333V4.66634Z"
					fill="currentColor"
				/>
				<path
					id="Vector  (Stroke)"
					fillRule="evenodd"
					clipRule="evenodd"
					d="M12.4151 3.0129L9.93155 3.02413L9.92704 2.02414L14.132 2.00514L14.0959 6.1951L13.096 6.18649L13.1172 3.72503L8.86055 7.98165L8.15344 7.27454L12.4151 3.0129Z"
					fill="currentColor"
				/>
			</g>
		</g>
	</svg>
);

export const TwitterXLogo = () => (
	<svg
		width="17"
		height="16"
		viewBox="0 0 17 16"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g clipPath="url(#clip0_22605_64194)">
			<path
				d="M10.0222 6.77491L15.9785 0H14.5671L9.39516 5.88256L5.26437 0H0.5L6.74656 8.89547L0.5 16H1.91155L7.37321 9.78782L11.7356 16H16.5L10.0218 6.77491H10.0222ZM8.08887 8.97384L7.45596 8.08805L2.42015 1.03974H4.5882L8.65216 6.72795L9.28507 7.61374L14.5677 15.0075H12.3997L8.08887 8.97418V8.97384Z"
				fill="#475569"
			/>
		</g>
		<defs>
			<clipPath id="clip0_22605_64194">
				<rect
					width="16"
					height="16"
					fill="white"
					transform="translate(0.5)"
				/>
			</clipPath>
		</defs>
	</svg>
);

export const LinkedInLogo = () => (
	<svg
		width="19"
		height="19"
		viewBox="0 0 19 19"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			d="M17 0.75C17.6641 0.75 18.25 1.33594 18.25 2.03906V17C18.25 17.7031 17.6641 18.25 17 18.25H1.96094C1.29688 18.25 0.75 17.7031 0.75 17V2.03906C0.75 1.33594 1.29688 0.75 1.96094 0.75H17ZM6.02344 15.75V7.42969H3.44531V15.75H6.02344ZM4.73438 6.25781C5.55469 6.25781 6.21875 5.59375 6.21875 4.77344C6.21875 3.95312 5.55469 3.25 4.73438 3.25C3.875 3.25 3.21094 3.95312 3.21094 4.77344C3.21094 5.59375 3.875 6.25781 4.73438 6.25781ZM15.75 15.75V11.1797C15.75 8.95312 15.2422 7.19531 12.625 7.19531C11.375 7.19531 10.5156 7.89844 10.1641 8.5625H10.125V7.42969H7.66406V15.75H10.2422V11.6484C10.2422 10.5547 10.4375 9.5 11.8047 9.5C13.1328 9.5 13.1328 10.75 13.1328 11.6875V15.75H15.75Z"
			fill="#475569"
		/>
	</svg>
);

export const FacebookLogo = () => (
	<svg
		width="21"
		height="21"
		viewBox="0 0 21 21"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			d="M20.1875 10.5C20.1875 15.3438 16.6328 19.3672 11.9844 20.0703V13.3125H14.25L14.6797 10.5H11.9844V8.70312C11.9844 7.92188 12.375 7.17969 13.5859 7.17969H14.7969V4.79688C14.7969 4.79688 13.7031 4.60156 12.6094 4.60156C10.4219 4.60156 8.97656 5.96875 8.97656 8.39062V10.5H6.51562V13.3125H8.97656V20.0703C4.32812 19.3672 0.8125 15.3438 0.8125 10.5C0.8125 5.14844 5.14844 0.8125 10.5 0.8125C15.8516 0.8125 20.1875 5.14844 20.1875 10.5Z"
			fill="#475569"
		/>
	</svg>
);

export const ExclamationTriangleColorfulIcon = ( { className } ) => (
	<svg
		className={ classNames( className ) }
		xmlns="http://www.w3.org/2000/svg"
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
	>
		<path
			d="M12.0003 9.00043V12.7504M2.69702 16.1261C1.83163 17.6261 2.9142 19.5004 4.64593 19.5004H19.3546C21.0863 19.5004 22.1689 17.6261 21.3035 16.1261L13.9492 3.37855C13.0833 1.87772 10.9172 1.87772 10.0513 3.37855L2.69702 16.1261ZM12.0003 15.7504H12.0078V15.7579H12.0003V15.7504Z"
			stroke="url(#paint0_linear_17495_33324)"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<defs>
			<linearGradient
				id="paint0_linear_17495_33324"
				x1="12.0003"
				y1="2.25293"
				x2="12.0003"
				y2="19.5004"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
		</defs>
	</svg>
);

export const FrameUI = ( { className } ) => (
	<svg
		className={ classNames( className ) }
		width="390"
		height="281"
		viewBox="0 0 390 281"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g filter="url(#filter0_d_16917_26998)">
			<rect
				x="3"
				y="0.856934"
				width="384"
				height="274.286"
				rx="10.2857"
				fill="white"
			/>
			<path
				d="M29.1551 14.9322C29.6198 13.7957 31.2293 13.7957 31.694 14.9322L33.9831 20.5307C34.03 20.6455 34.0923 20.7534 34.1683 20.8515L37.8721 25.6331C38.624 26.6038 37.8193 27.9976 36.6027 27.8318L30.6098 27.015C30.4869 26.9983 30.3623 26.9983 30.2393 27.015L24.2464 27.8318C23.0298 27.9976 22.2251 26.6038 22.977 25.6331L26.6808 20.8515C26.7568 20.7534 26.8191 20.6455 26.866 20.5307L29.1551 14.9322Z"
				fill="url(#paint0_linear_16917_26998)"
			/>
			<rect
				x="133.281"
				y="20.7417"
				width="21.9429"
				height="4.11429"
				rx="1.37143"
				fill="url(#paint1_linear_16917_26998)"
			/>
			<rect
				x="163.453"
				y="20.7417"
				width="21.9429"
				height="4.11429"
				rx="1.37143"
				fill="url(#paint2_linear_16917_26998)"
			/>
			<rect
				x="193.625"
				y="20.7417"
				width="21.9429"
				height="4.11429"
				rx="1.37143"
				fill="url(#paint3_linear_16917_26998)"
			/>
			<rect
				x="223.795"
				y="20.7417"
				width="21.9429"
				height="4.11429"
				rx="1.37143"
				fill="url(#paint4_linear_16917_26998)"
			/>
			<rect
				x="337.625"
				y="17.3135"
				width="32.9143"
				height="10.9714"
				rx="3.42857"
				fill="url(#paint5_linear_16917_26998)"
			/>
			<rect
				x="41.4043"
				y="225.748"
				width="77.4919"
				height="2.76757"
				rx="1.38378"
				fill="url(#paint6_linear_16917_26998)"
			/>
			<rect
				x="41.4043"
				y="232.666"
				width="66.4216"
				height="2.76757"
				rx="1.38378"
				fill="url(#paint7_linear_16917_26998)"
			/>
			<rect
				x="41.4043"
				y="239.585"
				width="49.8162"
				height="2.76757"
				rx="1.38378"
				fill="url(#paint8_linear_16917_26998)"
			/>
			<path
				d="M46.4601 209.972C46.6731 209.603 47.2057 209.603 47.4188 209.972L49.3362 213.293L51.2536 216.615C51.4667 216.984 51.2004 217.445 50.7743 217.445H46.9394H43.1046C42.6785 217.445 42.4122 216.984 42.6252 216.615L44.5426 213.293L46.4601 209.972Z"
				fill="url(#paint9_linear_16917_26998)"
			/>
			<rect
				x="156.258"
				y="225.748"
				width="77.4919"
				height="2.76757"
				rx="1.38378"
				fill="url(#paint10_linear_16917_26998)"
			/>
			<rect
				x="156.258"
				y="232.666"
				width="66.4216"
				height="2.76757"
				rx="1.38378"
				fill="url(#paint11_linear_16917_26998)"
			/>
			<rect
				x="156.258"
				y="239.585"
				width="49.8162"
				height="2.76757"
				rx="1.38378"
				fill="url(#paint12_linear_16917_26998)"
			/>
			<path
				d="M161.307 210.03C161.517 209.646 162.069 209.646 162.279 210.03L163.672 212.578C163.723 212.671 163.799 212.747 163.892 212.798L166.44 214.192C166.824 214.402 166.824 214.953 166.44 215.163L163.892 216.556C163.799 216.607 163.723 216.684 163.672 216.776L162.279 219.324C162.069 219.708 161.517 219.708 161.307 219.324L159.914 216.776C159.863 216.684 159.787 216.607 159.694 216.556L157.146 215.163C156.762 214.953 156.762 214.402 157.146 214.192L159.694 212.798C159.787 212.747 159.863 212.671 159.914 212.578L161.307 210.03Z"
				fill="url(#paint13_linear_16917_26998)"
			/>
			<rect
				x="271.113"
				y="225.748"
				width="77.4919"
				height="2.76757"
				rx="1.38378"
				fill="url(#paint14_linear_16917_26998)"
			/>
			<rect
				x="271.113"
				y="232.666"
				width="66.4216"
				height="2.76757"
				rx="1.38378"
				fill="url(#paint15_linear_16917_26998)"
			/>
			<rect
				x="271.113"
				y="239.585"
				width="49.8162"
				height="2.76757"
				rx="1.38378"
				fill="url(#paint16_linear_16917_26998)"
			/>
			<path
				d="M276.152 210.148C276.355 209.736 276.942 209.736 277.145 210.148L278.146 212.177C278.227 212.341 278.383 212.454 278.563 212.48L280.803 212.805C281.257 212.871 281.438 213.429 281.11 213.75L279.489 215.329C279.358 215.456 279.299 215.64 279.33 215.819L279.712 218.05C279.79 218.502 279.315 218.847 278.909 218.633L276.906 217.58C276.745 217.495 276.552 217.495 276.391 217.58L274.388 218.633C273.982 218.847 273.507 218.502 273.585 218.05L273.967 215.819C273.998 215.64 273.938 215.456 273.808 215.329L272.187 213.75C271.859 213.429 272.04 212.871 272.494 212.805L274.734 212.48C274.914 212.454 275.07 212.341 275.15 212.177L276.152 210.148Z"
				fill="url(#paint17_linear_16917_26998)"
			/>
			<rect
				x="19.2852"
				y="44.5708"
				width="351.429"
				height="141.429"
				rx="10.9714"
				fill="url(#paint18_linear_16917_26998)"
			/>
			<path
				d="M46.7129 108.514C46.7129 107.757 47.3269 107.143 48.0843 107.143H229.113C229.87 107.143 230.484 107.757 230.484 108.514C230.484 109.271 229.87 109.885 229.113 109.885H48.0843C47.3269 109.885 46.7129 109.271 46.7129 108.514Z"
				fill="url(#paint19_linear_16917_26998)"
			/>
			<rect
				x="46.7129"
				y="115.372"
				width="112.457"
				height="2.74286"
				rx="1.37143"
				fill="url(#paint20_linear_16917_26998)"
			/>
			<rect
				x="46.7129"
				y="93.4282"
				width="87.7714"
				height="5.48571"
				rx="1.37143"
				fill="url(#paint21_linear_16917_26998)"
			/>
			<rect
				x="46.7129"
				y="126.343"
				width="32.9143"
				height="10.9714"
				rx="2.74286"
				fill="url(#paint22_linear_16917_26998)"
			/>
			<rect
				x="85.799"
				y="127.028"
				width="31.5429"
				height="9.6"
				rx="2.05714"
				stroke="url(#paint23_linear_16917_26998)"
				strokeWidth="1.37143"
			/>
			<path
				d="M128.1 67.7834C128.313 66.7509 129.788 66.7509 130.001 67.7834L130.415 69.789C130.597 70.6711 131.286 71.3604 132.168 71.5425L134.174 71.9565C135.206 72.1697 135.206 73.6445 134.174 73.8576L132.168 74.2717C131.286 74.4538 130.597 75.1431 130.415 76.0252L130.001 78.0308C129.788 79.0632 128.313 79.0632 128.1 78.0308L127.686 76.0252C127.503 75.1431 126.814 74.4538 125.932 74.2717L123.926 73.8576C122.894 73.6445 122.894 72.1697 123.926 71.9565L125.932 71.5425C126.814 71.3604 127.503 70.6711 127.686 69.789L128.1 67.7834Z"
				stroke="url(#paint24_linear_16917_26998)"
				strokeWidth="1.28571"
			/>
			<path
				d="M254.957 86.6403C255.17 85.6078 256.645 85.6078 256.858 86.6403L257.272 88.6459C257.454 89.528 258.144 90.2173 259.026 90.3994L261.031 90.8135C262.064 91.0266 262.064 92.5014 261.031 92.7146L259.026 93.1286C258.144 93.3107 257.454 94 257.272 94.8821L256.858 96.8877C256.645 97.9202 255.17 97.9202 254.957 96.8877L254.543 94.8821C254.361 94 253.672 93.3107 252.789 93.1286L250.784 92.7146C249.751 92.5014 249.751 91.0266 250.784 90.8135L252.789 90.3994C253.672 90.2173 254.361 89.528 254.543 88.6459L254.957 86.6403Z"
				stroke="url(#paint25_linear_16917_26998)"
				strokeWidth="1.28571"
			/>
			<rect
				opacity="0.5"
				x="233.57"
				y="66.856"
				width="75.1684"
				height="75.1684"
				fill="url(#paint26_linear_16917_26998)"
			/>
			<rect
				opacity="0.5"
				x="268.973"
				y="94.6309"
				width="75.1684"
				height="75.1684"
				rx="37.5842"
				fill="url(#paint27_linear_16917_26998)"
			/>
			<path
				d="M137.529 186.926C137.742 185.893 139.217 185.893 139.43 186.926L139.844 188.932C140.027 189.814 140.716 190.503 141.598 190.685L143.604 191.099C144.636 191.312 144.636 192.787 143.604 193L141.598 193.414C140.716 193.596 140.027 194.286 139.844 195.168L139.43 197.173C139.217 198.206 137.742 198.206 137.529 197.173L137.115 195.168C136.933 194.286 136.244 193.596 135.362 193.414L133.356 193C132.324 192.787 132.324 191.312 133.356 191.099L135.362 190.685C136.244 190.503 136.933 189.814 137.115 188.932L137.529 186.926Z"
				stroke="url(#paint28_linear_16917_26998)"
				strokeWidth="1.28571"
			/>
			<rect
				x="3.42857"
				y="1.28551"
				width="383.143"
				height="273.429"
				rx="9.85714"
				stroke="url(#paint29_linear_16917_26998)"
				strokeWidth="0.857143"
			/>
		</g>
		<defs>
			<filter
				id="filter0_d_16917_26998"
				x="0.257143"
				y="0.856934"
				width="389.486"
				height="279.771"
				filterUnits="userSpaceOnUse"
				colorInterpolationFilters="sRGB"
			>
				<feFlood floodOpacity="0" result="BackgroundImageFix" />
				<feColorMatrix
					in="SourceAlpha"
					type="matrix"
					values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
					result="hardAlpha"
				/>
				<feMorphology
					radius="2.74286"
					operator="erode"
					in="SourceAlpha"
					result="effect1_dropShadow_16917_26998"
				/>
				<feOffset dy="2.74286" />
				<feGaussianBlur stdDeviation="2.74286" />
				<feComposite in2="hardAlpha" operator="out" />
				<feColorMatrix
					type="matrix"
					values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
				/>
				<feBlend
					mode="normal"
					in2="BackgroundImageFix"
					result="effect1_dropShadow_16917_26998"
				/>
				<feBlend
					mode="normal"
					in="SourceGraphic"
					in2="effect1_dropShadow_16917_26998"
					result="shape"
				/>
			</filter>
			<linearGradient
				id="paint0_linear_16917_26998"
				x1="19.4531"
				y1="22.7988"
				x2="41.396"
				y2="22.7988"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint1_linear_16917_26998"
				x1="133.281"
				y1="22.7988"
				x2="155.224"
				y2="22.7988"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint2_linear_16917_26998"
				x1="163.453"
				y1="22.7988"
				x2="185.396"
				y2="22.7988"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint3_linear_16917_26998"
				x1="193.625"
				y1="22.7988"
				x2="215.568"
				y2="22.7988"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint4_linear_16917_26998"
				x1="223.795"
				y1="22.7988"
				x2="245.738"
				y2="22.7988"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint5_linear_16917_26998"
				x1="337.625"
				y1="22.7991"
				x2="370.539"
				y2="22.7991"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint6_linear_16917_26998"
				x1="41.4043"
				y1="227.131"
				x2="118.896"
				y2="227.131"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint7_linear_16917_26998"
				x1="41.4043"
				y1="234.05"
				x2="107.826"
				y2="234.05"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint8_linear_16917_26998"
				x1="41.4043"
				y1="240.969"
				x2="91.2205"
				y2="240.969"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint9_linear_16917_26998"
				x1="41.4043"
				y1="214.677"
				x2="52.4746"
				y2="214.677"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint10_linear_16917_26998"
				x1="156.258"
				y1="227.131"
				x2="233.75"
				y2="227.131"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint11_linear_16917_26998"
				x1="156.258"
				y1="234.049"
				x2="222.679"
				y2="234.049"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint12_linear_16917_26998"
				x1="156.258"
				y1="240.969"
				x2="206.074"
				y2="240.969"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint13_linear_16917_26998"
				x1="156.258"
				y1="214.677"
				x2="167.328"
				y2="214.677"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint14_linear_16917_26998"
				x1="271.113"
				y1="227.131"
				x2="348.605"
				y2="227.131"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint15_linear_16917_26998"
				x1="271.113"
				y1="234.05"
				x2="337.535"
				y2="234.05"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint16_linear_16917_26998"
				x1="271.113"
				y1="240.969"
				x2="320.929"
				y2="240.969"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint17_linear_16917_26998"
				x1="271.113"
				y1="214.677"
				x2="282.184"
				y2="214.677"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint18_linear_16917_26998"
				x1="19.2852"
				y1="115.283"
				x2="370.714"
				y2="115.283"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint19_linear_16917_26998"
				x1="46.7129"
				y1="108.514"
				x2="230.484"
				y2="108.514"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint20_linear_16917_26998"
				x1="46.7129"
				y1="116.743"
				x2="159.17"
				y2="116.743"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint21_linear_16917_26998"
				x1="46.7129"
				y1="96.171"
				x2="134.484"
				y2="96.171"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint22_linear_16917_26998"
				x1="46.7129"
				y1="131.828"
				x2="79.6272"
				y2="131.828"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint23_linear_16917_26998"
				x1="85.1133"
				y1="131.828"
				x2="118.028"
				y2="131.828"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint24_linear_16917_26998"
				x1="129.05"
				y1="59.9995"
				x2="129.05"
				y2="85.8146"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint25_linear_16917_26998"
				x1="255.908"
				y1="78.8564"
				x2="255.908"
				y2="104.672"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint26_linear_16917_26998"
				x1="233.57"
				y1="104.439"
				x2="308.739"
				y2="104.439"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint27_linear_16917_26998"
				x1="268.973"
				y1="132.214"
				x2="344.141"
				y2="132.214"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0.25" />
				<stop offset="1" stopColor="#D1DAE5" stopOpacity="0" />
			</linearGradient>
			<linearGradient
				id="paint28_linear_16917_26998"
				x1="138.48"
				y1="179.142"
				x2="138.48"
				y2="204.957"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint29_linear_16917_26998"
				x1="3"
				y1="0.856934"
				x2="387"
				y2="275.143"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
		</defs>
	</svg>
);

export const TilesIcon = ( { className } ) => (
	<svg
		className={ className }
		width="52"
		height="36"
		viewBox="0 0 52 36"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<g clipPath="url(#clip0_27794_45971)">
			<rect width="8" height="8" fill="#333E52" />
			<rect x="32" width="8" height="8" fill="#333E52" />
			<rect x="16" width="8" height="8" fill="#333E52" />
			<rect x="48" width="8" height="8" fill="#333E52" />
			<rect y="16" width="8" height="8" fill="#333E52" />
			<rect x="32" y="16" width="8" height="8" fill="#333E52" />
			<rect x="16" y="16" width="8" height="8" fill="#333E52" />
			<rect x="48" y="16" width="8" height="8" fill="#333E52" />
			<rect x="8" y="8" width="8" height="8" fill="#333E52" />
			<rect x="8" y="24" width="8" height="8" fill="#333E52" />
			<rect x="24" y="8" width="8" height="8" fill="#333E52" />
			<rect x="40" y="8" width="8" height="8" fill="#333E52" />
			<rect x="24" y="24" width="8" height="8" fill="#333E52" />
			<rect x="40" y="24" width="8" height="8" fill="#333E52" />
			<rect y="32" width="8" height="8" fill="#333E52" />
			<rect x="32" y="32" width="8" height="8" fill="#333E52" />
			<rect x="16" y="32" width="8" height="8" fill="#333E52" />
			<rect x="48" y="32" width="8" height="8" fill="#333E52" />
		</g>
		<defs>
			<clipPath id="clip0_27794_45971">
				<path
					d="M0 2C0 0.895431 0.895431 0 2 0H70C71.1046 0 72 0.895431 72 2V42C72 43.1046 71.1046 44 70 44H2C0.895431 44 0 43.1046 0 42V2Z"
					fill="white"
				/>
			</clipPath>
		</defs>
	</svg>
);

export const SirenColorfulIcon = ( { className } ) => (
	<svg
		className={ className }
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			d="M7 12C7 10.6739 7.52678 9.40215 8.46447 8.46447C9.40215 7.52678 10.6739 7 12 7C13.3261 7 14.5979 7.52678 15.5355 8.46447C16.4732 9.40215 17 10.6739 17 12V18H7V12Z"
			stroke="url(#paint0_linear_1547_11043)"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M5 20C5 19.4696 5.21071 18.9609 5.58579 18.5858C5.96086 18.2107 6.46957 18 7 18H17C17.5304 18 18.0391 18.2107 18.4142 18.5858C18.7893 18.9609 19 19.4696 19 20V22H5V20Z"
			stroke="url(#paint1_linear_1547_11043)"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M21 12H22"
			stroke="url(#paint2_linear_1547_11043)"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M18.5 4.5L18 5"
			stroke="url(#paint3_linear_1547_11043)"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M2 12H3"
			stroke="url(#paint4_linear_1547_11043)"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M12 2V3"
			stroke="url(#paint5_linear_1547_11043)"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M4.92896 4.929L5.63596 5.636"
			stroke="url(#paint6_linear_1547_11043)"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M12 12V18"
			stroke="url(#paint7_linear_1547_11043)"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<defs>
			<linearGradient
				id="paint0_linear_1547_11043"
				x1="7"
				y1="12.5"
				x2="17"
				y2="12.5"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint1_linear_1547_11043"
				x1="5"
				y1="20"
				x2="19"
				y2="20"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint2_linear_1547_11043"
				x1="21"
				y1="12.5"
				x2="22"
				y2="12.5"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint3_linear_1547_11043"
				x1="18"
				y1="4.75"
				x2="18.5"
				y2="4.75"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint4_linear_1547_11043"
				x1="2"
				y1="12.5"
				x2="3"
				y2="12.5"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint5_linear_1547_11043"
				x1="12"
				y1="2.5"
				x2="13"
				y2="2.5"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint6_linear_1547_11043"
				x1="4.92896"
				y1="5.2825"
				x2="5.63596"
				y2="5.2825"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint7_linear_1547_11043"
				x1="12"
				y1="15"
				x2="13"
				y2="15"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
		</defs>
	</svg>
);
export const GemIcon = ( { className } ) => (
	<svg
		className={ className }
		width="14"
		height="11"
		viewBox="0 0 14 11"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			d="M12.8828 4.11719C13.0469 4.32812 13.0234 4.63281 12.8359 4.82031L7.39844 10.5859C7.28125 10.7031 7.14062 10.75 7 10.75C6.83594 10.75 6.69531 10.7031 6.57812 10.5859L1.16406 4.82031C0.976562 4.63281 0.953125 4.32812 1.11719 4.11719L3.53125 0.507812C3.625 0.34375 3.8125 0.25 3.97656 0.25H10C10.1641 0.25 10.3516 0.34375 10.4453 0.507812L12.8828 4.11719ZM9.95312 1.77344L8.17188 4H11.4531L9.95312 1.77344ZM7 3.69531L8.82812 1.375H5.14844L7 3.69531ZM4.02344 1.77344L2.52344 4H5.80469L4.02344 1.77344ZM7 9.36719L10.9844 5.125H2.99219L7 9.36719Z"
			fill="white"
		/>
	</svg>
);
export const PremiumAiIcon = ( { className } ) => (
	<svg
		className={ className }
		width="22"
		height="22"
		viewBox="0 0 22 22"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			d="M8.8132 14.9038L8 17.75L7.1868 14.9038C6.75968 13.4089 5.59112 12.2403 4.09619 11.8132L1.25 11L4.09619 10.1868C5.59113 9.75968 6.75968 8.59112 7.1868 7.09619L8 4.25L8.8132 7.09619C9.24032 8.59113 10.4089 9.75968 11.9038 10.1868L14.75 11L11.9038 11.8132C10.4089 12.2403 9.24032 13.4089 8.8132 14.9038Z"
			stroke="url(#paint0_linear_33314_28225)"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M17.2589 7.71454L17 8.75L16.7411 7.71454C16.4388 6.50533 15.4947 5.56117 14.2855 5.25887L13.25 5L14.2855 4.74113C15.4947 4.43883 16.4388 3.49467 16.7411 2.28546L17 1.25L17.2589 2.28546C17.5612 3.49467 18.5053 4.43883 19.7145 4.74113L20.75 5L19.7145 5.25887C18.5053 5.56117 17.5612 6.50533 17.2589 7.71454Z"
			stroke="url(#paint1_linear_33314_28225)"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M15.8942 19.5673L15.5 20.75L15.1058 19.5673C14.8818 18.8954 14.3546 18.3682 13.6827 18.1442L12.5 17.75L13.6827 17.3558C14.3546 17.1318 14.8818 16.6046 15.1058 15.9327L15.5 14.75L15.8942 15.9327C16.1182 16.6046 16.6454 17.1318 17.3173 17.3558L18.5 17.75L17.3173 18.1442C16.6454 18.3682 16.1182 18.8954 15.8942 19.5673Z"
			stroke="url(#paint2_linear_33314_28225)"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<defs>
			<linearGradient
				id="paint0_linear_33314_28225"
				x1="1.25"
				y1="11"
				x2="20.75"
				y2="11"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint1_linear_33314_28225"
				x1="1.25"
				y1="11"
				x2="20.75"
				y2="11"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="paint2_linear_33314_28225"
				x1="1.25"
				y1="11"
				x2="20.75"
				y2="11"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset="0.46875" stopColor="#E90B76" />
				<stop offset="1" stopColor="#FC8536" />
			</linearGradient>
		</defs>
	</svg>
);
