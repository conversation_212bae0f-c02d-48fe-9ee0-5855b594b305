export const Graphics = ( props ) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width={ 480 }
		height={ 388 }
		fill="none"
		{ ...props }
	>
		<g clipPath="url(#a)">
			<g filter="url(#b)" opacity={ 0.5 }>
				<rect
					width={ 331 }
					height={ 275 }
					x={ 77 }
					y={ 58 }
					fill="url(#c)"
					rx={ 34.286 }
				/>
			</g>
			<g filter="url(#d)">
				<rect
					width={ 432 }
					height={ 308.571 }
					x={ 24 }
					y={ 39.143 }
					fill="#fff"
					rx={ 11.571 }
				/>
				<rect
					width={ 431.036 }
					height={ 307.607 }
					x={ 24.482 }
					y={ 39.625 }
					stroke="url(#e)"
					strokeWidth={ 0.964 }
					rx={ 11.089 }
				/>
				<path
					fill="url(#f)"
					d="M53.373 56.51c.435-1.475 2.525-1.475 2.96 0l1.092 3.707c.149.502.542.895 1.044 1.043l3.707 1.093c1.475.435 1.475 2.525 0 2.96l-3.707 1.093a1.543 1.543 0 0 0-1.044 1.043l-1.092 3.707c-.435 1.476-2.525 1.476-2.96 0L52.28 67.45a1.543 1.543 0 0 0-1.044-1.043l-3.706-1.093c-1.476-.435-1.476-2.525 0-2.96l3.706-1.093a1.543 1.543 0 0 0 1.044-1.043l1.093-3.707Z"
				/>
				<rect
					width={ 24.686 }
					height={ 4.629 }
					x={ 170.567 }
					y={ 61.52 }
					fill="url(#g)"
					rx={ 1.543 }
				/>
				<rect
					width={ 24.686 }
					height={ 4.629 }
					x={ 204.51 }
					y={ 61.52 }
					fill="url(#h)"
					rx={ 1.543 }
				/>
				<rect
					width={ 24.686 }
					height={ 4.629 }
					x={ 238.453 }
					y={ 61.52 }
					fill="url(#i)"
					rx={ 1.543 }
				/>
				<rect
					width={ 24.686 }
					height={ 4.629 }
					x={ 272.396 }
					y={ 61.52 }
					fill="url(#j)"
					rx={ 1.543 }
				/>
				<rect
					width={ 37.029 }
					height={ 12.343 }
					x={ 400.452 }
					y={ 57.662 }
					fill="url(#k)"
					rx={ 3.857 }
				/>
				<rect
					width={ 87.178 }
					height={ 3.114 }
					x={ 67.206 }
					y={ 292.146 }
					fill="url(#l)"
					rx={ 1.557 }
				/>
				<rect
					width={ 74.724 }
					height={ 3.114 }
					x={ 67.206 }
					y={ 299.928 }
					fill="url(#m)"
					rx={ 1.557 }
				/>
				<rect
					width={ 56.043 }
					height={ 3.114 }
					x={ 67.206 }
					y={ 307.717 }
					fill="url(#n)"
					rx={ 1.557 }
				/>
				<path
					fill="url(#o)"
					d="M72.894 274.401a.623.623 0 0 1 1.078 0l2.157 3.736 2.158 3.736a.623.623 0 0 1-.54.934H69.12a.623.623 0 0 1-.54-.934l2.158-3.736 2.157-3.736Z"
				/>
				<rect
					width={ 87.178 }
					height={ 3.114 }
					x={ 196.417 }
					y={ 292.146 }
					fill="url(#p)"
					rx={ 1.557 }
				/>
				<rect
					width={ 74.724 }
					height={ 3.114 }
					x={ 196.417 }
					y={ 299.928 }
					fill="url(#q)"
					rx={ 1.557 }
				/>
				<rect
					width={ 56.043 }
					height={ 3.114 }
					x={ 196.417 }
					y={ 307.717 }
					fill="url(#r)"
					rx={ 1.557 }
				/>
				<path
					fill="url(#s)"
					d="M202.098 274.466a.622.622 0 0 1 1.092 0l1.568 2.866a.617.617 0 0 0 .248.248l2.866 1.567a.623.623 0 0 1 0 1.093l-2.866 1.568a.62.62 0 0 0-.248.247l-1.568 2.867a.622.622 0 0 1-1.092 0l-1.568-2.867a.62.62 0 0 0-.248-.247l-2.866-1.568a.623.623 0 0 1 0-1.093l2.866-1.567a.617.617 0 0 0 .248-.248l1.568-2.866Z"
				/>
				<rect
					width={ 87.178 }
					height={ 3.114 }
					x={ 325.628 }
					y={ 292.146 }
					fill="url(#t)"
					rx={ 1.557 }
				/>
				<rect
					width={ 74.724 }
					height={ 3.114 }
					x={ 325.628 }
					y={ 299.928 }
					fill="url(#u)"
					rx={ 1.557 }
				/>
				<rect
					width={ 56.043 }
					height={ 3.114 }
					x={ 325.628 }
					y={ 307.717 }
					fill="url(#v)"
					rx={ 1.557 }
				/>
				<path
					fill="url(#w)"
					d="M331.297 274.598a.622.622 0 0 1 1.116 0l1.127 2.283a.622.622 0 0 0 .469.341l2.52.366c.51.074.714.702.345 1.062l-1.823 1.777a.626.626 0 0 0-.18.552l.431 2.509a.623.623 0 0 1-.904.656l-2.253-1.184a.62.62 0 0 0-.58 0l-2.253 1.184a.623.623 0 0 1-.904-.656l.43-2.509a.623.623 0 0 0-.179-.552l-1.823-1.777a.623.623 0 0 1 .345-1.062l2.52-.366a.622.622 0 0 0 .469-.341l1.127-2.283Z"
				/>
				<rect
					width={ 395.357 }
					height={ 159.107 }
					x={ 42.323 }
					y={ 88.324 }
					fill="url(#x)"
					rx={ 12.343 }
				/>
				<path
					fill="url(#y)"
					d="M73 160.543c0-.852.69-1.543 1.543-1.543h147.914c.852 0 1.543.691 1.543 1.543v.914c0 .852-.691 1.543-1.543 1.543H74.543A1.543 1.543 0 0 1 73 161.457v-.914Z"
				/>
				<rect
					width={ 126.514 }
					height={ 4 }
					x={ 73.178 }
					y={ 167.98 }
					fill="url(#z)"
					rx={ 1.543 }
				/>
				<rect
					width={ 98.743 }
					height={ 8 }
					x={ 73.178 }
					y={ 143.295 }
					fill="url(#A)"
					rx={ 1.543 }
				/>
				<rect
					width={ 37.029 }
					height={ 12.343 }
					x={ 73.178 }
					y={ 180.324 }
					fill="url(#B)"
					rx={ 3.086 }
				/>
				<rect
					width={ 35.486 }
					height={ 10.8 }
					x={ 117.149 }
					y={ 181.096 }
					stroke="url(#C)"
					strokeWidth={ 1.543 }
					rx={ 2.314 }
				/>
				<path
					fill="url(#D)"
					d="M283.393 113.396h84.564v84.564h-84.564z"
					opacity={ 0.5 }
				/>
				<rect
					width={ 84.564 }
					height={ 84.564 }
					x={ 323.22 }
					y={ 144.643 }
					fill="url(#E)"
					opacity={ 0.5 }
					rx={ 42.282 }
				/>
			</g>
			<rect
				width={ 118.178 }
				height={ 68.363 }
				x={ 48.5 }
				y={ 260.5 }
				fill="#fff"
				fillOpacity={ 0.32 }
				rx={ 5.5 }
			/>
			<rect
				width={ 118.178 }
				height={ 68.363 }
				x={ 48.5 }
				y={ 260.5 }
				stroke="#D8DFE9"
				strokeDasharray="4 4"
				rx={ 5.5 }
			/>
			<rect
				width={ 87.178 }
				height={ 3.114 }
				x={ 64 }
				y={ 294.68 }
				fill="url(#F)"
				rx={ 1.557 }
			/>
			<rect
				width={ 74.724 }
				height={ 3.114 }
				x={ 64 }
				y={ 302.461 }
				fill="url(#G)"
				rx={ 1.557 }
			/>
			<rect
				width={ 56.043 }
				height={ 3.114 }
				x={ 64 }
				y={ 310.25 }
				fill="url(#H)"
				rx={ 1.557 }
			/>
			<path
				fill="url(#I)"
				d="M69.688 276.934a.623.623 0 0 1 1.078 0l2.157 3.736 2.157 3.736a.623.623 0 0 1-.539.935h-8.628a.623.623 0 0 1-.54-.935l2.158-3.736 2.157-3.736Z"
			/>
			<g filter="url(#J)">
				<rect
					width={ 119.178 }
					height={ 69.363 }
					x={ 38 }
					y={ 250 }
					fill="#fff"
					fillOpacity={ 0.32 }
					rx={ 6 }
					shapeRendering="crispEdges"
				/>
				<rect
					width={ 87.178 }
					height={ 3.114 }
					x={ 54 }
					y={ 284.68 }
					fill="url(#K)"
					rx={ 1.557 }
				/>
				<rect
					width={ 74.724 }
					height={ 3.114 }
					x={ 54 }
					y={ 292.461 }
					fill="url(#L)"
					rx={ 1.557 }
				/>
				<rect
					width={ 56.043 }
					height={ 3.114 }
					x={ 54 }
					y={ 300.25 }
					fill="url(#M)"
					rx={ 1.557 }
				/>
				<path
					fill="url(#N)"
					d="M59.688 266.934a.623.623 0 0 1 1.078 0l2.157 3.736 2.157 3.736a.623.623 0 0 1-.539.935h-8.628a.623.623 0 0 1-.54-.935l2.158-3.736 2.157-3.736Z"
				/>
			</g>
			<g filter="url(#O)">
				<rect
					width={ 123.429 }
					height={ 123.429 }
					x={ 178.285 }
					y={ 131.715 }
					fill="#fff"
					fillOpacity={ 0.25 }
					rx={ 61.714 }
					shapeRendering="crispEdges"
					style={ {
						mixBlendMode: 'overlay',
					} }
				/>
				<path
					fill="url(#P)"
					d="M204.734 193.43a2.94 2.94 0 1 1-5.878 0 2.94 2.94 0 0 1 5.878 0Z"
				/>
				<path
					fill="url(#Q)"
					d="M281.142 193.43a2.94 2.94 0 1 1-5.878 0 2.94 2.94 0 0 1 5.878 0Z"
				/>
				<path
					fill="url(#R)"
					d="M239.999 158.165a2.938 2.938 0 1 1 0-5.877 2.938 2.938 0 0 1 0 5.877Z"
				/>
				<path
					fill="url(#S)"
					d="M239.999 234.573a2.938 2.938 0 1 1 0-5.877 2.938 2.938 0 0 1 0 5.877Z"
				/>
				<path
					fill="url(#T)"
					d="M222.369 162.889a2.94 2.94 0 1 1-2.94-5.09 2.94 2.94 0 0 1 2.94 5.09Z"
				/>
				<path
					fill="url(#U)"
					d="M260.573 229.061a2.94 2.94 0 1 1-2.939-5.092 2.94 2.94 0 0 1 2.939 5.092Z"
				/>
				<path
					fill="url(#V)"
					d="M270.542 175.797a2.938 2.938 0 1 1 5.091-2.939 2.938 2.938 0 0 1-5.091 2.939Z"
				/>
				<path
					fill="url(#W)"
					d="M204.37 214.001a2.94 2.94 0 1 1 5.092-2.937A2.94 2.94 0 0 1 204.37 214Z"
				/>
				<path
					fill="url(#X)"
					d="M209.46 175.797a2.94 2.94 0 1 1-5.091-2.94 2.94 2.94 0 0 1 5.091 2.94Z"
				/>
				<path
					fill="url(#Y)"
					d="M275.631 214.001a2.938 2.938 0 1 1-5.09-2.938 2.938 2.938 0 0 1 5.09 2.938Z"
				/>
				<path
					fill="url(#Z)"
					d="M257.633 162.889a2.938 2.938 0 1 1 2.938-5.09 2.938 2.938 0 0 1-2.938 5.09Z"
				/>
				<path
					fill="url(#aa)"
					d="M219.429 229.061a2.94 2.94 0 1 1 2.94-5.091 2.94 2.94 0 0 1-2.94 5.091Z"
				/>
				<path
					stroke="url(#ab)"
					strokeLinecap="round"
					strokeLinejoin="round"
					strokeWidth={ 3.429 }
					d="m245.142 183.147 5.143 5.143m-20.572 20.571 25.714-25.714-5.142-5.143-25.715 25.714 5.143 5.143Zm5.143-30.857a3.428 3.428 0 0 0 3.429 3.428 3.43 3.43 0 0 0-3.429 3.429 3.428 3.428 0 0 0-3.429-3.429 3.429 3.429 0 0 0 3.429-3.428Zm17.143 17.143a3.428 3.428 0 0 0 3.428 3.428 3.429 3.429 0 0 0-3.428 3.429 3.428 3.428 0 0 0-3.429-3.429 3.429 3.429 0 0 0 3.429-3.428Z"
				/>
			</g>
			<rect
				width={ 197.143 }
				height={ 60 }
				x={ 141.427 }
				y={ 312.287 }
				fill="#fff"
				rx={ 30 }
			/>
			<rect
				width={ 197.143 }
				height={ 60 }
				x={ 141.427 }
				y={ 312.287 }
				stroke="#E2E8F0"
				strokeWidth={ 1.714 }
				rx={ 30 }
			/>
			<path
				stroke="url(#ac)"
				strokeLinecap="round"
				strokeLinejoin="round"
				strokeWidth={ 1.714 }
				d="m176.984 333.701 1.928-1.929a2.143 2.143 0 1 1 3.031 3.031l-12.136 12.135a5.132 5.132 0 0 1-2.168 1.292l-3.069.915.914-3.069a5.142 5.142 0 0 1 1.292-2.168l10.208-10.207Zm0 0 3.015 3.015m-1.715 7.857v5.429a2.57 2.57 0 0 1-2.571 2.571h-12a2.57 2.57 0 0 1-2.571-2.571v-12a2.572 2.572 0 0 1 2.571-2.572h5.429"
			/>
			<path
				stroke="url(#ad)"
				strokeLinecap="round"
				strokeLinejoin="round"
				strokeWidth={ 1.714 }
				d="M210.284 352.571a4.571 4.571 0 0 1-4.571-4.571v-13.714a2.285 2.285 0 0 1 2.286-2.286h4.571a2.286 2.286 0 0 1 2.286 2.286V348a4.571 4.571 0 0 1-4.572 4.571Zm0 0h13.715a2.285 2.285 0 0 0 2.285-2.285v-4.572a2.285 2.285 0 0 0-2.285-2.285h-2.678m-6.465-6.465 1.893-1.894a2.286 2.286 0 0 1 3.233 0l3.232 3.233a2.284 2.284 0 0 1 0 3.232l-9.697 9.697M210.284 348h.012"
			/>
			<path
				stroke="url(#ae)"
				strokeLinecap="round"
				strokeLinejoin="round"
				strokeWidth={ 2 }
				d="m258.071 346.749-.929 3.253-.929-3.253a5.142 5.142 0 0 0-3.532-3.532l-3.253-.93 3.253-.929a5.144 5.144 0 0 0 3.532-3.532l.929-3.253.929 3.253a5.145 5.145 0 0 0 3.533 3.532l3.252.929-3.252.93a5.143 5.143 0 0 0-3.533 3.532Z"
			/>
			<path
				stroke="url(#af)"
				strokeLinecap="round"
				strokeLinejoin="round"
				strokeWidth={ 2 }
				d="m267.724 338.533-.296 1.183-.296-1.183a3.86 3.86 0 0 0-2.807-2.807l-1.183-.296 1.183-.296a3.857 3.857 0 0 0 2.807-2.806l.296-1.183.296 1.183a3.854 3.854 0 0 0 2.806 2.806l1.183.296-1.183.296a3.857 3.857 0 0 0-2.806 2.807Z"
			/>
			<path
				stroke="url(#ag)"
				strokeLinecap="round"
				strokeLinejoin="round"
				strokeWidth={ 2 }
				d="m266.164 352.079-.451 1.351-.45-1.351a2.574 2.574 0 0 0-1.626-1.627l-1.352-.45 1.352-.451a2.572 2.572 0 0 0 1.626-1.626l.45-1.352.451 1.352a2.57 2.57 0 0 0 1.626 1.626l1.352.451-1.352.45a2.572 2.572 0 0 0-1.626 1.627Z"
			/>
			<path
				stroke="url(#ah)"
				strokeLinecap="round"
				strokeLinejoin="round"
				strokeWidth={ 1.714 }
				d="M309.245 344.996a6.857 6.857 0 0 1-6.675 8.434v-5.485m6.675-2.949c4.268-3.119 7.04-8.161 7.04-13.851-5.69 0-10.732 2.772-13.85 7.039m6.81 6.812a17.07 17.07 0 0 1-6.675 2.949m-.135-9.761a6.857 6.857 0 0 0-8.436 6.675h5.487m2.949-6.675a17.047 17.047 0 0 0-2.949 6.675m3.084 3.086c-.118.024-.236.047-.355.068a17.256 17.256 0 0 1-2.798-2.797c.022-.12.045-.239.069-.357m-2.559 2.732a5.135 5.135 0 0 0-2.01 4.921 5.134 5.134 0 0 0 4.921-2.01m10.447-11.643a1.714 1.714 0 1 1-3.429 0 1.714 1.714 0 0 1 3.429 0Z"
			/>
			<g filter="url(#ai)">
				<rect
					width={ 62 }
					height={ 68 }
					y={ 120 }
					fill="#fff"
					fillOpacity={ 0.32 }
					rx={ 6 }
					shapeRendering="crispEdges"
				/>
				<path
					fill="url(#aj)"
					d="m32.83 165-8.04-21h4.11l8.28 21h-4.35Zm-16.59 0 8.28-21h4.11l-8.04 21h-4.35Zm3.9-4.62v-3.63h13.17v3.63H20.14ZM39.366 165v-15h3.96v15h-3.96Zm1.98-17.43c-.6 0-1.13-.22-1.59-.66-.46-.46-.69-1-.69-1.62 0-.62.23-1.15.69-1.59.46-.46.99-.69 1.59-.69.62 0 1.15.23 1.59.69.46.44.69.97.69 1.59 0 .62-.23 1.16-.69 1.62-.44.44-.97.66-1.59.66Z"
				/>
			</g>
			<g filter="url(#ak)">
				<rect
					width={ 107 }
					height={ 56 }
					x={ 369 }
					y={ 85 }
					fill="#fff"
					fillOpacity={ 0.32 }
					rx={ 6 }
					shapeRendering="crispEdges"
				/>
				<path
					fill="#111827"
					fillRule="evenodd"
					d="M407.731 102.875v20.25h5.109v-6.321c1.054-.134 2.054-.322 2.973-.59.946-.268 1.784-.669 2.487-1.205.73-.536 1.271-1.205 1.703-2.036.406-.857.622-1.875.622-3.134 0-.991-.135-1.848-.378-2.571-.271-.75-.595-1.366-1.055-1.875a5.468 5.468 0 0 0-1.568-1.259 9.184 9.184 0 0 0-1.973-.777c-.676-.187-1.433-.294-2.217-.375a23.103 23.103 0 0 0-2.351-.107h-3.352Zm6.163 8.893c-.243.107-.568.134-.919.134v-4.554h.432c.325 0 .595.081.811.214.217.134.406.295.514.536.135.215.216.456.27.75.081.268.081.536.081.857 0 .322-.054.616-.135.884a1.735 1.735 0 0 1-.405.723 1.655 1.655 0 0 1-.649.456Z"
					clipRule="evenodd"
				/>
				<path
					fill="#111827"
					d="M405.532 103.063v20.009h-4.92v-20.009h4.92ZM391.353 107.723h-5.191v-4.607h12.787l-6.704 15.482h5.757v4.473H385l6.353-15.321v-.027Z"
				/>
				<path
					fill="#FF580E"
					d="m459.841 106.257-.013.002-.013.001.026-.003ZM446.324 111.592c.073.072.073.265.073.265l-.193 1.086a.46.46 0 0 1-.169.265c-.096.072-.289.096-.289.096h-2.725c-.072 0-.072-.072-.072-.072l.289-1.64c.024-.073.097-.097.097-.097h2.748c.097 0 .193.024.241.097Z"
				/>
				<path
					fill="#FF580E"
					fillRule="evenodd"
					d="M459.828 106.259c.123-.**************.205-.443 1.07-2.731 5.527-10.633 9.248-.153.067-.108.308.049.296l6.639.03c.***************.126.263-1.449 1.693-10.226 11.097-23.151 8.121-5.096-1.185-8.622-5.899-8.643-11.594-.02-5.695 3.406-10.077 9.182-10.989 6.528-1.052 17.475-.839 19.217-.784.105-.***************.209-.373.628-1.722 2.587-5.381 5.324-.101.062-.058.277.073.267 1.268.065 5.508.117 12.24-.596Zm-19.339 3.428s.145.024.193.096a.336.336 0 0 1 0 .217l-3.737 6.707a.44.44 0 0 1-.434.265h-1.471s-.169 0-.241-.072a.439.439 0 0 1-.121-.193l-.651-3.329s-.024-.073-.096-.073c-.072 0-.096 0-.096.049l-1.905 3.377a.509.509 0 0 1-.434.265h-1.471s-.169 0-.217-.072c-.072-.048-.097-.121-.121-.193l-1.278-6.707s0-.144.073-.217a.294.294 0 0 1 .217-.096h1.736s.169 0 .217.072c.014.022.029.041.042.06a.447.447 0 0 1 .078.133l.675 3.402s.025.072.097.072c.072 0 .096 0 .12-.048l1.905-3.45a.48.48 0 0 1 .434-.265h1.206s.169 0 .241.072c.***************.121.193l.651 3.402s.048.072.096.096c.048.024.097 0 .097-.048l1.928-3.45a.481.481 0 0 1 .434-.265h1.712Zm7.909 3.545.289-1.665.024-.024c.097-.53-.024-.989-.337-1.351-.314-.361-.748-.554-1.278-.554h-5.281a.508.508 0 0 0-.313.12c-.096.073-.169.29-.169.29l-1.181 6.513c-.024.121 0 .**************.072.265.12.265.12h1.399c.12 0 .217-.048.313-.12.097-.073.169-.29.169-.29l.241-1.351c0-.048.097-.072.097-.072h3.375c.555 0 1.037-.193 1.495-.555.434-.362.724-.82.82-1.351Z"
					clipRule="evenodd"
				/>
			</g>
			<g filter="url(#al)">
				<rect
					width={ 98 }
					height={ 94 }
					x={ 382 }
					y={ 214 }
					fill="#fff"
					fillOpacity={ 0.32 }
					rx={ 6 }
					shapeRendering="crispEdges"
				/>
				<path
					fill="url(#am)"
					d="M388 222a2 2 0 0 1 2-2h82a2 2 0 0 1 2 2v60a2 2 0 0 1-2 2h-82a2 2 0 0 1-2-2v-60Z"
				/>
				<circle cx={ 394 } cy={ 296 } r={ 6 } fill="#BD09A1" />
				<circle cx={ 412.5 } cy={ 296 } r={ 6 } fill="#FB7D3A" />
				<circle cx={ 431 } cy={ 296 } r={ 6 } fill="#0F172A" />
				<circle cx={ 449.5 } cy={ 296 } r={ 6 } fill="#475569" />
				<circle cx={ 468 } cy={ 296 } r={ 6 } fill="#D9D9D9" />
			</g>
		</g>
		<defs>
			<linearGradient
				id="c"
				x1={ 77 }
				x2={ 408 }
				y1={ 222.993 }
				y2={ 222.993 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#0A21F8" />
				<stop offset={ 0.427 } stopColor="#93F" />
				<stop offset={ 1 } stopColor="#FC65D2" />
			</linearGradient>
			<linearGradient
				id="e"
				x1={ 24 }
				x2={ 456 }
				y1={ 39.143 }
				y2={ 347.714 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="f"
				x1={ 42.51 }
				x2={ 67.195 }
				y1={ 63.833 }
				y2={ 63.833 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="g"
				x1={ 170.567 }
				x2={ 195.253 }
				y1={ 63.834 }
				y2={ 63.834 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="h"
				x1={ 204.51 }
				x2={ 229.195 }
				y1={ 63.834 }
				y2={ 63.834 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="i"
				x1={ 238.453 }
				x2={ 263.139 }
				y1={ 63.834 }
				y2={ 63.834 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="j"
				x1={ 272.396 }
				x2={ 297.081 }
				y1={ 63.834 }
				y2={ 63.834 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="k"
				x1={ 400.452 }
				x2={ 437.481 }
				y1={ 63.833 }
				y2={ 63.833 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="l"
				x1={ 67.206 }
				x2={ 154.384 }
				y1={ 293.703 }
				y2={ 293.703 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="m"
				x1={ 67.206 }
				x2={ 141.93 }
				y1={ 301.484 }
				y2={ 301.484 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="n"
				x1={ 67.206 }
				x2={ 123.249 }
				y1={ 309.274 }
				y2={ 309.274 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="o"
				x1={ 67.206 }
				x2={ 79.66 }
				y1={ 279.694 }
				y2={ 279.694 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="p"
				x1={ 196.417 }
				x2={ 283.595 }
				y1={ 293.703 }
				y2={ 293.703 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="q"
				x1={ 196.417 }
				x2={ 271.141 }
				y1={ 301.484 }
				y2={ 301.484 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="r"
				x1={ 196.417 }
				x2={ 252.46 }
				y1={ 309.274 }
				y2={ 309.274 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="s"
				x1={ 196.417 }
				x2={ 208.871 }
				y1={ 279.694 }
				y2={ 279.694 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="t"
				x1={ 325.628 }
				x2={ 412.806 }
				y1={ 293.703 }
				y2={ 293.703 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="u"
				x1={ 325.628 }
				x2={ 400.352 }
				y1={ 301.484 }
				y2={ 301.484 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="v"
				x1={ 325.628 }
				x2={ 381.671 }
				y1={ 309.274 }
				y2={ 309.274 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="w"
				x1={ 325.628 }
				x2={ 338.082 }
				y1={ 279.694 }
				y2={ 279.694 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="x"
				x1={ 42.323 }
				x2={ 437.68 }
				y1={ 167.876 }
				y2={ 167.876 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="y"
				x1={ 73 }
				x2={ 224 }
				y1={ 161 }
				y2={ 161 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="z"
				x1={ 73.178 }
				x2={ 199.692 }
				y1={ 169.98 }
				y2={ 169.98 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="A"
				x1={ 73.178 }
				x2={ 171.921 }
				y1={ 147.295 }
				y2={ 147.295 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="B"
				x1={ 73.178 }
				x2={ 110.206 }
				y1={ 186.496 }
				y2={ 186.496 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="C"
				x1={ 116.378 }
				x2={ 153.406 }
				y1={ 186.496 }
				y2={ 186.496 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="D"
				x1={ 283.393 }
				x2={ 367.957 }
				y1={ 155.678 }
				y2={ 155.678 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="E"
				x1={ 323.22 }
				x2={ 407.784 }
				y1={ 186.924 }
				y2={ 186.924 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="F"
				x1={ 64 }
				x2={ 151.178 }
				y1={ 296.236 }
				y2={ 296.236 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="G"
				x1={ 64 }
				x2={ 138.724 }
				y1={ 304.018 }
				y2={ 304.018 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="H"
				x1={ 64 }
				x2={ 120.043 }
				y1={ 311.807 }
				y2={ 311.807 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="I"
				x1={ 64 }
				x2={ 76.454 }
				y1={ 282.227 }
				y2={ 282.227 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="K"
				x1={ 54 }
				x2={ 141.178 }
				y1={ 286.236 }
				y2={ 286.236 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="L"
				x1={ 54 }
				x2={ 128.724 }
				y1={ 294.018 }
				y2={ 294.018 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="M"
				x1={ 54 }
				x2={ 110.043 }
				y1={ 301.807 }
				y2={ 301.807 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="N"
				x1={ 54 }
				x2={ 66.454 }
				y1={ 272.227 }
				y2={ 272.227 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#D1DAE5" />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0.25 } />
				<stop offset={ 1 } stopColor="#D1DAE5" stopOpacity={ 0 } />
			</linearGradient>
			<linearGradient
				id="ab"
				x1={ 224.57 }
				x2={ 255.427 }
				y1={ 193.432 }
				y2={ 193.432 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="ac"
				x1={ 161.142 }
				x2={ 182.57 }
				y1={ 341.859 }
				y2={ 341.859 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="ad"
				x1={ 205.713 }
				x2={ 226.284 }
				y1={ 342.286 }
				y2={ 342.286 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="ae"
				x1={ 249.428 }
				x2={ 271.713 }
				y1={ 342.287 }
				y2={ 342.287 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="af"
				x1={ 249.428 }
				x2={ 271.713 }
				y1={ 342.287 }
				y2={ 342.287 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="ag"
				x1={ 249.428 }
				x2={ 271.713 }
				y1={ 342.287 }
				y2={ 342.287 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="ah"
				x1={ 293.999 }
				x2={ 316.285 }
				y1={ 342.287 }
				y2={ 342.287 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="aj"
				x1={ 16 }
				x2={ 46 }
				y1={ 154 }
				y2={ 154 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</linearGradient>
			<linearGradient
				id="am"
				x1={ 388 }
				x2={ 474 }
				y1={ 220 }
				y2={ 284 }
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</linearGradient>
			<radialGradient
				id="P"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<radialGradient
				id="Q"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<radialGradient
				id="R"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<radialGradient
				id="S"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<radialGradient
				id="T"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<radialGradient
				id="U"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<radialGradient
				id="V"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<radialGradient
				id="W"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<radialGradient
				id="X"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<radialGradient
				id="Y"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<radialGradient
				id="Z"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<radialGradient
				id="aa"
				cx={ 0 }
				cy={ 0 }
				r={ 1 }
				gradientTransform="rotate(98.297 36.366 200.484) scale(41.5781)"
				gradientUnits="userSpaceOnUse"
			>
				<stop stopColor="#B809A7" stopOpacity={ 0 } />
				<stop offset={ 0.469 } stopColor="#E90B76" />
				<stop offset={ 1 } stopColor="#FC8536" />
			</radialGradient>
			<filter
				id="b"
				width={ 468.143 }
				height={ 412.143 }
				x={ 8.429 }
				y={ -10.571 }
				colorInterpolationFilters="sRGB"
				filterUnits="userSpaceOnUse"
			>
				<feFlood floodOpacity={ 0 } result="BackgroundImageFix" />
				<feBlend
					in="SourceGraphic"
					in2="BackgroundImageFix"
					result="shape"
				/>
				<feGaussianBlur
					result="effect1_foregroundBlur_24279_41066"
					stdDeviation={ 34.286 }
				/>
			</filter>
			<filter
				id="d"
				width={ 438.171 }
				height={ 314.744 }
				x={ 20.914 }
				y={ 39.143 }
				colorInterpolationFilters="sRGB"
				filterUnits="userSpaceOnUse"
			>
				<feFlood floodOpacity={ 0 } result="BackgroundImageFix" />
				<feColorMatrix
					in="SourceAlpha"
					result="hardAlpha"
					values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
				/>
				<feMorphology
					in="SourceAlpha"
					radius={ 3.086 }
					result="effect1_dropShadow_24279_41066"
				/>
				<feOffset dy={ 3.086 } />
				<feGaussianBlur stdDeviation={ 3.086 } />
				<feComposite in2="hardAlpha" operator="out" />
				<feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
				<feBlend
					in2="BackgroundImageFix"
					result="effect1_dropShadow_24279_41066"
				/>
				<feBlend
					in="SourceGraphic"
					in2="effect1_dropShadow_24279_41066"
					result="shape"
				/>
			</filter>
			<filter
				id="J"
				width={ 228.893 }
				height={ 179.078 }
				x={ -16.857 }
				y={ 215.714 }
				colorInterpolationFilters="sRGB"
				filterUnits="userSpaceOnUse"
			>
				<feFlood floodOpacity={ 0 } result="BackgroundImageFix" />
				<feGaussianBlur
					in="BackgroundImageFix"
					stdDeviation={ 3.429 }
				/>
				<feComposite
					in2="SourceAlpha"
					operator="in"
					result="effect1_backgroundBlur_24279_41066"
				/>
				<feColorMatrix
					in="SourceAlpha"
					result="hardAlpha"
					values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
				/>
				<feMorphology
					in="SourceAlpha"
					radius={ 13.714 }
					result="effect2_dropShadow_24279_41066"
				/>
				<feOffset dy={ 20.571 } />
				<feGaussianBlur stdDeviation={ 34.286 } />
				<feComposite in2="hardAlpha" operator="out" />
				<feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0" />
				<feBlend
					in2="effect1_backgroundBlur_24279_41066"
					result="effect2_dropShadow_24279_41066"
				/>
				<feBlend
					in="SourceGraphic"
					in2="effect2_dropShadow_24279_41066"
					result="shape"
				/>
			</filter>
			<filter
				id="O"
				width={ 233.143 }
				height={ 233.142 }
				x={ 123.428 }
				y={ 97.429 }
				colorInterpolationFilters="sRGB"
				filterUnits="userSpaceOnUse"
			>
				<feFlood floodOpacity={ 0 } result="BackgroundImageFix" />
				<feGaussianBlur
					in="BackgroundImageFix"
					stdDeviation={ 3.429 }
				/>
				<feComposite
					in2="SourceAlpha"
					operator="in"
					result="effect1_backgroundBlur_24279_41066"
				/>
				<feColorMatrix
					in="SourceAlpha"
					result="hardAlpha"
					values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
				/>
				<feMorphology
					in="SourceAlpha"
					radius={ 13.714 }
					result="effect2_dropShadow_24279_41066"
				/>
				<feOffset dy={ 20.571 } />
				<feGaussianBlur stdDeviation={ 34.286 } />
				<feComposite in2="hardAlpha" operator="out" />
				<feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
				<feBlend
					in2="effect1_backgroundBlur_24279_41066"
					result="effect2_dropShadow_24279_41066"
				/>
				<feBlend
					in="SourceGraphic"
					in2="effect2_dropShadow_24279_41066"
					result="shape"
				/>
			</filter>
			<filter
				id="ai"
				width={ 126 }
				height={ 132 }
				x={ -16 }
				y={ 112 }
				colorInterpolationFilters="sRGB"
				filterUnits="userSpaceOnUse"
			>
				<feFlood floodOpacity={ 0 } result="BackgroundImageFix" />
				<feGaussianBlur
					in="BackgroundImageFix"
					stdDeviation={ 3.429 }
				/>
				<feComposite
					in2="SourceAlpha"
					operator="in"
					result="effect1_backgroundBlur_24279_41066"
				/>
				<feColorMatrix
					in="SourceAlpha"
					result="hardAlpha"
					values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
				/>
				<feMorphology
					in="SourceAlpha"
					radius={ 8 }
					result="effect2_dropShadow_24279_41066"
				/>
				<feOffset dx={ 16 } dy={ 24 } />
				<feGaussianBlur stdDeviation={ 20 } />
				<feComposite in2="hardAlpha" operator="out" />
				<feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
				<feBlend
					in2="effect1_backgroundBlur_24279_41066"
					result="effect2_dropShadow_24279_41066"
				/>
				<feBlend
					in="SourceGraphic"
					in2="effect2_dropShadow_24279_41066"
					result="shape"
				/>
			</filter>
			<filter
				id="ak"
				width={ 171 }
				height={ 120 }
				x={ 321 }
				y={ 77 }
				colorInterpolationFilters="sRGB"
				filterUnits="userSpaceOnUse"
			>
				<feFlood floodOpacity={ 0 } result="BackgroundImageFix" />
				<feGaussianBlur
					in="BackgroundImageFix"
					stdDeviation={ 3.429 }
				/>
				<feComposite
					in2="SourceAlpha"
					operator="in"
					result="effect1_backgroundBlur_24279_41066"
				/>
				<feColorMatrix
					in="SourceAlpha"
					result="hardAlpha"
					values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
				/>
				<feMorphology
					in="SourceAlpha"
					radius={ 8 }
					result="effect2_dropShadow_24279_41066"
				/>
				<feOffset dx={ -16 } dy={ 24 } />
				<feGaussianBlur stdDeviation={ 20 } />
				<feComposite in2="hardAlpha" operator="out" />
				<feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
				<feBlend
					in2="effect1_backgroundBlur_24279_41066"
					result="effect2_dropShadow_24279_41066"
				/>
				<feBlend
					in="SourceGraphic"
					in2="effect2_dropShadow_24279_41066"
					result="shape"
				/>
			</filter>
			<filter
				id="al"
				width={ 162 }
				height={ 158 }
				x={ 334 }
				y={ 206 }
				colorInterpolationFilters="sRGB"
				filterUnits="userSpaceOnUse"
			>
				<feFlood floodOpacity={ 0 } result="BackgroundImageFix" />
				<feGaussianBlur
					in="BackgroundImageFix"
					stdDeviation={ 3.429 }
				/>
				<feComposite
					in2="SourceAlpha"
					operator="in"
					result="effect1_backgroundBlur_24279_41066"
				/>
				<feColorMatrix
					in="SourceAlpha"
					result="hardAlpha"
					values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
				/>
				<feMorphology
					in="SourceAlpha"
					radius={ 8 }
					result="effect2_dropShadow_24279_41066"
				/>
				<feOffset dx={ -16 } dy={ 24 } />
				<feGaussianBlur stdDeviation={ 20 } />
				<feComposite in2="hardAlpha" operator="out" />
				<feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
				<feBlend
					in2="effect1_backgroundBlur_24279_41066"
					result="effect2_dropShadow_24279_41066"
				/>
				<feBlend
					in="SourceGraphic"
					in2="effect2_dropShadow_24279_41066"
					result="shape"
				/>
			</filter>
			<clipPath id="a">
				<path fill="#fff" d="M0 0h480v388H0z" />
			</clipPath>
		</defs>
	</svg>
);
