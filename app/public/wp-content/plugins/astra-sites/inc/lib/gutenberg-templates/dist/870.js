"use strict";(self.webpackChunkast_block_templates=self.webpackChunkast_block_templates||[]).push([[870],{7870:function(e,t,o){o.r(t),o.d(t,{default:function(){return T}});var a=o(5711),s=o(5940),r=o(7363),n=o(8195),l=o(5302),c=o(1882),i=o(8096),p=o(5868),m=o(4142),u=o(9148),d=o(6128),g=o(1569),b=o(5622),f=o(8680),y=o(1277),h=o(7712),k=o(3698),v=o(1890);const{post:I}=wp.ajax,{parse:w}=wp.blocks,{bypassAuth:_}=ast_block_template_vars;var P=(0,a.Z)((0,s.Z)((e=>{const{getImportItemInfo:t,getCurrentScreen:o,getSitePreview:a,getActiveBlockPaletteSlug:s,getActivePagePaletteSlug:r,getDisableAi:n,getAdaptiveMode:l}=e("ast-block-templates");return{importItemInfo:t(),sitePreview:a(),currentScreen:o(),activeBlockPaletteSlug:s(),activePagePaletteSlug:r(),disableAI:n(),adaptiveMode:l()}})),(0,i.Z)((e=>{const{setImportItemInfo:t,setTogglePopup:o,setOnboardingAiPopup:a}=e("ast-block-templates"),{insertBlocks:s}=wp.data.dispatch("core/block-editor");return{setImportItemInfo:t,onSetTogglePopup:o,insertBlocks:s,setOnboardingAiPopup:a}})))((0,r.memo)((e=>{let{onImportFail:t,setImportItemInfo:o,requiredPlugins:a,importItemInfo:s,title:i,btnClass:P,insertBlocks:E,onSetTogglePopup:S,item:A,onClick:x,onBlockImport:N,activeBlockPaletteSlug:R,activePagePaletteSlug:C,currentScreen:$,disableAI:T,importing:j=!1,adaptiveMode:B,showIcon:O=!0}=e,[Z]=(0,r.useState)(s),L=a||[];const F=P||"",[D]=(0,r.useState)(i||"Import"),{index:q}=wp.data.select("core/block-editor").getBlockInsertionPoint(),{importInProgress:W,syncLibNotice:M,stepData:{token:z}}=(0,l.Z)((e=>{const{getImportInProgress:t,getNotice:o,getAIStepData:a}=e(k.L);return{importInProgress:t(),syncLibNotice:o(),stepData:a()}}),[]),{setImportInProgress:H,toggleConnectZipAI:K,setNotice:Q}=(0,c.Z)(k.L),U="info"===M?.type,V=(0,n.gt)(),G=(e,t,o)=>{(0,y.Am)((0,h.Z)({title:e,message:t}),h.Z.getOptions({type:o}))},J=async()=>{try{Q({type:"import-info",message:(0,v.__)("Activating Spectra plugin…","ast-block-templates")}),await(0,u.j)({slug:"ultimate-addons-for-gutenberg",init:"ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php",name:"Spectra"})}catch(e){H(!1),G("Import failed!","Spectra activation failed!","error")}},X=async()=>{try{Q({type:"import-info",message:(0,v.__)("Activating WPForms plugin…","ast-block-templates")}),await(0,u.j)({slug:"wpforms-lite",init:"wpforms-lite/wpforms.php",name:"WPForms Lite"}),await oe()}catch(e){H(!1),G("Import failed!","WPForms activation failed!","error")}},Y=()=>{const e=Z.original_content,o="all-blocks-grid"===$?"block":"page",a="block"===o?Z["blocks-category"][0]:Z["pages-category"][0];I({action:"ast_block_templates_import_block",content:e,category:a,_ajax_nonce:ast_block_template_vars._ajax_nonce,style:"all-blocks-grid"===$?R:C,disableAI:T,block_type:o,adaptiveMode:B}).done((e=>{H(!1);const t=(0,n.CN)(w(e));E(t,q,"",!1),ee(t[0].clientId),S(),document.getElementById("ast-block-templates-modal-wrap").classList.remove("open"),document.body.classList.remove("ast-block-templates-modal-open"),(V.inactive||V.notInstalled)&&(0,b.iP)(),"function"==typeof N&&N(e,q)})).fail((()=>{H(!1),t(),G("Import failed!","Failed to import the block. Please try again later or contact support for assistance.","error-import")}))},ee=e=>{e&&setTimeout((()=>{const t=te(),o="block-"+e,a=t.getElementById(o);a&&a.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})}),2500)},te=()=>{const e=document.getElementsByClassName("is-tablet-preview"),t=document.getElementsByClassName("is-mobile-preview");if(0!==e.length||0!==t.length){const o=e[0]||t[0];let a=!1;o&&(a=o.getElementsByTagName("iframe")[0]);const s=a?.contentWindow.document||a?.contentDocument;if(s)return s}return document},oe=()=>{Z?.["post-meta"]?.["astra-site-wpforms-path"]||""?I({action:"ast_block_templates_import_wpforms",id:Z.id,_ajax_nonce:ast_block_template_vars._ajax_nonce}).done((()=>{Y()})).fail((()=>{H(!1),G("Import failed!","WPForms import failed!","error")})):Y()};return React.createElement(p.Z,{className:(0,n.AK)("min-w-[4.875rem] h-7 hover:shadow-small sp-text-sm",F,(W&&!j||U)&&"opacity-50 cursor-not-allowed"),type:"button",variant:"primary",onClick:z||_?async e=>{W||U||(H(!0),"function"==typeof x&&x(e),V.notInstalled?(await(async()=>{try{Q({type:"import-info",message:(0,v.__)("Installing Spectra plugin…","ast-block-templates")}),await(0,u.H)({slug:"ultimate-addons-for-gutenberg",init:"ultimate-addons-for-gutenberg/ultimate-addons-for-gutenberg.php",name:"Spectra"})}catch(e){H(!1),G("Import failed!","Spectra installation failed!","error")}})(),await J()):V.inactive&&await J(),(0,g.I)(A.ID,"astra-blocks").then((e=>{o(e),Z=e,L=e?.["post-meta"]?.["astra-blocks-required-plugins"]?d.t.parse(e["post-meta"]["astra-blocks-required-plugins"]):[],L.length?"not-installed"===ast_block_template_vars.wpforms_status?(async()=>{try{Q({type:"import-info",message:(0,v.__)("Installing WPForms plugin…","ast-block-templates")}),await(0,u.H)({slug:"wpforms-lite",init:"wpforms-lite/wpforms.php",name:"WPForms Lite"}),await X()}catch(e){H(!1),G("Import failed!","WPForms installation failed!","error")}})():"inactive"===ast_block_template_vars.wpforms_status?X():oe():oe()})).catch((e=>{G("Import failed!",e,"error-import"),H(!1),t()})))}:async()=>{K(),(0,n.jw)("ast-import",{blockId:A.ID,blockType:A.type,blockPaletteSlug:R,pagePaletteSlug:C})},isSmall:!0,hasPrefixIcon:!j&&O},j?React.createElement(m.Z,{className:"h-4 w-4 !shrink-0"}):React.createElement(React.Fragment,null,O&&React.createElement(f.Z,{className:"h-4 w-4 !shrink-0"}),React.createElement("span",{className:"truncate"},D)))}))),E=o(168);const S=e=>{const t=["skip","logo","fav-icon"];for(const o of t)if(e.includes(o))return!0;return!1},A={linearTween(e,t,o,a){return o*e/a+t},easeInQuad(e,t,o,a){return o*(e/=a)*e+t},easeOutQuad(e,t,o,a){return-o*(e/=a)*(e-2)+t},easeInOutQuad(e,t,o,a){return(e/=a/2)<1?o/2*e*e+t:-o/2*(--e*(e-2)-1)+t},easeInCubic(e,t,o,a){return o*(e/=a)*e*e+t},easeOutCubic(e,t,o,a){return e/=a,o*(--e*e*e+1)+t},easeInOutCubic(e,t,o,a){return(e/=a/2)<1?o/2*e*e*e+t:o/2*((e-=2)*e*e+2)+t}};Object.seal(A);var x=A,N=o(3433);const{spectra_common_styles:R,is_rtl:C}=ast_block_template_vars,$="visible";var T=(0,a.Z)((0,s.Z)((e=>{const{getDisableAi:t,getDisablePreview:o,getTogglePopup:a}=e("ast-block-templates");return{disableAi:t(),disablePreview:o(),togglePopup:a()}})))((0,r.memo)((e=>{let{item:t,content:o,stylesheet:a,astraCustomizer:s,globalStylesheet:l,colorPalette:c,dynamicContent:i,selectedImages:p,disableAi:m,togglePopup:u,email:d,phone:g,address:b}=e;const f=(0,r.useRef)(null),y=(0,r.useRef)(null),h=t["thumbnail-image-url"]||"",k=t["featured-image-url"]||"",v=(0,n.gt)();let I=0,w=0;const[_,A]=(0,r.useState)({importing:!1}),[T,j]=(0,r.useState)(`${ast_block_template_vars.uri}dist/placeholder_200_200.png`),[B,O]=(0,r.useState)(`${ast_block_template_vars.uri}dist/placeholder_200_200.png`);function Z(){const e=f?.current,t=y?.current;if(!e||!t)return;const o=e.parentNode.offsetWidth/1200,a=o*t.offsetHeight;e.style.transform=`scale(${o})`,e.style.height=`${a}px`}(0,r.useLayoutEffect)((()=>{let e=!0;const t=p,r=new Image;r.src=h;const n=new Image;n.src=k;const u=y.current;function f(t,o){e&&(t.onload=()=>{o(t.src)})}f(r,j),f(n,O);const v=document.getElementById("astra-wp-editor-styles-inline-css")?.textContent.replace(/:root/g,".block-container").replace(/body/g,".block-container"),_=document.getElementById("astra-block-editor-styles-inline-css")?.textContent.replace(/:root/g,".block-container").replace(/body/g,".block-container");let P="";if(a&&(a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return m?e:0!==Object.keys(i).length&&i?t&&0===t.length?e:e.replace(/background-image\s*:\s*url\(['"]?([^'")]+)['"]?\)/g,(function(e,o){if(S(o))return e;if(!t[w]?.url)return"";const a=t[w]?.url;return w++,w%=t.length,'background-image: url("'+a+'")'})):e}(a,t)),P+=s?`<style type="text/css" id="gt-astra-customizer">${s}</style>`:"",P+=`<style type="text/css" id="gt-spectra-common-stylesheet">${R}</style>`,P=a?`${P} <style type="text/css" id="gt-common-stylesheet">${a}</style>`:P,l&&(P+=`<style type="text/css" id="gt-global-stylesheet"> ${l} </style>`),v&&(P+=`<style type="text/css" id="gt-wpeditor-css" > ${v} </style>`),_&&(P+=`<style type="text/css" id="gt-blockeditor-css"> ${_} </style>`),u&&o){let e=u.shadowRoot;if(null===e&&(e=u.attachShadow({mode:"open"})),null===e)return;let a=o;if(!m){const e=i??[];e&&Object.keys(e)?.length>0&&Object.keys(e).forEach((t=>{a=null!==e[t]?a.replace(t,e[t]):a})),i&&Object.keys(i).length>0&&(E=a,d&&(E=E.replace("<EMAIL>",d)),g&&(E=E.replace("202-555-0188",g)),b&&(E=E.replace("2360 Hood Avenue, San Diego, CA, 92123",b)),a=E,a=F(a))}e.innerHTML=P+'<div class="st-block-container">'+a+"</div>",c.forEach(((t,o)=>{e?.host.style.setProperty(`--ast-global-color-${o}`,t)}));const s=u.shadowRoot.querySelectorAll("img");e&&(s.forEach((e=>{S(e.src)||m||0===t?.length||0===Object.keys(i).length||!i||L(e,"hidden")})),Z(),((e,t)=>{m||0!==Object.keys(i).length&&i&&t?.length&&e?.forEach((function(e){S(e.src)||(e.onload=()=>{const o=e.getAttribute("src")||"";if(o.includes("unsplash")||o.includes("pexels")||o.includes("pixabay"))return void L(e,$);const a=t[I];if(void 0!==a)if(a.url.includes("unsplash")||a.url.includes("pexels")||a.url.includes("pixabay")){if(I++,void 0===t[I]&&(I=0),"PICTURE"===e.parentNode.nodeName)e.parentNode.querySelectorAll("source").forEach((e=>{e.setAttribute("srcset",a.optimized_url)})),e.parentNode.querySelector("img").setAttribute("src",a.optimized_url);else{const t=document.createElement("img");t.src=a.optimized_url,e.parentNode.replaceChild(t,e)}L(e,$)}else L(e,$);else L(e,$)},e.onerror=()=>{L(e,$)})}))})(s,t))}var E;return()=>{e=!1}}),[t,m,i,p]),(0,r.useEffect)((()=>{const e=new ResizeObserver(Z);return e.observe(document.documentElement),()=>{e&&e.disconnect()}}),[]),(0,r.useEffect)((()=>{const e=document.querySelector("#ast-block-templates-modal-wrap");if(!e)return;const t=new MutationObserver(Z);return t.observe(e,{childList:!0,attributes:!0,subtree:!0}),()=>{t&&t.disconnect()}}),[]),(0,r.useEffect)((()=>{const e=y?.current;if(e){const t=e.shadowRoot;c.forEach(((e,o)=>{null!==t&&t.host.style.setProperty(`--ast-global-color-${o}`,e)}))}}),[c]);const L=(e,t)=>{e&&(e.style.visibility=t)},F=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return b?e.replace(/(src="https:\/\/maps\.google\.com\/maps\?q=)([^&]+)(&z=\d+&hl=en&t=m&output=embed&iwloc=near")/g,`$1${encodeURIComponent(b)}$3`):e};let D=0,q=0;const W=(e,t,o)=>{const a=e.scrollTop,s=t-a;let r=0;const n=function(){r+=20;const t=x.linearTween(r,a,s,o);e.scrollTop=t,r<o&&(D=setTimeout(n,20))};n()};(0,r.useEffect)((()=>{u||(I=0,w=0)}),[u]);const M=C?"origin-top-right":"origin-top-left";return React.createElement(N.Z,{className:(0,n.AK)("w-full h-fit",!0===_.importing?"importing":"")},React.createElement("div",{className:(0,n.AK)("cursor-pointer relative border border-solid border-border-primary hover:border-accent-spectra transition duration-150 ease-in-out overflow-hidden group",_.importing&&"border-accent-spectra")},React.createElement("div",{className:`thumbnail left-0 m-0 min-h-[auto] overflow-visible text-left top-0 ${M} relative pointer-events-none`,ref:f},React.createElement("div",{className:"absolute w-[1200px] pointer-events-none max-h-[1600px] overflow-hidden",ref:y},!o&&React.createElement("img",{srcSet:`${B}, ${T}`,src:T,alt:"Preview"}),React.createElement("div",{className:"preview"},React.createElement("span",{className:"ast-block-templates-icon ast-block-templates-icon-search"})))),React.createElement("div",{className:"absolute inset-0 grid grid-cols-1 grid-rows-1 place-items-end",onMouseEnter:()=>{if("page"!==t.type)return;const e=y.current;D&&clearTimeout(D),q=setTimeout((()=>{W(e,e.scrollHeight,4e3)}),1e3)},onMouseLeave:()=>{q&&clearTimeout(q);const e=y.current;D&&clearTimeout(D),W(e,0,1500)}},React.createElement("div",{className:(0,n.AK)("opacity-0 group-hover:opacity-100 w-full flex items-center justify-between px-3 py-2 backdrop-blur-sm bg-white/[0.85] shadow-action-buttons transition-all duration-150 ease-in-out",!0===_.importing?"opacity-100":"")},React.createElement(P,{title:v.notInstalled?"Install Spectra & Insert":v.inactive?"Activate Spectra & Insert":"Insert",showIcon:v.active,liveRequest:!0,item:t,importing:_.importing,onClick:()=>{A({..._,importing:!0})},onBlockImport:()=>{A({..._,importing:!1})},onImportFail:()=>{A({..._,importing:!1})}}),React.createElement(E.Z,{item:t})))))})))}}]);