"use strict";(globalThis.webpackChunkast_block_templates=globalThis.webpackChunkast_block_templates||[]).push([[910],{1910:(e,t,o)=>{o.r(t),o.d(t,{default:()=>b});var s=o(3771),r=o(8242),l=o(1594),n=o(6134),c=o(9453),a=o(9260),i=o(7510),u=o(5034),p=o(7497);const{spectra_common_styles:m,is_rtl:d}=ast_block_template_vars,g="visible",b=(0,s.A)((0,r.A)((e=>{const{getDisableAi:t,getDisablePreview:o,getTogglePopup:s}=e("ast-block-templates");return{disableAi:t(),disablePreview:o(),togglePopup:s()}})))((0,l.memo)((({item:e,content:t,stylesheet:o,astraCustomizer:s,globalStylesheet:r,colorPalette:b,dynamicContent:h,selectedImages:y,disableAi:f,togglePopup:v,email:k,phone:w,address:E,onClickBlock:x})=>{const _=(0,l.useRef)(null),R=(0,l.useRef)(null),$=e["thumbnail-image-url"]||"",A=e["featured-image-url"]||"",I=(0,n.Fx)();let N=0,C=0;const[S,T]=(0,l.useState)({importing:!1}),[P,O]=(0,l.useState)(`${ast_block_template_vars.uri}dist/placeholder_200_200.png`),[j,z]=(0,l.useState)(`${ast_block_template_vars.uri}dist/placeholder_200_200.png`);function q(){const e=_?.current,t=R?.current;if(!e||!t)return;const o=e.parentNode.offsetWidth/1200,s=o*t.offsetHeight;e.style.transform=`scale(${o})`,e.style.height=`${s}px`}(0,l.useLayoutEffect)((()=>{let e=!0;const l=y,n=new Image;n.src=$;const c=new Image;c.src=A;const a=R.current;function u(t,o){e&&(t.onload=()=>{o(t.src)})}u(n,O),u(c,z);const p=document.getElementById("astra-wp-editor-styles-inline-css")?.textContent.replace(/:root/g,".block-container").replace(/body/g,".block-container"),d=document.getElementById("astra-block-editor-styles-inline-css")?.textContent.replace(/:root/g,".block-container").replace(/body/g,".block-container");let v="";if(o&&(o=((e,t=[])=>f?e:0!==Object.keys(h).length&&h?t&&0===t.length?e:e.replace(/background-image\s*:\s*url\(['"]?([^'")]+)['"]?\)/g,(function(e,o){if((0,i.p)(o))return e;if(!t[C]?.url)return"";const s=t[C]?.url;return C++,C%=t.length,'background-image: url("'+s+'")'})):e)(o,l)),v+=s?`<style type="text/css" id="gt-astra-customizer">${s}</style>`:"",v+=`<style type="text/css" id="gt-spectra-common-stylesheet">${m}</style>`,v=o?`${v} <style type="text/css" id="gt-common-stylesheet">${o}</style>`:v,r&&(v+=`<style type="text/css" id="gt-global-stylesheet"> ${r} </style>`),p&&(v+=`<style type="text/css" id="gt-wpeditor-css" > ${p} </style>`),d&&(v+=`<style type="text/css" id="gt-blockeditor-css"> ${d} </style>`),a&&t){let e=a.shadowRoot;if(null===e&&(e=a.attachShadow({mode:"open"})),null===e)return;let o=t;if(!f){const e=h??[];e&&Object.keys(e)?.length>0&&Object.keys(e).forEach((t=>{o=null!==e[t]?o.replace(t,e[t]):o})),h&&Object.keys(h).length>0&&(x=o,k&&(x=x.replace("<EMAIL>",k)),w&&(x=x.replace("202-555-0188",w)),E&&(x=x.replace("2360 Hood Avenue, San Diego, CA, 92123",E)),o=x,o=H(o))}e.innerHTML=v+'<div class="st-block-container">'+o+"</div>",b.forEach(((t,o)=>{e?.host.style.setProperty(`--ast-global-color-${o}`,t)}));const s=a.shadowRoot.querySelectorAll("img");e&&(s.forEach((e=>{(0,i.p)(e.src)||f||0===l?.length||0===Object.keys(h).length||!h||B(e,"hidden")})),q(),((e,t)=>{f||0!==Object.keys(h).length&&h&&t?.length&&e?.forEach((function(e){(0,i.p)(e.src)||(e.onload=()=>{const o=e.getAttribute("src")||"";if(o.includes("unsplash")||o.includes("pexels")||o.includes("pixabay"))return void B(e,g);const s=t[N];if(void 0!==s)if(s.url.includes("unsplash")||s.url.includes("pexels")||s.url.includes("pixabay")){if(N++,void 0===t[N]&&(N=0),"PICTURE"===e.parentNode.nodeName)e.parentNode.querySelectorAll("source").forEach((e=>{e.setAttribute("srcset",s.optimized_url)})),e.parentNode.querySelector("img").setAttribute("src",s.optimized_url);else{const t=document.createElement("img");t.src=s.optimized_url,e.parentNode.replaceChild(t,e)}B(e,g)}else B(e,g);else B(e,g)},e.onerror=()=>{B(e,g)})}))})(s,l))}var x;return()=>{e=!1}}),[e,f,h,y]),(0,l.useEffect)((()=>{const e=new ResizeObserver(q);return e.observe(document.documentElement),()=>{e&&e.disconnect()}}),[]),(0,l.useEffect)((()=>{const e=document.querySelector("#ast-block-templates-modal-wrap");if(!e)return;const t=new MutationObserver(q);return t.observe(e,{childList:!0,attributes:!0,subtree:!0}),()=>{t&&t.disconnect()}}),[]),(0,l.useEffect)((()=>{const e=R?.current;if(e){const t=e.shadowRoot;b.forEach(((e,o)=>{null!==t&&t.host.style.setProperty(`--ast-global-color-${o}`,e)}))}}),[b]);const B=(e,t)=>{e&&(e.style.visibility=t)},H=(e="")=>E?e.replace(/(src="https:\/\/maps\.google\.com\/maps\?q=)([^&]+)(&z=\d+&hl=en&t=m&output=embed&iwloc=near")/g,`$1${encodeURIComponent(E)}$3`):e;let L=0,M=0;const W=(e,t,o)=>{const s=e.scrollTop,r=t-s;let l=0;const n=function(){l+=20;const t=u.A.linearTween(l,s,r,o);e.scrollTop=t,l<o&&(L=setTimeout(n,20))};n()};(0,l.useEffect)((()=>{v||(N=0,C=0)}),[v]);const D=d?"origin-top-right":"origin-top-left";return React.createElement(p.A,{className:(0,n.xW)("w-full h-fit",!0===S.importing?"importing":"")},React.createElement("div",{className:(0,n.xW)("cursor-pointer relative border border-solid border-border-primary hover:border-accent-spectra transition duration-150 ease-in-out overflow-hidden group",S.importing&&"border-accent-spectra")},React.createElement("div",{className:`thumbnail left-0 m-0 min-h-[auto] overflow-visible text-left top-0 ${D} relative pointer-events-none`,ref:_},React.createElement("div",{className:"absolute w-[1200px] pointer-events-none max-h-[1600px] overflow-hidden",ref:R},!t&&React.createElement("img",{srcSet:`${j}, ${P}`,src:P,alt:"Preview"}),React.createElement("div",{className:"preview"},React.createElement("span",{className:"ast-block-templates-icon ast-block-templates-icon-search"})))),React.createElement("div",{className:"absolute inset-0 grid grid-cols-1 grid-rows-1 place-items-end",onMouseEnter:()=>{if("page"!==e.type)return;const t=R.current;L&&clearTimeout(L),M=setTimeout((()=>{W(t,t.scrollHeight,4e3)}),1e3)},onMouseLeave:()=>{M&&clearTimeout(M);const e=R.current;L&&clearTimeout(L),W(e,0,1500)},onClick:(F=e,e=>{e&&"gt-single-block-action-btns"!==e?.target&&"function"==typeof x&&x(F)}),"aria-hidden":"true"},React.createElement("div",{className:(0,n.xW)("opacity-0 group-hover:opacity-100 w-full flex items-center justify-between px-3 py-2 backdrop-blur-sm bg-white/[0.85] shadow-action-buttons transition-all duration-150 ease-in-out",!0===S.importing?"opacity-100":"")},React.createElement(c.A,{title:I.notInstalled?"Install Spectra & Insert":I.inactive?"Activate Spectra & Insert":"Insert",showIcon:I.active,liveRequest:!0,item:e,importing:S.importing,onClick:()=>{T({...S,importing:!0})},onBlockImport:()=>{T({...S,importing:!1})},onImportFail:()=>{T({...S,importing:!1})}}),React.createElement(a.A,{item:e})))));var F})))}}]);