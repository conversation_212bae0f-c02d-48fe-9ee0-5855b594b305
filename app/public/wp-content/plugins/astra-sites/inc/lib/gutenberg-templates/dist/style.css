@font-face {
	font-family: ast-block-templates;
	src: url( "fonts/ast-block-templates.eot?p3u0q1" );
	src:
		url( "fonts/ast-block-templates.eot?p3u0q1#iefix" ) format( "embedded-opentype" ),
		url( "fonts/ast-block-templates.ttf?p3u0q1" ) format( "truetype" ),
		url( "fonts/ast-block-templates.woff?p3u0q1" ) format( "woff" ),
		url( "fonts/ast-block-templates.svg?p3u0q1#ast-block-templates" ) format( "svg" );
	font-weight: 400;
	font-style: normal;
	font-display: block;
}

[class^="ast-block-templates-icon-"],
[class*=" ast-block-templates-icon-"] {

	/* use !important to prevent issues with browser extensions that change fonts */
	font-family: ast-block-templates, sans-serif !important;
	speak: never;
	font-style: normal;
	font-weight: 400;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.ast-block-templates-icon-chevron-left::before {
	content: "\e904";
}

.ast-block-templates-icon-pages::before {
	content: "\e902";
}

.ast-block-templates-icon-patterns::before {
	content: "\e903";
}

.ast-block-templates-icon-wireframe::before {
	content: "\e900";
}

.ast-block-templates-icon-refresh::before {
	content: "\e901";
}
