/*! This file is auto-generated */
!function(s){window.findPosts={open:function(n,e){var i=s(".ui-find-overlay");return 0===i.length&&(s("body").append('<div class="ui-find-overlay"></div>'),findPosts.overlay()),i.show(),n&&e&&s("#affected").attr("name",n).val(e),s("#find-posts").show(),s("#find-posts-input").trigger("focus").on("keyup",function(n){27==n.which&&findPosts.close()}),findPosts.send(),!1},close:function(){s("#find-posts-response").empty(),s("#find-posts").hide(),s(".ui-find-overlay").hide()},overlay:function(){s(".ui-find-overlay").on("click",function(){findPosts.close()})},send:function(){var n={ps:s("#find-posts-input").val(),action:"find_posts",_ajax_nonce:s("#_ajax_nonce").val()},e=s(".find-box-search .spinner");e.addClass("is-active"),s.ajax(ajaxurl,{type:"POST",data:n,dataType:"json"}).always(function(){e.removeClass("is-active")}).done(function(n){n.success||s("#find-posts-response").text(wp.i18n.__("An error has occurred. Please reload the page and try again.")),s("#find-posts-response").html(n.data)}).fail(function(){s("#find-posts-response").text(wp.i18n.__("An error has occurred. Please reload the page and try again."))})}},s(function(){var o,n,e=s("#wp-media-grid"),i=new ClipboardJS(".copy-attachment-url.media-library");e.length&&window.wp&&window.wp.media&&(n=_wpMediaGridSettings,n=window.wp.media({frame:"manage",container:e,library:n.queryVars}).open(),e.trigger("wp-media-grid-ready",n)),s("#find-posts-submit").on("click",function(n){s('#find-posts-response input[type="radio"]:checked').length||n.preventDefault()}),s("#find-posts .find-box-search :input").on("keypress",function(n){if(13==n.which)return findPosts.send(),!1}),s("#find-posts-search").on("click",findPosts.send),s("#find-posts-close").on("click",findPosts.close),s("#doaction").on("click",function(e){s('select[name="action"]').each(function(){var n=s(this).val();"attach"===n?(e.preventDefault(),findPosts.open()):"delete"!==n||showNotice.warn()||e.preventDefault()})}),s(".find-box-inside").on("click","tr",function(){s(this).find(".found-radio input").prop("checked",!0)}),i.on("success",function(n){var e=s(n.trigger),i=s(".success",e.closest(".copy-to-clipboard-container"));n.clearSelection(),clearTimeout(o),i.removeClass("hidden"),o=setTimeout(function(){i.addClass("hidden")},3e3),wp.a11y.speak(wp.i18n.__("The file URL has been copied to your clipboard"))})})}(jQuery);