/*! This file is auto-generated */
window.wp=window.wp||{},window.communityEventsData=window.communityEventsData||{},jQuery(function(s){var t,n=s("#welcome-panel"),e=s("#wp_welcome_panel-hide");t=function(e){s.post(ajaxurl,{action:"update-welcome-panel",visible:e,welcomepanelnonce:s("#welcomepanelnonce").val()})},n.hasClass("hidden")&&e.prop("checked")&&n.removeClass("hidden"),s(".welcome-panel-close, .welcome-panel-dismiss a",n).on("click",function(e){e.preventDefault(),n.addClass("hidden"),t(0),s("#wp_welcome_panel-hide").prop("checked",!1)}),e.on("click",function(){n.toggleClass("hidden",!this.checked),t(this.checked?1:0)}),window.ajaxWidgets=["dashboard_primary"],window.ajaxPopulateWidgets=function(e){function t(e,t){var n,o=s("#"+t+" div.inside:visible").find(".widget-loading");o.length&&(n=o.parent(),setTimeout(function(){n.load(ajaxurl+"?action=dashboard-widgets&widget="+t+"&pagenow="+pagenow,"",function(){n.hide().slideDown("normal",function(){s(this).css("display","")})})},500*e))}e?(e=e.toString(),-1!==s.inArray(e,ajaxWidgets)&&t(0,e)):s.each(ajaxWidgets,t)},ajaxPopulateWidgets(),postboxes.add_postbox_toggles(pagenow,{pbshow:ajaxPopulateWidgets}),window.quickPressLoad=function(){var t,n,o,i,a,e=s("#quickpost-action");s('#quick-press .submit input[type="submit"], #quick-press .submit input[type="reset"]').prop("disabled",!1),t=s("#quick-press").on("submit",function(e){e.preventDefault(),s("#dashboard_quick_press #publishing-action .spinner").show(),s('#quick-press .submit input[type="submit"], #quick-press .submit input[type="reset"]').prop("disabled",!0),s.post(t.attr("action"),t.serializeArray(),function(e){var t;s("#dashboard_quick_press .inside").html(e),s("#quick-press").removeClass("initial-form"),quickPressLoad(),(t=s(".drafts ul li").first()).css("background","#fffbe5"),setTimeout(function(){t.css("background","none")},1e3),s("#title").trigger("focus")})}),s("#publish").on("click",function(){e.val("post-quickpress-publish")}),s("#quick-press").on("click focusin",function(){wpActiveEditor="content"}),document.documentMode&&document.documentMode<9||(s("body").append('<div class="quick-draft-textarea-clone" style="display: none;"></div>'),n=s(".quick-draft-textarea-clone"),o=s("#content"),i=o.height(),a=s(window).height()-100,n.css({"font-family":o.css("font-family"),"font-size":o.css("font-size"),"line-height":o.css("line-height"),"padding-bottom":o.css("paddingBottom"),"padding-left":o.css("paddingLeft"),"padding-right":o.css("paddingRight"),"padding-top":o.css("paddingTop"),"white-space":"pre-wrap","word-wrap":"break-word",display:"none"}),o.on("focus input propertychange",function(){var e=s(this),t=e.val()+"&nbsp;",t=n.css("width",e.css("width")).text(t).outerHeight()+2;o.css("overflow-y","auto"),t===i||a<=t&&a<=i||(i=a<t?a:t,o.css("overflow","hidden"),e.css("height",i+"px"))}))},window.quickPressLoad(),s(".meta-box-sortables").sortable("option","containment","#wpwrap")}),jQuery(function(c){"use strict";var r=window.communityEventsData,s=wp.date.dateI18n,m=wp.date.format,d=wp.i18n.sprintf,l=wp.i18n.__,u=wp.i18n._x,p=window.wp.communityEvents={initialized:!1,model:null,init:function(){var e;p.initialized||(e=c("#community-events"),c(".community-events-errors").attr("aria-hidden","true").removeClass("hide-if-js"),e.on("click",".community-events-toggle-location, .community-events-cancel",p.toggleLocationForm),e.on("submit",".community-events-form",function(e){var t=c("#community-events-location").val().trim();e.preventDefault(),t&&p.getEvents({location:t})}),r&&r.cache&&r.cache.location&&r.cache.events?p.renderEventsTemplate(r.cache,"app"):p.getEvents(),p.initialized=!0)},toggleLocationForm:function(e){var t=c(".community-events-toggle-location"),n=c(".community-events-cancel"),o=c(".community-events-form"),i=c();"object"==typeof e&&(i=c(e.target),e="true"==t.attr("aria-expanded")?"hide":"show"),"hide"===e?(t.attr("aria-expanded","false"),n.attr("aria-expanded","false"),o.attr("aria-hidden","true"),i.hasClass("community-events-cancel")&&t.trigger("focus")):(t.attr("aria-expanded","true"),n.attr("aria-expanded","true"),o.attr("aria-hidden","false"))},getEvents:function(t){var n,o=this,e=c(".community-events-form").children(".spinner");(t=t||{})._wpnonce=r.nonce,t.timezone=window.Intl?window.Intl.DateTimeFormat().resolvedOptions().timeZone:"",n=t.location?"user":"app",e.addClass("is-active"),wp.ajax.post("get-community-events",t).always(function(){e.removeClass("is-active")}).done(function(e){"no_location_available"===e.error&&(t.location?e.unknownCity=t.location:delete e.error),o.renderEventsTemplate(e,n)}).fail(function(){o.renderEventsTemplate({location:!1,events:[],error:!0},n)})},renderEventsTemplate:function(e,t){var n,o,i=c(".community-events-toggle-location"),a=c("#community-events-location-message"),s=c(".community-events-results");e.events=p.populateDynamicEventFields(e.events,r.time_format),o={".community-events":!0,".community-events-loading":!1,".community-events-errors":!1,".community-events-error-occurred":!1,".community-events-could-not-locate":!1,"#community-events-location-message":!1,".community-events-toggle-location":!1,".community-events-results":!1},e.location.ip?(a.text(l("Attend an upcoming event near you.")),n=e.events.length?wp.template("community-events-event-list"):wp.template("community-events-no-upcoming-events"),s.html(n(e)),o["#community-events-location-message"]=!0,o[".community-events-toggle-location"]=!0,o[".community-events-results"]=!0):e.location.description?(n=wp.template("community-events-attend-event-near"),a.html(n(e)),n=e.events.length?wp.template("community-events-event-list"):wp.template("community-events-no-upcoming-events"),s.html(n(e)),"user"===t&&wp.a11y.speak(d(l("City updated. Listing events near %s."),e.location.description),"assertive"),o["#community-events-location-message"]=!0,o[".community-events-toggle-location"]=!0,o[".community-events-results"]=!0):e.unknownCity?(n=wp.template("community-events-could-not-locate"),c(".community-events-could-not-locate").html(n(e)),wp.a11y.speak(d(l("We couldn\u2019t locate %s. Please try another nearby city. For example: Kansas City; Springfield; Portland."),e.unknownCity)),o[".community-events-errors"]=!0,o[".community-events-could-not-locate"]=!0):e.error&&"user"===t?(wp.a11y.speak(l("An error occurred. Please try again.")),o[".community-events-errors"]=!0,o[".community-events-error-occurred"]=!0):(a.text(l("Enter your closest city to find nearby events.")),o["#community-events-location-message"]=!0,o[".community-events-toggle-location"]=!0),_.each(o,function(e,t){c(t).attr("aria-hidden",!e)}),i.attr("aria-expanded",o[".community-events-toggle-location"]),e.location&&(e.location.ip||e.location.latitude)?(p.toggleLocationForm("hide"),"user"===t&&i.trigger("focus")):p.toggleLocationForm("show")},populateDynamicEventFields:function(e,o){e=JSON.parse(JSON.stringify(e));return c.each(e,function(e,t){var n=p.getTimeZone(1e3*t.start_unix_timestamp);t.user_formatted_date=p.getFormattedDate(1e3*t.start_unix_timestamp,1e3*t.end_unix_timestamp,n),t.user_formatted_time=s(o,1e3*t.start_unix_timestamp,n),t.timeZoneAbbreviation=p.getTimeZoneAbbreviation(1e3*t.start_unix_timestamp)}),e},getTimeZone:function(e){var t=Intl.DateTimeFormat().resolvedOptions().timeZone;return t=void 0===t?p.getFlippedTimeZoneOffset(e):t},getFlippedTimeZoneOffset:function(e){return-1*new Date(e).getTimezoneOffset()},getTimeZoneAbbreviation:function(e){var t,n=new Date(e).toLocaleTimeString(void 0,{timeZoneName:"short"}).split(" ");return void 0===(t=3===n.length?n[2]:t)&&(n=p.getFlippedTimeZoneOffset(e),e=-1===Math.sign(n)?"":"+",t=u("GMT","Events widget offset prefix")+e+n/60),t},getFormattedDate:function(e,t,n){var o=l("l, M j, Y"),i=l("%1$s %2$d\u2013%3$d, %4$d"),a=l("%1$s %2$d \u2013 %3$s %4$d, %5$d"),i=t&&m("Y-m-d",e)!==m("Y-m-d",t)?m("Y-m",e)===m("Y-m",t)?d(i,s(u("F","upcoming events month format"),e,n),s(u("j","upcoming events day format"),e,n),s(u("j","upcoming events day format"),t,n),s(u("Y","upcoming events year format"),t,n)):d(a,s(u("F","upcoming events month format"),e,n),s(u("j","upcoming events day format"),e,n),s(u("F","upcoming events month format"),t,n),s(u("j","upcoming events day format"),t,n),s(u("Y","upcoming events year format"),t,n)):s(o,e,n);return i}};c("#dashboard_primary").is(":visible")?p.init():c(document).on("postbox-toggled",function(e,t){t=c(t);"dashboard_primary"===t.attr("id")&&t.is(":visible")&&p.init()})}),window.communityEventsData.l10n=window.communityEventsData.l10n||{enter_closest_city:"",error_occurred_please_try_again:"",attend_event_near_generic:"",could_not_locate_city:"",city_updated:""},window.communityEventsData.l10n=window.wp.deprecateL10nObject("communityEventsData.l10n",window.communityEventsData.l10n,"5.6.0");