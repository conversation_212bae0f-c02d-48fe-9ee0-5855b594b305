/*! This file is auto-generated */
window.wp=window.wp||{},wp.svgPainter=function(e,i,n){"use strict";var t,o,a,m,r,s,c,u,l,f={},g=[];function p(){for(;l<256;)m=String.fromCharCode(l),s+=m,u[l]=l,c[l]=r.indexOf(m),++l}function d(n,t,e,a,i,o){for(var r,s=0,c=0,u="",l=0,f=(n=String(n)).length;c<f;){for(s=(s<<i)+(m=(m=n.charCodeAt(c))<256?e[m]:-1),l+=i;o<=l;)l-=o,u+=a.charAt(r=s>>l),s^=r<<l;++c}return!t&&0<l&&(u+=a.charAt(s<<o-l)),u}return e(function(){n.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#Image","1.1")&&(e(n.body).removeClass("no-svg").addClass("svg"),wp.svgPainter.init())}),r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s="",c=[256],u=[256],l=0,o={atob:function(n){var t;for(m||p(),n=n.replace(/[^A-Za-z0-9\+\/\=]/g,""),t=(n=String(n).split("=")).length;n[--t]=d(n[t],!0,c,s,6,8),0<t;);return n=n.join("")},btoa:function(n){return m||p(),(n=d(n,!1,u,r,8,6))+"====".slice(n.length%4||4)}},{init:function(){a=this,t=e("#adminmenu .wp-menu-image, #wpadminbar .ab-item"),this.setColors(),this.findElements(),this.paint()},setColors:function(n){(n=void 0===n&&void 0!==i._wpColorScheme?i._wpColorScheme:n)&&n.icons&&n.icons.base&&n.icons.current&&n.icons.focus&&(f=n.icons)},findElements:function(){t.each(function(){var n=e(this),t=n.css("background-image");t&&-1!=t.indexOf("data:image/svg+xml;base64")&&g.push(n)})},paint:function(){e.each(g,function(n,t){var e=t.parent().parent();e.hasClass("current")||e.hasClass("wp-has-current-submenu")?a.paintElement(t,"current"):(a.paintElement(t,"base"),e.on("mouseenter",function(){a.paintElement(t,"focus")}).on("mouseleave",function(){i.setTimeout(function(){a.paintElement(t,"base")},100)}))})},paintElement:function(n,t){var e,a;if(t&&f.hasOwnProperty(t)&&(t=f[t]).match(/^(#[0-9a-f]{3}|#[0-9a-f]{6})$/i)&&"none"!==(e=n.data("wp-ui-svg-"+t))){if(!e){if(!(a=n.css("background-image").match(/.+data:image\/svg\+xml;base64,([A-Za-z0-9\+\/\=]+)/))||!a[1])return void n.data("wp-ui-svg-"+t,"none");try{e=("atob"in i?i:o).atob(a[1])}catch(n){}if(!e)return void n.data("wp-ui-svg-"+t,"none");e=(e=(e=e.replace(/fill="(.+?)"/g,'fill="'+t+'"')).replace(/style="(.+?)"/g,'style="fill:'+t+'"')).replace(/fill:.*?;/g,"fill: "+t+";"),e=("btoa"in i?i:o).btoa(e),n.data("wp-ui-svg-"+t,e)}n.attr("style",'background-image: url("data:image/svg+xml;base64,'+e+'") !important;')}}}}(jQuery,window,document);