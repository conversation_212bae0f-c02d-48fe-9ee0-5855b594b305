#!/bin/bash

# Nadjib WordPress Project Stop Script

echo "🛑 Stopping Nadjib WordPress Project..."

# Stop PHP server if running
if [ -f .php-server.pid ]; then
    PHP_PID=$(cat .php-server.pid)
    if kill -0 $PHP_PID 2>/dev/null; then
        echo "Stopping PHP server (PID: $PHP_PID)..."
        kill $PHP_PID
        rm .php-server.pid
    else
        echo "PHP server not running"
        rm .php-server.pid
    fi
else
    echo "No PHP server PID file found"
fi

# Note about MariaDB
echo "📝 Note: MariaDB is left running (system service)"
echo "   To stop MariaDB: sudo systemctl stop mariadb"

echo "✅ PHP development server stopped!"
echo ""
echo "💡 To start again, run: ./start.sh"
echo "🐳 For Docker setup, use: docker-compose up -d"
echo ""
