<?php
/**
 * WordPress Sample Products Setup Script
 * This script will create sample products for the custom design store
 */

// Define WordPress root
define('WP_ROOT', __DIR__ . '/app/public');

// Include WordPress
require_once WP_ROOT . '/wp-config.php';
require_once WP_ROOT . '/wp-settings.php';

// Include required WordPress admin functions
require_once WP_ROOT . '/wp-admin/includes/plugin.php';
require_once WP_ROOT . '/wp-admin/includes/upgrade.php';

echo "🛍️  Setting up sample products...\n";

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo "❌ WooCommerce is not active. Please run setup-wordpress.php first.\n";
    exit(1);
}

// Sample products data
$products = [
    [
        'name' => 'Custom T-Shirt',
        'description' => 'Design your own custom t-shirt with our easy-to-use designer tool. Choose from various colors and add your personal touch.',
        'short_description' => 'Customizable t-shirt with design tool',
        'price' => 24.99,
        'sku' => 'CUSTOM-TSHIRT-001',
        'categories' => ['Apparel', 'Custom Design'],
        'tags' => ['custom', 't-shirt', 'design', 'personalized'],
        'featured' => true
    ],
    [
        'name' => 'Personalized Mug',
        'description' => 'Create a unique mug with your own design, text, or images. Perfect for gifts or personal use.',
        'short_description' => 'Design your own custom mug',
        'price' => 15.99,
        'sku' => 'CUSTOM-MUG-001',
        'categories' => ['Home & Living', 'Custom Design'],
        'tags' => ['custom', 'mug', 'personalized', 'gift'],
        'featured' => true
    ],
    [
        'name' => 'Custom Phone Case',
        'description' => 'Protect your phone in style with a custom-designed phone case. Compatible with most popular phone models.',
        'short_description' => 'Personalized phone case protection',
        'price' => 19.99,
        'sku' => 'CUSTOM-CASE-001',
        'categories' => ['Accessories', 'Custom Design'],
        'tags' => ['phone case', 'custom', 'protection', 'design'],
        'featured' => false
    ],
    [
        'name' => 'Design Your Own Poster',
        'description' => 'Create stunning custom posters for your home, office, or events. High-quality printing on premium paper.',
        'short_description' => 'Custom poster design and printing',
        'price' => 29.99,
        'sku' => 'CUSTOM-POSTER-001',
        'categories' => ['Art & Prints', 'Custom Design'],
        'tags' => ['poster', 'custom', 'print', 'art', 'design'],
        'featured' => false
    ],
    [
        'name' => 'Personalized Tote Bag',
        'description' => 'Eco-friendly tote bag that you can customize with your own design. Perfect for shopping or daily use.',
        'short_description' => 'Eco-friendly customizable tote bag',
        'price' => 18.99,
        'sku' => 'CUSTOM-TOTE-001',
        'categories' => ['Accessories', 'Eco-Friendly'],
        'tags' => ['tote bag', 'eco-friendly', 'custom', 'reusable'],
        'featured' => true
    ]
];

// Create product categories
$categories_created = [];
foreach ($products as $product_data) {
    foreach ($product_data['categories'] as $cat_name) {
        if (!isset($categories_created[$cat_name])) {
            $term = wp_insert_term($cat_name, 'product_cat');
            if (!is_wp_error($term)) {
                $categories_created[$cat_name] = $term['term_id'];
                echo "✅ Created category: $cat_name\n";
            }
        }
    }
}

// Create products
foreach ($products as $product_data) {
    // Check if product already exists
    $existing = get_page_by_title($product_data['name'], OBJECT, 'product');
    if ($existing) {
        echo "⚠️  Product '{$product_data['name']}' already exists, skipping...\n";
        continue;
    }
    
    // Create the product
    $product = new WC_Product_Simple();
    
    $product->set_name($product_data['name']);
    $product->set_description($product_data['description']);
    $product->set_short_description($product_data['short_description']);
    $product->set_regular_price($product_data['price']);
    $product->set_sku($product_data['sku']);
    $product->set_manage_stock(false);
    $product->set_stock_status('instock');
    $product->set_catalog_visibility('visible');
    $product->set_featured($product_data['featured']);
    
    // Set categories
    $cat_ids = [];
    foreach ($product_data['categories'] as $cat_name) {
        if (isset($categories_created[$cat_name])) {
            $cat_ids[] = $categories_created[$cat_name];
        }
    }
    $product->set_category_ids($cat_ids);
    
    // Save the product
    $product_id = $product->save();
    
    if ($product_id) {
        // Add tags
        wp_set_object_terms($product_id, $product_data['tags'], 'product_tag');
        
        // Add custom meta for Fancy Product Designer
        update_post_meta($product_id, '_fpd_enabled', 'yes');
        update_post_meta($product_id, '_fpd_product_designer_visibility', 'visible');
        
        echo "✅ Created product: {$product_data['name']} (ID: $product_id)\n";
    } else {
        echo "❌ Failed to create product: {$product_data['name']}\n";
    }
}

// Create a sample page with product showcase
$page_content = '
<!-- wp:heading {"level":1} -->
<h1>Welcome to Nadjib Custom Products</h1>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>Design and create your perfect custom products with our easy-to-use design tools. From t-shirts to mugs, phone cases to posters - make it uniquely yours!</p>
<!-- /wp:paragraph -->

<!-- wp:heading {"level":2} -->
<h2>Featured Products</h2>
<!-- /wp:heading -->

<!-- wp:woocommerce/product-collection {"query":{"perPage":6,"pages":0,"offset":0,"postType":"product","order":"desc","orderBy":"date","search":"","exclude":[],"inherit":false,"taxQuery":{},"isProductCollectionBlock":true,"featured":true,"woocommerceOnSale":false,"woocommerceStockStatus":["instock","onbackorder"],"woocommerceAttributes":[],"woocommerceHandPickedProducts":[]},"tagName":"div","displayLayout":{"type":"flex","columns":3}} -->
<div class="wp-block-woocommerce-product-collection">
<!-- wp:woocommerce/product-template -->
<!-- wp:woocommerce/product-image {"imageSizing":"thumbnail","isDescendentOfQueryLoop":true} /-->

<!-- wp:post-title {"textAlign":"center","level":3,"isLink":true,"style":{"spacing":{"margin":{"bottom":"0.75rem","top":"0"}}},"fontSize":"medium","__woocommerceNamespace":"woocommerce/product-collection/product-title"} /-->

<!-- wp:woocommerce/product-price {"isDescendentOfQueryLoop":true,"textAlign":"center","style":{"spacing":{"margin":{"bottom":"0.75rem"}}}} /-->

<!-- wp:woocommerce/product-button {"textAlign":"center","isDescendentOfQueryLoop":true,"style":{"spacing":{"margin":{"bottom":"0.75rem"}}}} /-->
<!-- /wp:woocommerce/product-template -->
</div>
<!-- /wp:woocommerce/product-collection -->

<!-- wp:heading {"level":2} -->
<h2>How It Works</h2>
<!-- /wp:heading -->

<!-- wp:columns -->
<div class="wp-block-columns">
<!-- wp:column -->
<div class="wp-block-column">
<!-- wp:heading {"level":3,"textAlign":"center"} -->
<h3 class="has-text-align-center">1. Choose Product</h3>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center"} -->
<p class="has-text-align-center">Select from our range of customizable products</p>
<!-- /wp:paragraph -->
</div>
<!-- /wp:column -->

<!-- wp:column -->
<div class="wp-block-column">
<!-- wp:heading {"level":3,"textAlign":"center"} -->
<h3 class="has-text-align-center">2. Design</h3>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center"} -->
<p class="has-text-align-center">Use our design tool to create your perfect design</p>
<!-- /wp:paragraph -->
</div>
<!-- /wp:column -->

<!-- wp:column -->
<div class="wp-block-column">
<!-- wp:heading {"level":3,"textAlign":"center"} -->
<h3 class="has-text-align-center">3. Order</h3>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center"} -->
<p class="has-text-align-center">Place your order and we\'ll create your custom product</p>
<!-- /wp:paragraph -->
</div>
<!-- /wp:column -->
</div>
<!-- /wp:columns -->
';

// Create or update homepage
$homepage = get_page_by_title('Home');
if (!$homepage) {
    $homepage_id = wp_insert_post([
        'post_title' => 'Home',
        'post_content' => $page_content,
        'post_status' => 'publish',
        'post_type' => 'page'
    ]);
    
    // Set as homepage
    update_option('show_on_front', 'page');
    update_option('page_on_front', $homepage_id);
    
    echo "✅ Created homepage with product showcase\n";
}

echo "\n🎉 Sample products setup completed!\n";
echo "📦 Created " . count($products) . " sample products\n";
echo "🏠 Homepage created with product showcase\n";
echo "🛒 Your store is ready for customization!\n";
?>
