<?php
/**
 * WordPress Automatic Setup Script
 * This script will automatically configure WordPress with all plugins and settings
 */

// Define WordPress root
define('WP_ROOT', __DIR__ . '/app/public');

// Include WordPress
require_once WP_ROOT . '/wp-config.php';
require_once WP_ROOT . '/wp-settings.php';

// Include required WordPress admin functions
require_once WP_ROOT . '/wp-admin/includes/plugin.php';
require_once WP_ROOT . '/wp-admin/includes/upgrade.php';
require_once WP_ROOT . '/wp-admin/includes/file.php';

echo "🚀 Starting WordPress Automatic Setup...\n";

// 1. Install WordPress if not already installed
if (!is_blog_installed()) {
    echo "📦 Installing WordPress...\n";
    
    $site_title = 'Nadjib Custom Products';
    $admin_user = 'admin';
    $admin_password = 'admin123';
    $admin_email = '<EMAIL>';
    
    $result = wp_install($site_title, $admin_user, $admin_email, true, '', $admin_password);
    
    if (is_wp_error($result)) {
        echo "❌ WordPress installation failed: " . $result->get_error_message() . "\n";
        exit(1);
    }
    
    echo "✅ WordPress installed successfully!\n";
    echo "   Site: $site_title\n";
    echo "   Admin User: $admin_user\n";
    echo "   Admin Password: $admin_password\n";
    echo "   Admin Email: $admin_email\n\n";
} else {
    echo "✅ WordPress already installed\n\n";
}

// 2. Activate plugins
echo "🔌 Activating plugins...\n";

$plugins_to_activate = [
    'woocommerce/woocommerce.php',
    'elementor/elementor.php',
    'pro-elements/pro-elements.php',
    'essential-addons-for-elementor-lite/essential_adons_elementor.php',
    'astra-sites/astra-sites.php',
    'fancy-product-designer/fancy-product-designer.php',
    'instantio/instantio.php',
    'easy-login-woocommerce/xoo-el-main.php',
    'variation-swatches-woo/variation-swatches-woo.php',
    'woo-cart-abandonment-recovery/woo-cart-abandonment-recovery.php',
    'wpforms-lite/wpforms.php',
    'visibility-logic-elementor/conditional.php',
    'query-monitor/query-monitor.php'
];

foreach ($plugins_to_activate as $plugin) {
    if (!is_plugin_active($plugin)) {
        $result = activate_plugin($plugin);
        if (is_wp_error($result)) {
            echo "⚠️  Failed to activate $plugin: " . $result->get_error_message() . "\n";
        } else {
            echo "✅ Activated: $plugin\n";
        }
    } else {
        echo "✅ Already active: $plugin\n";
    }
}

echo "\n";

// 3. Set up theme
echo "🎨 Setting up theme...\n";

$theme = 'astra-child';
if (wp_get_theme($theme)->exists()) {
    switch_theme($theme);
    echo "✅ Activated theme: $theme\n";
} else {
    echo "⚠️  Theme $theme not found, using default\n";
}

// 4. Configure basic WordPress settings
echo "⚙️  Configuring WordPress settings...\n";

update_option('blogname', 'Nadjib Custom Products');
update_option('blogdescription', 'Design Your Perfect Product');
update_option('start_of_week', 1);
update_option('use_balanceTags', 0);
update_option('use_smilies', 1);
update_option('require_name_email', 1);
update_option('comments_notify', 1);
update_option('posts_per_rss', 10);
update_option('rss_use_excerpt', 0);
update_option('permalink_structure', '/%postname%/');

// Set timezone
update_option('timezone_string', 'UTC');

echo "✅ WordPress settings configured\n\n";

// 5. Configure WooCommerce basic settings
if (class_exists('WooCommerce')) {
    echo "🛒 Configuring WooCommerce...\n";
    
    // Basic WooCommerce settings
    update_option('woocommerce_store_address', '123 Design Street');
    update_option('woocommerce_store_city', 'Creative City');
    update_option('woocommerce_default_country', 'US:CA');
    update_option('woocommerce_store_postcode', '90210');
    update_option('woocommerce_currency', 'USD');
    update_option('woocommerce_product_type', 'both');
    update_option('woocommerce_allow_tracking', 'no');
    
    // Enable registration on checkout
    update_option('woocommerce_enable_signup_and_login_from_checkout', 'yes');
    update_option('woocommerce_enable_myaccount_registration', 'yes');
    
    // Set up pages
    $pages = [
        'woocommerce_shop_page_id' => 'Shop',
        'woocommerce_cart_page_id' => 'Cart',
        'woocommerce_checkout_page_id' => 'Checkout',
        'woocommerce_myaccount_page_id' => 'My Account'
    ];
    
    foreach ($pages as $option => $title) {
        $page = get_page_by_title($title);
        if (!$page) {
            $page_id = wp_insert_post([
                'post_title' => $title,
                'post_content' => '',
                'post_status' => 'publish',
                'post_type' => 'page'
            ]);
            update_option($option, $page_id);
        }
    }
    
    echo "✅ WooCommerce configured\n\n";
}

echo "🎉 WordPress setup completed successfully!\n";
echo "🌐 Your site is ready at: http://localhost:8080\n";
echo "🔐 Admin login: http://localhost:8080/wp-admin\n";
echo "   Username: admin\n";
echo "   Password: admin123\n\n";

echo "🛒 Features ready:\n";
echo "   ✅ WooCommerce e-commerce\n";
echo "   ✅ Fancy Product Designer\n";
echo "   ✅ Elementor page builder\n";
echo "   ✅ Astra theme\n";
echo "   ✅ All plugins activated\n\n";

echo "🚀 You can now start using your custom product design website!\n";
?>
