# Nadjib WordPress E-commerce Project

A modern WordPress-based e-commerce website with product customization capabilities, built with Docker for easy local development.

## 🚀 Quick Start

1. **Clone and navigate to the project:**
   ```bash
   cd /path/to/nadjib
   ```

2. **Make scripts executable:**
   ```bash
   chmod +x *.sh
   ```

3. **Start the project:**
   ```bash
   ./start.sh
   ```

4. **Access your site:**
   - **WordPress Site:** http://localhost:8080
   - **phpMyAdmin:** http://localhost:8081

## 🛠️ Project Features

### Core Technology Stack
- **WordPress** (Latest version)
- **WooCommerce** (v9.0.2) - E-commerce platform
- **Astra Theme** with child theme
- **Elementor** page builder with Pro Elements
- **Docker** containerized environment

### Key Plugins & Functionality
- **🎨 Fancy Product Designer** - Custom product design tool
- **🛒 Instantio** - Quick checkout & cart optimization
- **📊 Essential Addons for Elementor** - 90+ additional widgets
- **🔐 Easy Login WooCommerce** - Streamlined authentication
- **📧 Cart Abandonment Recovery** - Email recovery system
- **🏗️ Astra Sites** - AI-powered website builder

## 📋 Management Scripts

- `./start.sh` - Start all services
- `./stop.sh` - Stop all services  
- `./restart.sh` - Restart all services
- `./logs.sh [service]` - View logs (nginx, php, mysql, or all)

## 🗄️ Database Access

**Credentials:**
- Host: `mysql` (container) or `localhost:3306` (from host)
- Database: `local`
- Username: `root`
- Password: `root`

## 🐳 Docker Services

- **nginx** - Web server (port 8080)
- **php** - PHP-FPM 8.1
- **mysql** - MySQL 8.0 database
- **phpmyadmin** - Database management (port 8081)

## 📁 Project Structure

```
├── app/public/          # WordPress installation
├── conf/               # Server configurations
│   ├── nginx/         # Nginx configuration
│   ├── php/           # PHP-FPM configuration
│   └── mysql/         # MySQL configuration
├── logs/              # Application logs
├── docker-compose.yml # Docker services definition
└── *.sh              # Management scripts
```

## 🔧 Development

### File Permissions
The startup script automatically sets proper WordPress file permissions. If you encounter permission issues:

```bash
sudo chown -R $USER:$USER app/public
find app/public -type d -exec chmod 755 {} \;
find app/public -type f -exec chmod 644 {} \;
```

### Debugging
- WordPress debug mode is enabled in development
- Logs are available in the `logs/` directory
- Use `./logs.sh` to monitor real-time logs

### Custom Configuration
- Environment variables: `.env`
- WordPress config: `app/public/wp-config.php`
- Server configs: `conf/` directory

## 🚨 Troubleshooting

**Services won't start:**
```bash
docker-compose down -v  # Remove all containers and volumes
./start.sh              # Start fresh
```

**Permission errors:**
```bash
sudo chown -R $USER:$USER app/public
```

**Database connection issues:**
- Ensure MySQL container is running: `docker-compose ps`
- Check database credentials in `wp-config.php`
- Verify `.env` file settings

## 📞 Support

This is a custom WordPress e-commerce setup optimized for product customization businesses. The project includes all necessary configurations for local development and can be easily deployed to production environments.

For WordPress-specific issues, refer to the [WordPress Documentation](https://wordpress.org/support/).
For WooCommerce issues, check the [WooCommerce Documentation](https://docs.woocommerce.com/).
