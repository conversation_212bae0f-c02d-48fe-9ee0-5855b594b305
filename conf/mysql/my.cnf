[mysqld]
skip-name-resolve

datadir = /var/lib/mysql
port = 3306
bind-address = 0.0.0.0
socket = /var/run/mysqld/mysqld.sock

# Older PHP/client compatibility
character-set-server = utf8mb3
default_authentication_plugin = mysql_native_password

# Fine Tuning
performance_schema = off
max_allowed_packet = 16M
thread_stack = 192K
thread_cache_size = 8

# InnoDB
innodb_buffer_pool_size = 32M
innodb_log_file_size = 96M

[client]
socket = /var/run/mysqld/mysqld.sock
user = root
password = root
