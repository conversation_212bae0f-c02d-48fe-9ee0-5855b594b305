[PHP]
engine = On

; Maxes
max_execution_time = 1200
max_input_time = 600
max_input_vars = 4000
memory_limit = 256M
post_max_size = 1000M
upload_max_filesize = 1000M
max_file_uploads = 20
output_buffering = 4096

; Error Handling
error_reporting = E_ALL & ~E_DEPRECATED
error_log = /var/log/php/error.log
log_errors = On
display_errors = On
display_startup_errors = On
ignore_repeated_errors = Off
ignore_repeated_source = Off
report_memleaks = On
html_errors = On

; Other
short_open_tag = On
asp_tags = Off
precision = 14
y2k_compliance = On
zlib.output_compression = Off
implicit_flush = Off
unserialize_callback_func =
serialize_precision = 17
allow_call_time_pass_reference = Off
safe_mode = Off
safe_mode_gid = Off
safe_mode_include_dir =
safe_mode_exec_dir =
safe_mode_allowed_env_vars = PHP_
safe_mode_protected_env_vars = LD_LIBRARY_PATH
disable_functions =
disable_classes =

; File Uploads
file_uploads = On
upload_tmp_dir = /tmp

; Sessions
session.save_handler = files
session.save_path = /tmp
session.use_cookies = 1
session.use_only_cookies = 1
session.name = PHPSESSID
session.auto_start = 0
session.cookie_lifetime = 0
session.cookie_path = /
session.cookie_domain =
session.cookie_httponly =
session.serialize_handler = php
session.gc_probability = 1
session.gc_divisor = 1000
session.gc_maxlifetime = 1440
session.bug_compat_42 = Off
session.bug_compat_warn = Off
session.referer_check =
session.entropy_length = 0
session.entropy_file =
session.cache_limiter = nocache
session.cache_expire = 180
session.use_trans_sid = 0
session.hash_function = 0
session.hash_bits_per_character = 5

; Date
date.timezone = UTC

; MySQL
mysql.allow_local_infile = On
mysql.allow_persistent = On
mysql.cache_size = 2000
mysql.max_persistent = -1
mysql.max_links = -1
mysql.default_port =
mysql.default_socket =
mysql.default_host =
mysql.default_user =
mysql.default_password =
mysql.connect_timeout = 60
mysql.trace_mode = Off

; MySQLi
mysqli.max_persistent = -1
mysqli.allow_persistent = On
mysqli.max_links = -1
mysqli.cache_size = 2000
mysqli.default_port = 3306
mysqli.default_socket =
mysqli.default_host =
mysqli.default_user =
mysqli.default_pw =
mysqli.reconnect = Off
