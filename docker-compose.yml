version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: nadjib_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: local
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress
    volumes:
      - mysql_data:/var/lib/mysql
      - ./conf/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf
    ports:
      - "3306:3306"
    networks:
      - nadjib_network

  # PHP-FPM
  php:
    image: php:8.1-fpm
    container_name: nadjib_php
    restart: unless-stopped
    volumes:
      - ./app/public:/var/www/html
      - ./conf/php/php.ini:/usr/local/etc/php/php.ini
      - ./conf/php/php-fpm.conf:/usr/local/etc/php-fpm.conf
      - ./logs/php:/var/log/php
    depends_on:
      - mysql
    networks:
      - nadjib_network
    build:
      context: .
      dockerfile: Dockerfile.php

  # Nginx Web Server
  nginx:
    image: nginx:alpine
    container_name: nadjib_nginx
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./app/public:/var/www/html
      - ./conf/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./conf/nginx/site.conf:/etc/nginx/conf.d/default.conf
      - ./conf/nginx/includes:/etc/nginx/includes
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - php
    networks:
      - nadjib_network

  # phpMyAdmin (optional, for database management)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: nadjib_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - nadjib_network

volumes:
  mysql_data:

networks:
  nadjib_network:
    driver: bridge
