#!/bin/bash

# Nadjib WordPress Project Logs Script

echo "📋 Nadjib WordPress Project Logs"
echo "================================"
echo ""

if [ "$1" = "php" ] || [ -z "$1" ]; then
    echo "📄 PHP Development Server Logs:"
    if [ -f logs/php/php-server.log ]; then
        tail -f logs/php/php-server.log
    else
        echo "No PHP server logs found. Is the server running?"
    fi
elif [ "$1" = "mariadb" ]; then
    echo "📄 MariaDB Logs:"
    sudo journalctl -u mariadb -f
elif [ "$1" = "docker" ]; then
    echo "📄 Docker Service Logs:"
    if command -v docker-compose &> /dev/null; then
        docker-compose logs -f
    else
        echo "Docker Compose not available. Use ./start.sh for PHP server mode."
    fi
else
    echo "Usage: ./logs.sh [service]"
    echo ""
    echo "Available services:"
    echo "  php      - PHP development server logs (default)"
    echo "  mariadb  - MariaDB database logs"
    echo "  docker   - Docker services logs (if available)"
    echo ""
    echo "Example: ./logs.sh php"
fi
