#!/bin/bash

# Complete WordPress Setup Script
# This script will fully configure WordPress with all plugins, theme, and sample content

echo "🚀 Starting Complete WordPress Setup..."
echo "======================================"
echo ""

# Check if PHP server is running
if ! pgrep -f "php -S localhost:8080" > /dev/null; then
    echo "⚠️  PHP server is not running. Starting it first..."
    ./start.sh
    sleep 5
fi

echo "📋 Step 1: Installing and configuring WordPress..."
php setup-wordpress.php

if [ $? -ne 0 ]; then
    echo "❌ WordPress setup failed!"
    exit 1
fi

echo ""
echo "📋 Step 2: Creating sample products..."
php setup-products.php

if [ $? -ne 0 ]; then
    echo "❌ Products setup failed!"
    exit 1
fi

echo ""
echo "📋 Step 3: Final configuration..."

# Set proper file permissions
echo "📁 Setting file permissions..."
find app/public -type d -exec chmod 755 {} \;
find app/public -type f -exec chmod 644 {} \;

# Create uploads directory if it doesn't exist
mkdir -p app/public/wp-content/uploads
chmod 755 app/public/wp-content/uploads

echo "✅ File permissions set"

echo ""
echo "🎉 SETUP COMPLETE! 🎉"
echo "===================="
echo ""
echo "🌐 Your WordPress site: http://localhost:8080"
echo "🔐 Admin panel: http://localhost:8080/wp-admin"
echo ""
echo "👤 Admin Credentials:"
echo "   Username: admin"
echo "   Password: admin123"
echo ""
echo "🛒 Store Features:"
echo "   ✅ WooCommerce e-commerce platform"
echo "   ✅ Fancy Product Designer (custom design tool)"
echo "   ✅ Elementor page builder"
echo "   ✅ Astra theme with child theme"
echo "   ✅ 5 sample customizable products"
echo "   ✅ Quick checkout optimization"
echo "   ✅ Cart abandonment recovery"
echo "   ✅ Login/signup popups"
echo ""
echo "📦 Sample Products Created:"
echo "   • Custom T-Shirt ($24.99)"
echo "   • Personalized Mug ($15.99)"
echo "   • Custom Phone Case ($19.99)"
echo "   • Design Your Own Poster ($29.99)"
echo "   • Personalized Tote Bag ($18.99)"
echo ""
echo "🎨 Design Tools Ready:"
echo "   • Fancy Product Designer for custom designs"
echo "   • Image upload and editing"
echo "   • Text customization"
echo "   • QR code generation"
echo "   • Layer management"
echo ""
echo "🛠️  Management Commands:"
echo "   ./stop.sh    - Stop the server"
echo "   ./restart.sh - Restart the server"
echo "   ./logs.sh    - View server logs"
echo ""
echo "🚀 Your custom product design website is ready!"
echo "   Visit http://localhost:8080 to see your store"
echo "   Visit http://localhost:8080/wp-admin to manage your site"
echo ""
